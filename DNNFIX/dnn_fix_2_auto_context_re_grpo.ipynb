import os
import sys
dirpath = "/Users/<USER>/Downloads"
sys.path.append(dirpath)
from pylib.dnn_fix_v2 import *

old_yaml = 'suntianyu06_dsp_sty_ue_cross.yaml'
new_yaml = 'xiatian06_dsp_lps_sv_xt_context_re_grpo_v1 (1).yaml'

old_yaml = os.path.join(dirpath, old_yaml)
new_yaml = os.path.join(dirpath, new_yaml)
dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)
# print(dense_old)
dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)
# print(dense_new)
weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)

weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}
extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}
print()
print('粘贴到my_load_dense_func开头的origin_weight处')
print(weight_diff_change)
# print(extra_diff_change)

def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):
    # type==change，原来的小尺寸、现在变大的weight和它的原始shape
    origin_weight = {'QUEUE_SOFT/share_bottom_layer_0/w:0': (7821, 1024), 'rocket_upper_layer_2/w:0': (560, 128), 'share_bottom_layer_new_0/w:0': (13835, 1024)}
    ln_map = {"dcn_layer_0/LayerNorm/beta:0":"LayerNorm_35/beta:0","dcn_layer_0/LayerNorm/gamma:0":"LayerNorm_35/gamma:0","dcn_layer_1/LayerNorm/beta:0":"LayerNorm_36/beta:0","dcn_layer_1/LayerNorm/gamma:0":"LayerNorm_36/gamma:0"}
    ub_map = {"dcn_layer_0/u:0":"u_0:0","dcn_layer_1/u:0":"u_1:0","dcn_layer_0/b:0":"b_0:0","dcn_layer_1/b:0":"b_1:0"}
    v_map = {"dcn_layer_0/v:0":"v_0:0","dcn_layer_1/v:0":"v_1:0"}

    dcn_weight_1 = {'LayerNorm_35/beta:0': (13835, 1), 'LayerNorm_35/gamma:0': (13835, 1), 'LayerNorm_36/beta:0': (13835, 1), 'LayerNorm_36/gamma:0': (13835, 1), }
    dcn_weight_2 = {'b_0:0': (13835, 1), 'b_1:0': (13835, 1),  'u_0:0': (13835, 64), 'u_1:0': (13835, 64),}
    dcn_weight_3 = {'v_0:0': (64, 13835), 'v_1:0': (64, 13835)}

    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。
    def extra_reshape(weight, extra):
        exShape = list(weight.shape)
        exShape.append(-1)
        extra = extra[:weight.size].reshape(exShape)
        return extra
    
    def get_extra(extra):
        return extra.reshape([-1])
    
    print('my_load_dense_func')
    weight = None
    extra = None
    dense_variable_nums = len(tf_weight)
    
    # 第一层的权重的尺寸可能不一样，取决于输入
    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。
    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。

    # 这里是旧权重尺寸小(或无）的情况
    for var_name in list(tf_weight): 
        if var_name in origin_weight:
            origin_size = origin_weight[var_name][0]
            # 原来的 weight
            ori_weight = warmup_weight[var_name]
            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])
            # 新的 weight
            new_weight = tf_weight[var_name]
            new_extra = extra_reshape(new_weight, ps_extra[var_name])
            # 进行赋值
            new_weight[:origin_size, :] = ori_weight
            new_extra[:origin_size, :,:] = ori_extra
            # 回填
            warmup_weight[var_name]  = new_weight
            warmup_extra[var_name] = get_extra(new_extra)
            print("加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}".format(
                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))
        elif var_name in ln_map:
            old_name = ln_map[var_name]
            origin_size = dcn_weight_1[old_name][0]
            # 原来的 weight
            ori_weight = warmup_weight[old_name]
            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])
            # 新的 weight
            new_weight = tf_weight[var_name]
            new_extra = extra_reshape(new_weight, ps_extra[var_name])
            # 进行赋值
            new_weight[:origin_size, :] = ori_weight
            new_extra[:origin_size, :,:] = ori_extra
            # 回填
            warmup_weight[var_name]  = new_weight
            warmup_extra[var_name] = get_extra(new_extra)
            print("加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}".format(
                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))
        elif var_name in ub_map:
            old_name = ub_map[var_name]
            origin_size = dcn_weight_2[old_name][0]
            # 原来的 weight
            ori_weight = warmup_weight[old_name]
            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])
            # 新的 weight
            new_weight = tf_weight[var_name]
            new_extra = extra_reshape(new_weight, ps_extra[var_name])
            # 进行赋值
            new_weight[:origin_size, :] = ori_weight
            new_extra[:origin_size, :,:] = ori_extra
            # 回填
            warmup_weight[var_name]  = new_weight
            warmup_extra[var_name] = get_extra(new_extra)
            print("加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}".format(
                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))
        elif var_name in v_map:
            old_name = v_map[var_name]
            origin_size = dcn_weight_3[old_name][1]
            # 原来的 weight
            ori_weight = warmup_weight[old_name]
            ori_extra = extra_reshape(ori_weight, warmup_extra[old_name])
            # 新的 weight
            new_weight = tf_weight[var_name]
            new_extra = extra_reshape(new_weight, ps_extra[var_name])
            # 进行赋值
            new_weight[:, :origin_size] = ori_weight
            new_extra[:, :origin_size, :] = ori_extra
            # 回填
            warmup_weight[var_name]  = new_weight
            warmup_extra[var_name] = get_extra(new_extra)
            print("加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}".format(
                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))
        elif var_name not in warmup_weight:
            print("加载的 dense variable({}) 不存在，其值全新初始化".format(var_name))
            warmup_weight[var_name] = tf_weight[var_name]
            warmup_extra[var_name] = ps_extra[var_name]

            
    if len(warmup_weight) > 0:
        for var_name in list(warmup_weight):
            if var_name not in tf_weight:
                print("加载的 dense variable({}) 在运行时不存在，其值被忽略。".format(var_name))  # noqa
                try:
                    del warmup_weight[var_name]
                    del warmup_extra[var_name]
                except KeyError as e:
#                     if var_name not in warmup_weight:
#                         print(f"{var_name} not in warmup_weight")
#                     else:
#                         print(f"{var_name} not in warmup_extra")
                    pass
            elif warmup_weight[var_name].size != tf_weight[var_name].size:
                # 这里可以添加旧权重尺寸大的处理逻辑
                print("加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa
                try:
                    del warmup_weight[var_name]
                    del warmup_extra[var_name]
                except KeyError as e:
                    if var_name not in warmup_weight:
                        print(f"{var_name} not in warmup_weight")
                    else:
                        print(f"{var_name} not in warmup_extra")
        weight = warmup_weight
        extra = warmup_extra
    else:
        weight = tf_weight
        extra = ps_extra

    assert len(weight) == dense_variable_nums
    assert len(extra) == dense_variable_nums

    print('end my_load_dense_func')
    return weight, extra

res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)
dense_change = get_dense_table_from_dict(res_weight, res_extra)
diff_dense_table(dense_new, dense_change)