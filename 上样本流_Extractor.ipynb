index = 656
for l in """remap_slot=656, dim=16, map_slot=656, size=0, slot=0, name=ExtractCombineSparseInnerOrderCvr, category=ad, field=498, feature_size=1000001, topic_id=0
remap_slot=667, dim=16, map_slot=667, size=0, slot=0, name=ExtractCombineRankIndexUescorePctrNoPrefix, category=ad, field=499, feature_size=1001, topic_id=0
remap_slot=668, dim=16, map_slot=668, size=0, slot=0, name=ExtractCombineRankIndexUescorePlvtrNoPrefix, category=ad, field=500, feature_size=1001, topic_id=0
remap_slot=669, dim=16, map_slot=669, size=0, slot=0, name=ExtractCombineRankIndexUescorePsvrNoPrefix, category=ad, field=501, feature_size=1001, topic_id=0
remap_slot=670, dim=16, map_slot=670, size=0, slot=0, name=ExtractCombineRankIndexUescorePvtrNoPrefix, category=ad, field=502, feature_size=1001, topic_id=0
remap_slot=671, dim=16, map_slot=671, size=0, slot=0, name=ExtractCombineRankIndexUescorePwatchtimeNoPrefix, category=ad, field=503, feature_size=1001, topic_id=0
remap_slot=672, dim=16, map_slot=672, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtdNoPrefix, category=ad, field=504, feature_size=1001, topic_id=0
remap_slot=673, dim=16, map_slot=673, size=0, slot=0, name=ExtractCombineRankIndexUescorePcprNoPrefix, category=ad, field=505, feature_size=1001, topic_id=0
remap_slot=674, dim=16, map_slot=674, size=0, slot=0, name=ExtractCombineRankIndexUescorePltrNoPrefix, category=ad, field=506, feature_size=1001, topic_id=0
remap_slot=675, dim=16, map_slot=675, size=0, slot=0, name=ExtractCombineRankIndexUescorePwtrNoPrefix, category=ad, field=507, feature_size=1001, topic_id=0
remap_slot=676, dim=16, map_slot=676, size=0, slot=0, name=ExtractCombineRankIndexUescorePftrNoPrefix, category=ad, field=508, feature_size=1001, topic_id=0
remap_slot=677, dim=16, map_slot=677, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmtrNoPrefix, category=ad, field=509, feature_size=1001, topic_id=0
remap_slot=678, dim=16, map_slot=678, size=0, slot=0, name=ExtractCombineRankIndexUescorePhtrNoPrefix, category=ad, field=510, feature_size=1001, topic_id=0
remap_slot=679, dim=16, map_slot=679, size=0, slot=0, name=ExtractCombineRankIndexUescorePclickLiveNoPrefix, category=ad, field=511, feature_size=1001, topic_id=0
remap_slot=680, dim=16, map_slot=680, size=0, slot=0, name=ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix, category=ad, field=512, feature_size=1001, topic_id=0
remap_slot=681, dim=16, map_slot=681, size=0, slot=0, name=ExtractCombineRankIndexUescorePptrNoPrefix, category=ad, field=513, feature_size=1001, topic_id=0
remap_slot=682, dim=16, map_slot=682, size=0, slot=0, name=ExtractCombineRankIndexUescorePepstrNoPrefix, category=ad, field=514, feature_size=1001, topic_id=0
remap_slot=683, dim=16, map_slot=683, size=0, slot=0, name=ExtractCombineRankIndexUescorePlstrNoPrefix, category=ad, field=515, feature_size=1001, topic_id=0
remap_slot=684, dim=16, map_slot=684, size=0, slot=0, name=ExtractCombineRankIndexUescorePetcmNoPrefix, category=ad, field=516, feature_size=1001, topic_id=0
remap_slot=685, dim=16, map_slot=685, size=0, slot=0, name=ExtractCombineRankIndexUescorePcmefNoPrefix, category=ad, field=517, feature_size=1001, topic_id=0
remap_slot=686, dim=16, map_slot=686, size=0, slot=0, name=ExtractCombineRankIndexInnerOrderCvrNoPrefix, category=ad, field=518, feature_size=1001, topic_id=0
remap_slot=687, dim=16, map_slot=687, size=0, slot=0, name=ExtractCombineRankIndexLiveAudienceCtrNoPrefix, category=ad, field=519, feature_size=1001, topic_id=0""".splitlines():
    print(l.replace(str(index), str(index+1)))
    index += 1

s = 0
for i in """46086
100273
51821
170758
73150
87936
109794
98695
108904
121827
119712
202007
243126
94502
210814
88856
75082
116866
206956
82100
156421
201639
63836
174275
67313
68248
94125
64698
336153""".splitlines():
    s += int(i)
s

# 特征size都是1001
# combine
combine = """ExtractCombineRankIndexUescorePctrNoPrefix
ExtractCombineRankIndexUescorePlvtrNoPrefix
ExtractCombineRankIndexUescorePsvrNoPrefix
ExtractCombineRankIndexUescorePvtrNoPrefix
ExtractCombineRankIndexUescorePwatchtimeNoPrefix
ExtractCombineRankIndexUescorePwtdNoPrefix
ExtractCombineRankIndexUescorePcprNoPrefix
ExtractCombineRankIndexUescorePltrNoPrefix
ExtractCombineRankIndexUescorePwtrNoPrefix
ExtractCombineRankIndexUescorePftrNoPrefix
ExtractCombineRankIndexUescorePcmtrNoPrefix
ExtractCombineRankIndexUescorePhtrNoPrefix
ExtractCombineRankIndexUescorePclickLiveNoPrefix
ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix
ExtractCombineRankIndexUescorePptrNoPrefix
ExtractCombineRankIndexUescorePepstrNoPrefix
ExtractCombineRankIndexUescorePlstrNoPrefix
ExtractCombineRankIndexUescorePetcmNoPrefix
ExtractCombineRankIndexUescorePcmefNoPrefix
ExtractCombineRankIndexInnerOrderCvrNoPrefix""".splitlines()
user = """ExtractUserRankInnerOrderNum
ExtractUserRankUescoreNum""".splitlines()
len(combine) + len(user)
print(',\n'.join(combine + user))

for i in combine:
    print(f"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true")
for i in user:
    print(f"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true")

for i in combine:
    print(f"{i} array<bigint>,")
for i in user:
    print(f"{i} array<bigint>,")

start_slot = 303

slot = start_slot
for i in combine:
    size = 1001
    category = 'combine'
    # concat_len = 50 * dim
    print(f"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0")
    slot += 1

start_slot = 301
slot = start_slot
for i in user:
    size = 1001
    category = 'user'
    print(f"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0")
    slot += 1

remap_start_slot = 667

slot = remap_start_slot
for i in combine:
    size = 1001
    dim=16
    category = 'combine'
    print(f"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0")
    slot += 1

remap_start_slot = 665
slot = remap_start_slot
for i in user:
    size = 1001
    dim=16
    category = 'user'
    print(f"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0")
    slot += 1