import pandas as pd
df = pd.read_csv('/Users/<USER>/Downloads/colossus_rs_item_id.csv')
for idx,row in df.iterrows():
    row2 = eval(row[2])
    row3 = eval(row[3])
    same = [row2[i] == row3[i] for i in range(min(len(row2), len(row3)))]
    print(row[0], f"online {len(row2)} vs. offline {len(row3)}, same{sum(same)}")
    print(same)
    print(row[2])
    print(row[3])
    

import pandas as pd
df = pd.read_csv('/Users/<USER>/Downloads/soft_topk_values.csv')
for idx,row in df.iterrows():
    row2 = eval(row[2])
    row3 = eval(row[3])
    same = [row2[i] == row3[i] for i in range(min(len(row2), len(row3)))]
    print(row[0], f"online {len(row2)} vs. offline {len(row3)}, same{sum(same)}")
    print(same)
    print(row[2])
    print(row[3])
    