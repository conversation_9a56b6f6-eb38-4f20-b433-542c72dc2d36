v1 = {"0": {"indices": [391, 682, 430, 754, 774, 775, 195, 779, 781, 392, 375, 393, 623, 178, 787, 12, 17, 85, 176, 177, 783, 784, 785, 203, 205, 199, 200, 792, 614, 616, 617, 618, 404, 405, 428, 789, 67, 106, 107, 807, 406, 791, 149, 531, 82, 83, 530, 541, 196, 201, 170, 630, 461, 465, 482, 483, 484, 36, 608, 148, 162, 163, 213, 538, 169, 808, 429, 157, 670, 862, 395, 400, 401, 402, 403, 210, 211, 891, 893, 894, 669, 101, 168, 437, 609, 412, 413, 439, 450, 665, 764, 765, 285, 485, 525, 526, 557, 854, 381, 606], "values": [0.64306640625, 0.5732421875, 0.5673828125, 0.56591796875, 0.56591796875, 0.56591796875, 0.556640625, 0.54541015625, 0.54541015625, 0.5439453125, 0.54052734375, 0.54052734375, 0.52001953125, 0.515625, 0.51416015625, 0.50048828125, 0.498046875, 0.498046875, 0.498046875, 0.498046875, 0.493408203125, 0.493408203125, 0.493408203125, 0.492431640625, 0.492431640625, 0.457275390625, 0.457275390625, 0.456298828125, 0.45068359375, 0.45068359375, 0.45068359375, 0.45068359375, 0.4453125, 0.4453125, 0.435302734375, 0.425537109375, 0.42333984375, 0.42333984375, 0.42333984375, 0.416015625, 0.409423828125, 0.407958984375, 0.402587890625, 0.40234375, 0.39111328125, 0.39111328125, 0.3876953125, 0.3798828125, 0.37353515625, 0.372802734375, 0.366455078125, 0.365966796875, 0.36474609375, 0.36474609375, 0.36474609375, 0.36474609375, 0.36474609375, 0.364013671875, 0.3623046875, 0.35888671875, 0.35205078125, 0.35205078125, 0.3515625, 0.34716796875, 0.345703125, 0.345703125, 0.34521484375, 0.341064453125, 0.3408203125, 0.33203125, 0.33154296875, 0.33154296875, 0.33154296875, 0.33154296875, 0.33154296875, 0.326171875, 0.326171875, 0.32470703125, 0.32470703125, 0.32470703125, 0.321044921875, 0.318359375, 0.318359375, 0.31591796875, 0.314453125, 0.31396484375, 0.31396484375, 0.31396484375, 0.31396484375, 0.311767578125, 0.3115234375, 0.3115234375, 0.310791015625, 0.310791015625, 0.310791015625, 0.310791015625, 0.310791015625, 0.310791015625, 0.304443359375, 0.304443359375]}, "1": {"indices": [62, 63, 66, 75, 77, 78, 79, 80, 81, 82, 83, 87, 88, 57, 58, 1, 76, 39, 50, 130, 12, 0, 36, 38, 44, 85, 86, 52, 51, 89, 31, 53, 54, 67, 6, 128, 148, 149, 150, 151, 144, 145, 131, 65, 133, 49, 129, 23, 98, 103, 105, 68, 69, 122, 146, 71, 72, 126, 8, 115, 37, 97, 99, 100, 101, 102, 106, 84, 134, 116, 118, 123, 124, 132, 48, 26, 27, 45, 42, 43, 138, 10, 125, 73, 15, 16, 19, 22, 24, 64, 17, 25, 18, 21, 70, 139, 140, 141, 142, 136], "values": [0.63916015625, 0.63916015625, 0.63232421875, 0.5439453125, 0.5419921875, 0.5419921875, 0.51416015625, 0.51416015625, 0.51416015625, 0.51416015625, 0.51416015625, 0.51416015625, 0.51416015625, 0.4697265625, 0.4697265625, 0.431884765625, 0.423583984375, 0.4072265625, 0.402587890625, 0.38525390625, 0.378662109375, 0.374755859375, 0.364501953125, 0.36376953125, 0.348876953125, 0.34228515625, 0.34228515625, 0.319580078125, 0.314208984375, 0.309814453125, 0.309326171875, 0.300048828125, 0.300048828125, 0.2958984375, 0.2900390625, 0.283447265625, 0.276611328125, 0.276611328125, 0.276611328125, 0.276611328125, 0.268798828125, 0.268798828125, 0.26123046875, 0.25830078125, 0.25390625, 0.24365234375, 0.2423095703125, 0.2412109375, 0.237060546875, 0.237060546875, 0.237060546875, 0.2320556640625, 0.2320556640625, 0.231689453125, 0.2275390625, 0.2147216796875, 0.2147216796875, 0.21435546875, 0.2059326171875, 0.20166015625, 0.2015380859375, 0.19970703125, 0.19970703125, 0.19970703125, 0.19970703125, 0.19970703125, 0.19970703125, 0.1986083984375, 0.198486328125, 0.1959228515625, 0.1915283203125, 0.1915283203125, 0.1915283203125, 0.1827392578125, 0.1822509765625, 0.1817626953125, 0.1817626953125, 0.1817626953125, 0.1793212890625, 0.1793212890625, 0.1717529296875, 0.1705322265625, 0.1658935546875, 0.1578369140625, 0.1563720703125, 0.1563720703125, 0.146728515625, 0.1424560546875, 0.1424560546875, 0.135986328125, 0.1339111328125, 0.1314697265625, 0.1268310546875, 0.1260986328125, 0.12408447265625, 0.120361328125, 0.120361328125, 0.120361328125, 0.11724853515625, 0.10870361328125]}, "2": {"indices": [34, 35, 23, 9, 46, 28, 29, 30, 22, 24, 21, 47, 32, 33, 26, 27, 45, 25, 0, 6, 65, 39, 13, 14, 79, 80, 81, 82, 83, 87, 88, 17, 147, 40, 3, 4, 5, 1, 66, 146, 36, 67, 7, 20, 15, 16, 31, 77, 78, 76, 85, 86, 142, 148, 149, 150, 151, 132, 8, 75, 84, 144, 145, 38, 89, 37, 122, 11, 12, 44, 137, 42, 43, 10, 129, 118, 123, 124, 138, 134, 41, 128, 115, 60, 61, 139, 140, 141, 125, 62, 63, 131, 71, 72, 135, 136, 48, 19, 133, 58], "values": [0.69140625, 0.68115234375, 0.6796875, 0.63330078125, 0.60498046875, 0.5927734375, 0.5927734375, 0.5927734375, 0.57177734375, 0.57177734375, 0.499267578125, 0.48681640625, 0.45263671875, 0.45263671875, 0.42724609375, 0.42724609375, 0.42724609375, 0.37255859375, 0.361328125, 0.352783203125, 0.34130859375, 0.3251953125, 0.323486328125, 0.323486328125, 0.3154296875, 0.3154296875, 0.3154296875, 0.3154296875, 0.3154296875, 0.3154296875, 0.3154296875, 0.30908203125, 0.2880859375, 0.281005859375, 0.27294921875, 0.27294921875, 0.27294921875, 0.25927734375, 0.259033203125, 0.2445068359375, 0.2421875, 0.23779296875, 0.2332763671875, 0.2332763671875, 0.2305908203125, 0.2305908203125, 0.2236328125, 0.217529296875, 0.217529296875, 0.21728515625, 0.213134765625, 0.213134765625, 0.206298828125, 0.200927734375, 0.200927734375, 0.200927734375, 0.200927734375, 0.1982421875, 0.1959228515625, 0.1947021484375, 0.193115234375, 0.1890869140625, 0.1890869140625, 0.182373046875, 0.18115234375, 0.1788330078125, 0.1732177734375, 0.16552734375, 0.149658203125, 0.149169921875, 0.1484375, 0.1427001953125, 0.1427001953125, 0.1258544921875, 0.11370849609375, 0.113525390625, 0.113525390625, 0.113525390625, 0.11285400390625, 0.1102294921875, 0.1072998046875, 0.1025390625, 0.09979248046875, 0.09442138671875, 0.09442138671875, 0.09295654296875, 0.09295654296875, 0.09295654296875, 0.0791015625, 0.07879638671875, 0.07879638671875, 0.075439453125, 0.0751953125, 0.0751953125, 0.053619384765625, 0.053619384765625, 0.0517578125, 0.050750732421875, 0.0479736328125, 0.044586181640625]}, "3": {"indices": [7, 388, 361, 390, 391, 5, 90, 385, 389, 141, 145, 209, 383, 239, 266, 267, 30, 252, 6, 8, 716, 955, 4024, 25, 3812, 4014, 9, 17, 26, 27, 253, 254, 1440, 467, 650, 1083, 956, 465, 3177, 2075, 2101, 792, 793, 794, 332, 29, 41, 4008, 4009, 4324, 3041, 3209, 3210, 3211, 3212, 150, 4321, 2643, 48, 338, 2889, 31, 466, 392, 397, 3815, 368, 369, 370, 4169, 717, 4087, 4141, 4028, 4029, 651, 3695, 642, 4267, 4293, 2828, 623, 723, 745, 1131, 2347, 2348, 1487, 1447, 1448, 1495, 1496, 4048, 4197, 1702, 32, 4092, 811, 89, 111], "values": [0.998046875, 0.998046875, 0.96435546875, 0.96435546875, 0.96435546875, 0.84521484375, 0.84521484375, 0.84521484375, 0.84521484375, 0.83544921875, 0.83544921875, 0.83544921875, 0.83544921875, 0.83447265625, 0.83447265625, 0.83447265625, 0.79443359375, 0.7919921875, 0.75, 0.75, 0.75, 0.75, 0.74951171875, 0.69482421875, 0.6787109375, 0.67431640625, 0.6611328125, 0.6611328125, 0.6611328125, 0.6611328125, 0.6611328125, 0.6611328125, 0.650390625, 0.64599609375, 0.64599609375, 0.64599609375, 0.63818359375, 0.62939453125, 0.62939453125, 0.6279296875, 0.6279296875, 0.62255859375, 0.62255859375, 0.62255859375, 0.603515625, 0.60205078125, 0.60205078125, 0.6005859375, 0.6005859375, 0.6005859375, 0.5966796875, 0.5966796875, 0.5966796875, 0.5966796875, 0.5966796875, 0.595703125, 0.5947265625, 0.59033203125, 0.58984375, 0.583984375, 0.58154296875, 0.5791015625, 0.5791015625, 0.57861328125, 0.57861328125, 0.5751953125, 0.5712890625, 0.5712890625, 0.5712890625, 0.5703125, 0.5595703125, 0.556640625, 0.556640625, 0.548828125, 0.548828125, 0.546875, 0.544921875, 0.54296875, 0.54296875, 0.54296875, 0.54248046875, 0.5400390625, 0.5400390625, 0.5400390625, 0.5400390625, 0.53857421875, 0.53857421875, 0.5361328125, 0.53564453125, 0.53564453125, 0.5341796875, 0.5341796875, 0.53125, 0.5263671875, 0.525390625, 0.52294921875, 0.5224609375, 0.5185546875, 0.517578125, 0.51708984375]}, "4": {"indices": [218, 227, 205, 49, 50, 84, 174, 113, 114, 117, 119, 120, 225, 202, 85, 156, 172, 135, 122, 128, 10, 151, 171, 242, 243, 199, 115, 102, 54, 232, 233, 4, 145, 146, 226, 170, 63, 65, 169, 103, 104, 125, 139, 140, 37, 100, 109, 110, 111, 112, 53, 38, 173, 150, 29, 87, 97, 101, 106, 121, 123, 134, 141, 93, 0, 27, 206, 240, 7, 45, 180, 223, 72, 200, 9, 39, 60, 67, 71, 74, 75, 78, 79, 152, 153, 236, 86, 230, 46, 196, 231, 5, 6, 229, 47, 213, 58, 182, 183, 28], "values": [0.6767578125, 0.6767578125, 0.671875, 0.6337890625, 0.6337890625, 0.6337890625, 0.6337890625, 0.59814453125, 0.59814453125, 0.59814453125, 0.59814453125, 0.59814453125, 0.5947265625, 0.5498046875, 0.54296875, 0.51025390625, 0.51025390625, 0.5087890625, 0.479248046875, 0.47705078125, 0.46728515625, 0.46435546875, 0.462890625, 0.445068359375, 0.445068359375, 0.434326171875, 0.431640625, 0.429931640625, 0.429443359375, 0.42529296875, 0.42529296875, 0.4208984375, 0.419189453125, 0.419189453125, 0.41357421875, 0.41259765625, 0.41064453125, 0.41064453125, 0.410400390625, 0.404541015625, 0.404541015625, 0.404541015625, 0.404541015625, 0.404541015625, 0.403076171875, 0.40087890625, 0.40087890625, 0.40087890625, 0.40087890625, 0.40087890625, 0.396240234375, 0.392822265625, 0.390380859375, 0.3837890625, 0.380859375, 0.37548828125, 0.37548828125, 0.37548828125, 0.37548828125, 0.37548828125, 0.37548828125, 0.37548828125, 0.37548828125, 0.37353515625, 0.370849609375, 0.370849609375, 0.368896484375, 0.365478515625, 0.34423828125, 0.333251953125, 0.33251953125, 0.32861328125, 0.320068359375, 0.3193359375, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.31591796875, 0.311767578125, 0.311767578125, 0.31103515625, 0.310546875, 0.310546875, 0.308837890625, 0.30859375, 0.308349609375, 0.306640625, 0.306640625, 0.306396484375, 0.305419921875, 0.300537109375, 0.285888671875, 0.285400390625, 0.285400390625, 0.28466796875]}, "5": {"indices": [853, 854, 860, 920, 1230, 1833, 1875, 2091, 4120, 3871, 2185, 2176, 2215, 2216, 2320, 2433, 2481, 2577, 2695, 2697, 2698, 2730, 2731, 2732, 2733, 2855, 2859, 2863, 4071, 4473, 3870, 3872, 3873, 4155, 923, 960, 1002, 1020, 4465, 4184, 4701, 2208, 2217, 2615, 2646, 2683, 2692, 4353, 290, 2589, 2629, 2640, 2693, 2696, 2701, 2704, 2713, 2716, 2754, 2767, 2786, 4190, 4191, 4192, 4606, 4624, 4426, 4349, 1219, 3266, 3536, 3544, 3548, 3549, 4026, 3546, 3072, 3600, 3419, 3422, 3440, 4565, 4584, 4607, 4608, 4622, 2717, 2777, 2785, 4639, 4826, 4699, 3925, 3928, 4189, 17, 3541, 3545, 3929, 2769], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.96533203125, 0.82275390625, 0.82275390625, 0.78125, 0.70263671875, 0.69921875, 0.6640625, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.66064453125, 0.6572265625, 0.6572265625, 0.65478515625, 0.65478515625, 0.65478515625, 0.65234375, 0.634765625, 0.634765625, 0.634765625, 0.634765625, 0.630859375, 0.625, 0.625, 0.623046875, 0.619140625, 0.61669921875, 0.61669921875, 0.61669921875, 0.61669921875, 0.6162109375, 0.61279296875, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.60791015625, 0.6044921875, 0.6044921875, 0.6044921875, 0.60107421875, 0.60107421875, 0.5986328125, 0.5966796875, 0.5927734375, 0.5927734375, 0.5927734375, 0.5927734375, 0.5927734375, 0.5927734375, 0.5927734375, 0.58984375, 0.5830078125, 0.5830078125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58203125, 0.58203125, 0.58203125, 0.57080078125, 0.57080078125, 0.56884765625, 0.56396484375, 0.56396484375, 0.56298828125, 0.55615234375, 0.55517578125, 0.55517578125, 0.55419921875, 0.55224609375]}, "6": {"indices": [284, 240, 143, 144, 146, 147, 148, 827, 285, 145, 1051, 3060, 3236, 1014, 1501, 1502, 149, 2767, 996, 1040, 1060, 327, 328, 329, 87, 4313, 4094, 4095, 4097, 4098, 2345, 1086, 2489, 2492, 2493, 2494, 4542, 3858, 3497, 927, 1767, 2791, 3069, 3070, 3071, 3073, 3081, 3082, 3133, 3134, 3231, 2497, 3474, 3475, 3479, 3480, 3221, 3222, 116, 115, 1710, 1080, 4491, 3176, 4656, 4742, 4857, 834, 835, 531, 4500, 826, 1008, 289, 2760, 527, 778, 781, 2523, 2524, 2525, 3477, 2714, 3492, 828, 243, 553, 554, 556, 557, 558, 4403, 3233, 3235, 75, 3784, 1663, 999, 3265, 3137], "values": [0.72021484375, 0.66943359375, 0.66650390625, 0.66650390625, 0.66650390625, 0.66650390625, 0.66650390625, 0.63427734375, 0.62939453125, 0.548828125, 0.53466796875, 0.51318359375, 0.5068359375, 0.49755859375, 0.46533203125, 0.46533203125, 0.4609375, 0.453369140625, 0.440185546875, 0.440185546875, 0.440185546875, 0.43896484375, 0.43896484375, 0.43896484375, 0.431640625, 0.431640625, 0.4169921875, 0.4169921875, 0.4169921875, 0.4169921875, 0.41650390625, 0.414306640625, 0.408447265625, 0.408447265625, 0.408447265625, 0.408447265625, 0.408447265625, 0.40185546875, 0.400390625, 0.397216796875, 0.397216796875, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.3955078125, 0.395263671875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.392333984375, 0.392333984375, 0.3896484375, 0.388671875, 0.38720703125, 0.3857421875, 0.385009765625, 0.3837890625, 0.381103515625, 0.381103515625, 0.381103515625, 0.375732421875, 0.375732421875, 0.373046875, 0.371337890625, 0.37109375, 0.369384765625, 0.36767578125, 0.366943359375, 0.364990234375, 0.364990234375, 0.364990234375, 0.3623046875, 0.3623046875, 0.3623046875, 0.3525390625, 0.351318359375, 0.350830078125, 0.3505859375, 0.34912109375, 0.348388671875, 0.348388671875, 0.348388671875, 0.348388671875, 0.348388671875, 0.347900390625, 0.344970703125, 0.344970703125, 0.3447265625, 0.34423828125, 0.34375, 0.34228515625, 0.34228515625, 0.340576171875]}, "7": {"indices": [34, 7, 11, 14, 123, 12, 46, 33, 13, 36, 41, 42, 15, 43, 29, 35, 104, 98, 30, 8, 9, 77, 6, 207, 27, 28, 31, 137, 138, 139, 140, 37, 172, 40, 68, 99, 112, 113, 184, 25, 63, 155, 193, 32, 54, 55, 56, 57, 158, 159, 160, 161, 162, 163, 91, 26, 10, 52, 97, 39, 199, 200, 201, 110, 118, 150, 152, 153, 210, 89, 108, 24, 38, 58, 194, 213, 94, 180, 197, 93, 44, 53, 59, 50, 174, 121, 122, 205, 96, 166, 103, 105, 212, 101, 71, 5, 100, 48, 45, 167], "values": [0.837890625, 0.6875, 0.6318359375, 0.6318359375, 0.6181640625, 0.607421875, 0.5478515625, 0.533203125, 0.52978515625, 0.52587890625, 0.52587890625, 0.52587890625, 0.497314453125, 0.48193359375, 0.476318359375, 0.466796875, 0.45703125, 0.43115234375, 0.42529296875, 0.421875, 0.41845703125, 0.405029296875, 0.40185546875, 0.388916015625, 0.384521484375, 0.384521484375, 0.381591796875, 0.3740234375, 0.3740234375, 0.3740234375, 0.3740234375, 0.36572265625, 0.35302734375, 0.352294921875, 0.342529296875, 0.337890625, 0.327392578125, 0.327392578125, 0.314697265625, 0.310791015625, 0.30908203125, 0.302001953125, 0.301513671875, 0.297119140625, 0.2919921875, 0.2919921875, 0.2919921875, 0.2919921875, 0.290771484375, 0.290771484375, 0.290771484375, 0.290771484375, 0.290771484375, 0.290771484375, 0.287353515625, 0.285888671875, 0.283447265625, 0.278076171875, 0.277587890625, 0.277099609375, 0.27587890625, 0.27587890625, 0.27587890625, 0.27392578125, 0.27392578125, 0.27099609375, 0.27099609375, 0.27099609375, 0.27099609375, 0.270751953125, 0.262451171875, 0.25244140625, 0.2447509765625, 0.241455078125, 0.241455078125, 0.2364501953125, 0.2347412109375, 0.2301025390625, 0.2281494140625, 0.223388671875, 0.22314453125, 0.2227783203125, 0.2208251953125, 0.2203369140625, 0.2164306640625, 0.21337890625, 0.21337890625, 0.213134765625, 0.2127685546875, 0.21240234375, 0.211181640625, 0.211181640625, 0.2059326171875, 0.197021484375, 0.1964111328125, 0.1962890625, 0.1944580078125, 0.1793212890625, 0.17724609375, 0.172119140625]}, "8": {"indices": [446, 473, 575, 597, 604, 605, 606, 607, 614, 461, 1433, 1700, 1348, 611, 1571, 1572, 1573, 627, 412, 413, 414, 621, 132, 133, 525, 526, 130, 1944, 245, 248, 1166, 425, 422, 1943, 2193, 1930, 145, 146, 127, 1462, 1463, 1295, 1296, 1297, 1298, 472, 1781, 894, 1845, 2002, 2003, 2014, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 444, 445, 468, 469, 470, 128, 154, 1379, 1975, 1299, 343, 148, 149, 1658, 1984, 1993, 2012, 2039, 925, 2683, 618, 619, 653, 654, 1522, 1739, 1784, 1785, 1787, 1788, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 542, 544], "values": [0.90673828125, 0.90673828125, 0.87744140625, 0.87744140625, 0.87744140625, 0.87744140625, 0.87744140625, 0.87744140625, 0.87744140625, 0.853515625, 0.80029296875, 0.7705078125, 0.74951171875, 0.74462890625, 0.701171875, 0.701171875, 0.701171875, 0.65576171875, 0.6513671875, 0.6513671875, 0.6513671875, 0.6484375, 0.640625, 0.640625, 0.63330078125, 0.63330078125, 0.59130859375, 0.580078125, 0.572265625, 0.572265625, 0.552734375, 0.54638671875, 0.5361328125, 0.5361328125, 0.5166015625, 0.51416015625, 0.509765625, 0.509765625, 0.50439453125, 0.50439453125, 0.50439453125, 0.50341796875, 0.50341796875, 0.50341796875, 0.50341796875, 0.50146484375, 0.50146484375, 0.5009765625, 0.50048828125, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.496337890625, 0.488037109375, 0.488037109375, 0.488037109375, 0.488037109375, 0.488037109375, 0.48291015625, 0.48291015625, 0.4716796875, 0.4716796875, 0.460205078125, 0.459228515625, 0.45849609375, 0.45849609375, 0.451171875, 0.4453125, 0.4453125, 0.4453125, 0.4453125, 0.444580078125, 0.439453125, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.434814453125, 0.4287109375, 0.4287109375]}, "9": {"indices": [1572, 4631, 4562, 4033, 4217, 2593, 2672, 2592, 2228, 2694, 2697, 36, 2226, 2227, 3384, 780, 440, 505, 3957, 31, 580, 588, 504, 4755, 4759, 626, 2710, 175, 2201, 4136, 1771, 581, 587, 828, 829, 830, 1092, 39, 4260, 2184, 2229, 2556, 2714, 3918, 383, 4187, 4188, 4241, 1543, 2669, 4102, 4103, 4129, 4757, 4758, 4762, 525, 15, 4101, 4104, 619, 620, 4286, 3142, 377, 506, 507, 658, 30, 1090, 3383, 437, 439, 549, 508, 509, 2234, 4761, 4756, 1732, 953, 954, 784, 1948, 2061, 4429, 55, 1697, 1978, 3933, 4233, 4470, 4477, 4478, 4479, 1079, 625, 1655, 502, 657], "values": [0.998046875, 0.876953125, 0.8466796875, 0.7919921875, 0.7919921875, 0.6884765625, 0.68603515625, 0.6796875, 0.66552734375, 0.66552734375, 0.66552734375, 0.64697265625, 0.64697265625, 0.64697265625, 0.6279296875, 0.61083984375, 0.58984375, 0.58251953125, 0.57763671875, 0.57421875, 0.57275390625, 0.57275390625, 0.56494140625, 0.55810546875, 0.55810546875, 0.55517578125, 0.5517578125, 0.54052734375, 0.5400390625, 0.5126953125, 0.51123046875, 0.5107421875, 0.5107421875, 0.5107421875, 0.5107421875, 0.5107421875, 0.5107421875, 0.51025390625, 0.50537109375, 0.50390625, 0.498046875, 0.498046875, 0.498046875, 0.486328125, 0.482177734375, 0.48095703125, 0.48095703125, 0.4794921875, 0.477294921875, 0.477294921875, 0.47705078125, 0.47705078125, 0.47705078125, 0.47705078125, 0.47705078125, 0.47705078125, 0.4755859375, 0.474365234375, 0.4697265625, 0.4697265625, 0.467041015625, 0.467041015625, 0.466796875, 0.464599609375, 0.463134765625, 0.463134765625, 0.463134765625, 0.463134765625, 0.45947265625, 0.456787109375, 0.455078125, 0.454833984375, 0.454833984375, 0.453125, 0.450927734375, 0.450927734375, 0.447998046875, 0.4443359375, 0.44384765625, 0.430419921875, 0.428466796875, 0.428466796875, 0.427978515625, 0.427978515625, 0.427978515625, 0.427978515625, 0.422119140625, 0.4208984375, 0.4189453125, 0.418212890625, 0.41162109375, 0.40869140625, 0.40869140625, 0.40869140625, 0.40869140625, 0.408203125, 0.407470703125, 0.407470703125, 0.4072265625, 0.4072265625]}, "10": {"indices": [190, 195, 207, 390, 967, 1274, 1593, 1231, 1267, 1273, 1302, 1313, 1329, 1429, 1453, 1475, 619, 1214, 1596, 576, 682, 722, 1120, 1581, 1582, 1594, 201, 205, 206, 1584, 1449, 1481, 1493, 723, 812, 1261, 1268, 1625, 1627, 1630, 1373, 1352, 429, 607, 613, 658, 720, 236, 374, 393, 396, 451, 509, 510, 513, 556, 569, 570, 575, 582, 602, 614, 617, 639, 642, 643, 714, 719, 1400, 498, 1597, 1599, 1601, 1679, 1600, 452, 1149, 1180, 1207, 1211, 1452, 1476, 430, 1263, 1612, 1628, 1619, 1605, 131, 139, 679, 691, 724, 743, 748, 749, 790, 800, 809, 819], "values": [0.998046875, 0.998046875, 0.998046875, 0.8984375, 0.8984375, 0.8984375, 0.8984375, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.89794921875, 0.8818359375, 0.8818359375, 0.8818359375, 0.875, 0.875, 0.875, 0.865234375, 0.86083984375, 0.86083984375, 0.86083984375, 0.859375, 0.859375, 0.859375, 0.8505859375, 0.845703125, 0.845703125, 0.845703125, 0.83984375, 0.83984375, 0.83984375, 0.83984375, 0.83984375, 0.83984375, 0.83984375, 0.8388671875, 0.8359375, 0.83544921875, 0.83544921875, 0.83544921875, 0.83544921875, 0.83544921875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.83154296875, 0.82763671875, 0.8271484375, 0.8271484375, 0.8271484375, 0.8271484375, 0.8271484375, 0.826171875, 0.82275390625, 0.82275390625, 0.82275390625, 0.82275390625, 0.82275390625, 0.82177734375, 0.82177734375, 0.8203125, 0.8125, 0.810546875, 0.810546875, 0.806640625, 0.80615234375, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625, 0.8056640625]}, "11": {"indices": [385, 1541, 481, 485, 486, 505, 515, 689, 988, 406, 1579, 1613, 1328, 1414, 1415, 1417, 1682, 375, 380, 386, 473, 1375, 1391, 873, 874, 1266, 881, 1117, 940, 1461, 412, 421, 422, 423, 427, 428, 435, 449, 478, 480, 1270, 1314, 1330, 1402, 1430, 1454, 1466, 1467, 1468, 1530, 442, 968, 1499, 1673, 1707, 408, 1508, 1543, 1306, 499, 757, 1133, 418, 447, 893, 1199, 1200, 439, 69, 29, 30, 52, 53, 54, 1708, 232, 1662, 376, 1573, 311, 1222, 1463, 1478, 1496, 1552, 1555, 1558, 1559, 1576, 1587, 584, 1225, 1226, 1227, 1519, 1534, 1535, 437, 438, 487], "values": [0.998046875, 0.97314453125, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.94384765625, 0.94384765625, 0.94384765625, 0.93701171875, 0.93701171875, 0.93701171875, 0.93701171875, 0.93310546875, 0.93115234375, 0.93115234375, 0.93115234375, 0.9169921875, 0.9033203125, 0.9033203125, 0.90087890625, 0.90087890625, 0.900390625, 0.8916015625, 0.890625, 0.888671875, 0.88623046875, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.87109375, 0.86279296875, 0.86279296875, 0.86279296875, 0.86279296875, 0.86279296875, 0.8525390625, 0.8525390625, 0.8525390625, 0.84912109375, 0.84765625, 0.8466796875, 0.84375, 0.8427734375, 0.8427734375, 0.8427734375, 0.8291015625, 0.8291015625, 0.82763671875, 0.8271484375, 0.82666015625, 0.82666015625, 0.82666015625, 0.82666015625, 0.82666015625, 0.826171875, 0.82080078125, 0.818359375, 0.81787109375, 0.8173828125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81689453125, 0.81640625, 0.8154296875, 0.8154296875, 0.8154296875, 0.8154296875, 0.8154296875, 0.8154296875, 0.81494140625, 0.81494140625, 0.81494140625]}, "12": {"indices": [224, 225, 226, 221, 227, 218, 219, 220, 170, 185, 198, 210, 169, 183, 70, 73, 76, 233, 280, 300, 330, 361, 362, 16, 223, 33, 376, 128, 43, 358, 146, 106, 176, 239, 360, 147, 209, 126, 196, 272, 273, 274, 277, 62, 192, 186, 2, 112, 113, 39, 64, 68, 350, 351, 153, 154, 211, 199, 387, 388, 389, 390, 391, 392, 278, 355, 177, 44, 322, 152, 56, 18, 250, 144, 117, 119, 120, 71, 72, 51, 377, 378, 156, 157, 174, 175, 28, 327, 114, 332, 82, 27, 59, 214, 248, 249, 96, 140, 229, 55], "values": [0.84326171875, 0.84326171875, 0.84326171875, 0.7001953125, 0.7001953125, 0.68310546875, 0.68310546875, 0.68310546875, 0.556640625, 0.52685546875, 0.51904296875, 0.49853515625, 0.498291015625, 0.483154296875, 0.47265625, 0.47265625, 0.466064453125, 0.460205078125, 0.4580078125, 0.4443359375, 0.443359375, 0.443359375, 0.443359375, 0.44287109375, 0.411865234375, 0.41015625, 0.409912109375, 0.4033203125, 0.401611328125, 0.386962890625, 0.386474609375, 0.378173828125, 0.377197265625, 0.37451171875, 0.371337890625, 0.37109375, 0.37109375, 0.36669921875, 0.36669921875, 0.365478515625, 0.365478515625, 0.365478515625, 0.365478515625, 0.365234375, 0.363037109375, 0.36083984375, 0.360107421875, 0.353271484375, 0.353271484375, 0.348876953125, 0.347412109375, 0.346923828125, 0.34619140625, 0.34619140625, 0.341064453125, 0.341064453125, 0.33935546875, 0.3330078125, 0.33056640625, 0.33056640625, 0.33056640625, 0.33056640625, 0.33056640625, 0.33056640625, 0.325439453125, 0.324462890625, 0.32421875, 0.3203125, 0.31689453125, 0.316162109375, 0.31298828125, 0.302734375, 0.302734375, 0.30126953125, 0.297607421875, 0.297607421875, 0.297607421875, 0.295654296875, 0.295654296875, 0.294189453125, 0.291748046875, 0.291748046875, 0.291259765625, 0.291259765625, 0.291259765625, 0.291015625, 0.28955078125, 0.2880859375, 0.287109375, 0.28466796875, 0.284423828125, 0.282470703125, 0.2822265625, 0.2822265625, 0.2822265625, 0.2822265625, 0.281982421875, 0.281982421875, 0.27978515625, 0.279541015625]}, "13": {"indices": [258, 255, 259, 257, 256, 260, 520, 496, 561, 2819, 512, 513, 556, 557, 481, 2461, 986, 1014, 1016, 1017, 1018, 1019, 1021, 1026, 1027, 1031, 1032, 1033, 1034, 1036, 1037, 1038, 1847, 276, 590, 2815, 2816, 558, 559, 585, 425, 212, 213, 279, 516, 262, 2858, 1364, 281, 253, 514, 1973, 1974, 1975, 216, 699, 700, 509, 1050, 1053, 2007, 2009, 2594, 2508, 274, 1009, 2356, 277, 278, 2111, 2112, 1013, 1015, 1020, 1022, 1023, 1025, 1028, 1029, 1030, 2242, 1758, 1759, 2440, 2865, 2867, 2868, 2826, 2822, 237, 261, 471, 988, 1361, 1363, 2551, 2552, 2441, 2389, 2390], "values": [0.6474609375, 0.58837890625, 0.57666015625, 0.568359375, 0.55859375, 0.55615234375, 0.55322265625, 0.552734375, 0.5205078125, 0.51123046875, 0.5048828125, 0.5048828125, 0.5, 0.5, 0.49951171875, 0.498046875, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.47998046875, 0.472412109375, 0.46826171875, 0.46728515625, 0.46728515625, 0.457275390625, 0.457275390625, 0.4521484375, 0.43798828125, 0.4375, 0.4375, 0.437255859375, 0.437255859375, 0.4365234375, 0.434814453125, 0.430419921875, 0.423095703125, 0.419921875, 0.419677734375, 0.41259765625, 0.41259765625, 0.41259765625, 0.41162109375, 0.41015625, 0.41015625, 0.40771484375, 0.40771484375, 0.40771484375, 0.40625, 0.40625, 0.405517578125, 0.405029296875, 0.40478515625, 0.39990234375, 0.399658203125, 0.396484375, 0.396484375, 0.396240234375, 0.396240234375, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.3935546875, 0.39111328125, 0.38916015625, 0.38916015625, 0.38916015625, 0.387451171875, 0.387451171875, 0.387451171875, 0.386474609375, 0.38427734375, 0.3837890625, 0.382568359375, 0.37939453125, 0.374267578125, 0.371337890625, 0.371337890625, 0.370361328125, 0.370361328125, 0.36767578125, 0.36572265625, 0.36572265625]}, "14": {"indices": [274, 2311, 276, 467, 468, 254, 2387, 2380, 256, 2895, 255, 509, 1050, 1053, 478, 1989, 1990, 1994, 1995, 1999, 2000, 540, 1046, 1047, 1048, 2455, 286, 275, 281, 2322, 287, 2551, 2552, 2869, 489, 1581, 522, 731, 267, 268, 269, 270, 271, 441, 2385, 2386, 285, 288, 733, 2349, 2508, 884, 2506, 279, 263, 264, 280, 252, 2146, 403, 277, 278, 283, 2094, 2095, 940, 300, 730, 732, 457, 1996, 648, 558, 559, 258, 2105, 772, 1100, 273, 1051, 1991, 1992, 1993, 663, 726, 727, 728, 729, 460, 461, 212, 213, 487, 2027, 2039, 2040, 2043, 2044, 2045, 2046], "values": [0.63671875, 0.607421875, 0.603515625, 0.59228515625, 0.59228515625, 0.587890625, 0.57861328125, 0.568359375, 0.548828125, 0.5400390625, 0.53857421875, 0.53857421875, 0.53857421875, 0.53857421875, 0.525390625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5146484375, 0.5009765625, 0.5009765625, 0.5009765625, 0.50048828125, 0.499755859375, 0.48974609375, 0.486572265625, 0.478759765625, 0.4736328125, 0.469970703125, 0.469970703125, 0.468017578125, 0.46728515625, 0.4658203125, 0.457763671875, 0.457763671875, 0.457275390625, 0.457275390625, 0.457275390625, 0.457275390625, 0.457275390625, 0.457275390625, 0.45654296875, 0.45654296875, 0.456298828125, 0.451904296875, 0.44677734375, 0.444580078125, 0.444580078125, 0.4326171875, 0.431640625, 0.42724609375, 0.422607421875, 0.422607421875, 0.4208984375, 0.420166015625, 0.420166015625, 0.41943359375, 0.418212890625, 0.418212890625, 0.41650390625, 0.416259765625, 0.416259765625, 0.4150390625, 0.413330078125, 0.410888671875, 0.410888671875, 0.410400390625, 0.408203125, 0.402587890625, 0.3974609375, 0.3974609375, 0.397216796875, 0.396728515625, 0.39404296875, 0.389404296875, 0.38671875, 0.385009765625, 0.38427734375, 0.38427734375, 0.38427734375, 0.384033203125, 0.384033203125, 0.384033203125, 0.384033203125, 0.384033203125, 0.383056640625, 0.383056640625, 0.379150390625, 0.379150390625, 0.3779296875, 0.376953125, 0.376953125, 0.376953125, 0.376953125, 0.376953125, 0.376953125, 0.376953125]}, "15": {"indices": [553, 2753, 466, 2089, 2440, 756, 2389, 2390, 2547, 1821, 1840, 228, 229, 232, 233, 234, 235, 236, 2140, 906, 2608, 458, 469, 1107, 2048, 2503, 1361, 1363, 910, 911, 1191, 1100, 1582, 1583, 238, 243, 244, 344, 405, 2352, 1277, 1278, 1316, 909, 1024, 1035, 2716, 2592, 1796, 1164, 1165, 1166, 1248, 1249, 1264, 2182, 2198, 2199, 2200, 444, 507, 508, 2849, 725, 174, 175, 549, 555, 2695, 242, 757, 770, 807, 847, 848, 849, 850, 851, 852, 853, 854, 862, 863, 864, 794, 796, 797, 414, 2506, 772, 368, 1106, 982, 1644, 2274, 1507, 425, 349, 715, 716], "values": [0.66455078125, 0.63623046875, 0.60986328125, 0.54443359375, 0.49169921875, 0.470703125, 0.46435546875, 0.46435546875, 0.463623046875, 0.45849609375, 0.45849609375, 0.458251953125, 0.458251953125, 0.458251953125, 0.458251953125, 0.458251953125, 0.458251953125, 0.458251953125, 0.45068359375, 0.4501953125, 0.4501953125, 0.44580078125, 0.44580078125, 0.445556640625, 0.445556640625, 0.4453125, 0.443603515625, 0.443603515625, 0.43505859375, 0.43505859375, 0.43310546875, 0.43212890625, 0.423095703125, 0.423095703125, 0.420166015625, 0.420166015625, 0.420166015625, 0.415771484375, 0.415771484375, 0.415771484375, 0.4150390625, 0.4150390625, 0.4150390625, 0.414794921875, 0.41357421875, 0.41357421875, 0.41162109375, 0.40625, 0.405517578125, 0.404052734375, 0.404052734375, 0.404052734375, 0.404052734375, 0.404052734375, 0.404052734375, 0.401611328125, 0.401611328125, 0.401611328125, 0.401611328125, 0.39990234375, 0.39990234375, 0.39990234375, 0.397705078125, 0.396728515625, 0.39599609375, 0.39599609375, 0.39453125, 0.39453125, 0.391845703125, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.391357421875, 0.388916015625, 0.388916015625, 0.388916015625, 0.387451171875, 0.385986328125, 0.384033203125, 0.3837890625, 0.381591796875, 0.379638671875, 0.377685546875, 0.37646484375, 0.376220703125, 0.374267578125, 0.37353515625, 0.37158203125, 0.37158203125]}, "16": {"indices": [347, 169, 3310, 3314, 3399, 4412, 3122, 1846, 1847, 1850, 1852, 1855, 1856, 3431, 4854, 3304, 819, 760, 958, 3305, 3307, 78, 79, 80, 81, 331, 4636, 3937, 957, 1849, 1857, 1858, 3866, 3867, 0, 10, 1987, 4629, 3105, 4832, 1862, 1848, 1939, 3876, 3933, 3125, 4518, 4562, 4627, 4630, 4834, 3942, 3944, 3945, 1827, 762, 763, 767, 2851, 2852, 2857, 2859, 3939, 3940, 2625, 3946, 3943, 4136, 961, 3745, 1284, 3103, 1890, 1892, 2503, 3202, 4576, 2447, 2451, 3301, 1829, 2647, 4138, 4139, 3112, 2061, 2062, 2332, 4392, 4498, 3123, 2498, 2392, 2393, 4821, 83, 163, 2320, 2321, 2322], "values": [0.8837890625, 0.6748046875, 0.6689453125, 0.6689453125, 0.64306640625, 0.6328125, 0.6279296875, 0.62744140625, 0.62744140625, 0.6181640625, 0.6181640625, 0.6181640625, 0.6181640625, 0.6064453125, 0.59716796875, 0.5966796875, 0.59423828125, 0.59033203125, 0.59033203125, 0.5869140625, 0.5869140625, 0.58349609375, 0.58349609375, 0.58349609375, 0.58349609375, 0.58349609375, 0.58349609375, 0.5830078125, 0.58154296875, 0.58154296875, 0.58154296875, 0.58154296875, 0.580078125, 0.57373046875, 0.5673828125, 0.5673828125, 0.5673828125, 0.56640625, 0.5576171875, 0.55615234375, 0.5537109375, 0.55322265625, 0.54736328125, 0.54296875, 0.541015625, 0.53857421875, 0.533203125, 0.533203125, 0.533203125, 0.533203125, 0.53271484375, 0.53173828125, 0.53173828125, 0.53173828125, 0.529296875, 0.52392578125, 0.52392578125, 0.52392578125, 0.521484375, 0.521484375, 0.521484375, 0.521484375, 0.52099609375, 0.52099609375, 0.5205078125, 0.5205078125, 0.52001953125, 0.5166015625, 0.51513671875, 0.51513671875, 0.51416015625, 0.51171875, 0.51025390625, 0.51025390625, 0.50927734375, 0.5087890625, 0.50732421875, 0.50341796875, 0.50341796875, 0.50244140625, 0.50048828125, 0.50048828125, 0.49951171875, 0.49951171875, 0.49755859375, 0.497314453125, 0.497314453125, 0.497314453125, 0.4970703125, 0.49658203125, 0.492431640625, 0.491943359375, 0.490478515625, 0.490478515625, 0.490478515625, 0.48974609375, 0.48974609375, 0.48974609375, 0.48974609375, 0.48974609375]}, "17": {"indices": [29, 8, 10, 24, 18, 31, 32, 6, 7, 19, 20, 22, 11, 12, 25, 27, 21, 3, 9, 26, 34, 35, 4, 1, 2, 28, 23, 30, 0, 5, 13, 14, 15, 16, 17, 33], "values": [0.54296875, 0.48193359375, 0.48193359375, 0.469970703125, 0.468505859375, 0.4658203125, 0.447265625, 0.437255859375, 0.437255859375, 0.437255859375, 0.437255859375, 0.4345703125, 0.424560546875, 0.424560546875, 0.395263671875, 0.346435546875, 0.328857421875, 0.31103515625, 0.295654296875, 0.295166015625, 0.295166015625, 0.295166015625, 0.2900390625, 0.286865234375, 0.286865234375, 0.279296875, 0.27685546875, 0.1845703125, 0.16748046875, 0.1458740234375, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 24, 26, 6, 7, 19, 20, 8, 10, 31, 28, 9, 21, 27, 30, 23, 29, 11, 12, 4, 1, 2, 25, 5, 34, 35, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.69287109375, 0.66650390625, 0.61328125, 0.60400390625, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.578125, 0.578125, 0.56201171875, 0.54638671875, 0.53271484375, 0.52978515625, 0.52734375, 0.51416015625, 0.480712890625, 0.47607421875, 0.44140625, 0.44140625, 0.43994140625, 0.435302734375, 0.435302734375, 0.433837890625, 0.40283203125, 0.401123046875, 0.401123046875, 0.3642578125, 0.354736328125, 0.26416015625, 0.1600341796875, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [60, 61, 18, 79, 163, 161, 86, 87, 51, 52, 111, 59, 57, 119, 43, 7, 195, 210, 105, 201, 206, 3, 6, 55, 62, 175, 215, 63, 64, 70, 211, 58, 180, 208, 209, 142, 143, 144, 172, 46, 126, 81, 69, 243, 189, 226, 194, 65, 190, 2, 4, 5, 203, 113, 74, 120, 14, 16, 89, 82, 198, 207, 71, 54, 80, 199, 110, 197, 181, 124, 141, 23, 112, 114, 179, 233, 244, 205, 213, 128, 22, 234, 42, 196, 133, 212, 15, 236, 237, 103, 247, 229, 214, 106, 146, 160, 166, 24, 183, 245], "values": [0.8076171875, 0.8076171875, 0.80419921875, 0.61376953125, 0.572265625, 0.5498046875, 0.50732421875, 0.50732421875, 0.49755859375, 0.49755859375, 0.486328125, 0.453369140625, 0.449951171875, 0.4140625, 0.396240234375, 0.39208984375, 0.39111328125, 0.3828125, 0.356201171875, 0.353271484375, 0.351806640625, 0.33203125, 0.3232421875, 0.30712890625, 0.30712890625, 0.292724609375, 0.27734375, 0.2763671875, 0.2763671875, 0.27197265625, 0.271240234375, 0.2568359375, 0.2486572265625, 0.2447509765625, 0.2447509765625, 0.2264404296875, 0.2264404296875, 0.2264404296875, 0.225341796875, 0.2236328125, 0.217041015625, 0.204833984375, 0.20458984375, 0.190673828125, 0.1873779296875, 0.1864013671875, 0.1839599609375, 0.1820068359375, 0.1805419921875, 0.1771240234375, 0.1771240234375, 0.1771240234375, 0.17578125, 0.1746826171875, 0.170654296875, 0.1640625, 0.1573486328125, 0.1573486328125, 0.1566162109375, 0.1556396484375, 0.1484375, 0.1453857421875, 0.145263671875, 0.1417236328125, 0.1417236328125, 0.138916015625, 0.1370849609375, 0.12841796875, 0.1268310546875, 0.12457275390625, 0.12457275390625, 0.1207275390625, 0.115966796875, 0.115966796875, 0.10546875, 0.1051025390625, 0.1046142578125, 0.10052490234375, 0.09912109375, 0.098388671875, 0.09588623046875, 0.095703125, 0.092529296875, 0.0899658203125, 0.0897216796875, 0.08697509765625, 0.0867919921875, 0.08453369140625, 0.0816650390625, 0.08099365234375, 0.08074951171875, 0.07666015625, 0.0733642578125, 0.07171630859375, 0.07171630859375, 0.07171630859375, 0.07171630859375, 0.0706787109375, 0.06781005859375, 0.06695556640625]}}

v2 = {"0": {"indices": [391, 682, 430, 392, 195, 754, 774, 775, 779, 781, 375, 393, 623, 178, 12, 787, 203, 205, 17, 85, 176, 177, 783, 784, 785, 428, 199, 200, 792, 404, 405, 614, 616, 617, 618, 789, 67, 106, 107, 807, 791, 531, 406, 149, 82, 83, 530, 630, 461, 465, 482, 483, 484, 541, 201, 196, 36, 170, 429, 213, 608, 162, 163, 148, 538, 169, 808, 670, 157, 862, 210, 211, 891, 893, 894, 764, 765, 395, 400, 401, 402, 403, 412, 413, 439, 450, 669, 101, 168, 665, 437, 609, 854, 285, 485, 525, 526, 557, 455, 228], "values": [0.634227991104126, 0.5791579484939575, 0.5569994449615479, 0.5523046851158142, 0.5470099449157715, 0.5457106828689575, 0.5457106828689575, 0.5457106828689575, 0.5335338711738586, 0.5335338711738586, 0.5325450897216797, 0.5325450897216797, 0.5082945227622986, 0.5031034350395203, 0.49784886837005615, 0.49738410115242004, 0.4895824193954468, 0.4895824193954468, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.4803156852722168, 0.4803156852722168, 0.4803156852722168, 0.44719216227531433, 0.44329220056533813, 0.44329220056533813, 0.43824338912963867, 0.43666914105415344, 0.43666914105415344, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.43399766087532043, 0.42945337295532227, 0.42945337295532227, 0.42945337295532227, 0.40701183676719666, 0.4068290591239929, 0.4050838053226471, 0.40103745460510254, 0.3955700993537903, 0.38879138231277466, 0.38879138231277466, 0.3796015679836273, 0.373837411403656, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.36885976791381836, 0.3659629821777344, 0.3619682192802429, 0.3513945937156677, 0.3505263924598694, 0.3496953845024109, 0.34440508484840393, 0.34231650829315186, 0.33986353874206543, 0.33986353874206543, 0.33520692586898804, 0.3319920301437378, 0.3302970826625824, 0.3302970826625824, 0.32643309235572815, 0.326144278049469, 0.3249531388282776, 0.32240742444992065, 0.32240742444992065, 0.31665271520614624, 0.31665271520614624, 0.31665271520614624, 0.31230077147483826, 0.31230077147483826, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30816277861595154, 0.3070964813232422, 0.3070964813232422, 0.30662038922309875, 0.3054530918598175, 0.30521929264068604, 0.3042910397052765, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29580268263816833, 0.29359209537506104]}, "1": {"indices": [62, 63, 66, 75, 77, 78, 79, 80, 81, 82, 83, 87, 88, 57, 58, 50, 76, 39, 1, 12, 130, 0, 36, 38, 44, 85, 86, 52, 51, 53, 54, 31, 89, 128, 67, 6, 148, 149, 150, 151, 49, 65, 133, 131, 129, 144, 145, 126, 98, 103, 105, 68, 69, 23, 71, 72, 122, 97, 99, 100, 101, 102, 106, 118, 123, 124, 115, 146, 116, 134, 8, 37, 84, 132, 48, 26, 27, 45, 42, 43, 138, 125, 73, 19, 15, 16, 18, 10, 25, 70, 17, 21, 22, 24, 139, 140, 141, 104, 40, 64], "values": [0.6368813514709473, 0.6368813514709473, 0.6130360960960388, 0.5421620011329651, 0.5375056266784668, 0.5375056266784668, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.46104589104652405, 0.46104589104652405, 0.4271812438964844, 0.4214109182357788, 0.4121088981628418, 0.4103316068649292, 0.40282025933265686, 0.38614192605018616, 0.3760111331939697, 0.3721415102481842, 0.36416345834732056, 0.3479672372341156, 0.3272571563720703, 0.3272571563720703, 0.3245006203651428, 0.3203641474246979, 0.31175732612609863, 0.31175732612609863, 0.2979270815849304, 0.2969631552696228, 0.29581066966056824, 0.2801106572151184, 0.2711765766143799, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.26397550106048584, 0.26295462250709534, 0.2629290223121643, 0.25873640179634094, 0.2566879689693451, 0.2504494786262512, 0.2504494786262512, 0.2430320680141449, 0.23912516236305237, 0.23912516236305237, 0.23912516236305237, 0.23463478684425354, 0.23463478684425354, 0.23280125856399536, 0.2311907708644867, 0.2311907708644867, 0.22300760447978973, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.21124772727489471, 0.21124772727489471, 0.21124772727489471, 0.2102745771408081, 0.20816147327423096, 0.20361432433128357, 0.2030743658542633, 0.19641417264938354, 0.1943962126970291, 0.1915108859539032, 0.18737488985061646, 0.18413467705249786, 0.17745643854141235, 0.17745643854141235, 0.17745643854141235, 0.1760486364364624, 0.1760486364364624, 0.17468427121639252, 0.1661725640296936, 0.16441266238689423, 0.15954509377479553, 0.15381674468517303, 0.15381674468517303, 0.15104883909225464, 0.14442673325538635, 0.13740898668766022, 0.13240142166614532, 0.13176646828651428, 0.12431777268648148, 0.12279310822486877, 0.12279310822486877, 0.12142190337181091, 0.12142190337181091, 0.12142190337181091, 0.11630198359489441, 0.11599351465702057, 0.11398488283157349]}, "2": {"indices": [34, 35, 23, 9, 46, 28, 29, 30, 22, 24, 21, 47, 32, 33, 26, 27, 45, 25, 0, 65, 6, 39, 13, 14, 79, 80, 81, 82, 83, 87, 88, 17, 147, 40, 3, 4, 5, 66, 36, 146, 1, 20, 7, 15, 16, 67, 31, 77, 78, 76, 132, 142, 148, 149, 150, 151, 85, 86, 84, 144, 145, 8, 37, 89, 75, 38, 122, 11, 12, 44, 42, 43, 137, 118, 123, 124, 138, 134, 129, 10, 115, 41, 139, 140, 141, 60, 61, 128, 71, 72, 125, 62, 63, 131, 135, 136, 19, 48, 133, 50], "values": [0.6814258098602295, 0.6722895503044128, 0.6695839166641235, 0.6259387135505676, 0.6024741530418396, 0.5824971199035645, 0.5824971199035645, 0.5824971199035645, 0.5680152177810669, 0.5680152177810669, 0.4911673069000244, 0.4871631860733032, 0.45073726773262024, 0.45073726773262024, 0.4268677830696106, 0.4268677830696106, 0.4268677830696106, 0.376714289188385, 0.3537614941596985, 0.34439730644226074, 0.33253952860832214, 0.3217625916004181, 0.3176993727684021, 0.3176993727684021, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.2996395528316498, 0.28956639766693115, 0.2835095524787903, 0.2568712830543518, 0.2568712830543518, 0.2568712830543518, 0.2430286705493927, 0.2380228042602539, 0.2379739135503769, 0.2350529432296753, 0.22470572590827942, 0.22204771637916565, 0.22017446160316467, 0.22017446160316467, 0.22016121447086334, 0.21909378468990326, 0.20961999893188477, 0.20961999893188477, 0.2049424648284912, 0.20417499542236328, 0.20354653894901276, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19900810718536377, 0.19900810718536377, 0.19178065657615662, 0.18584679067134857, 0.18584679067134857, 0.18242135643959045, 0.17864935100078583, 0.17281433939933777, 0.17222051322460175, 0.17136624455451965, 0.16804558038711548, 0.16254693269729614, 0.1495170295238495, 0.14899128675460815, 0.14316879212856293, 0.14316879212856293, 0.11986328661441803, 0.11767048388719559, 0.11767048388719559, 0.11767048388719559, 0.11017382144927979, 0.1099415123462677, 0.10691234469413757, 0.10325178503990173, 0.09600088745355606, 0.09595703333616257, 0.0950678139925003, 0.0950678139925003, 0.0950678139925003, 0.09348975121974945, 0.09348975121974945, 0.09135644137859344, 0.07336081564426422, 0.07336081564426422, 0.07084788382053375, 0.06675050407648087, 0.06675050407648087, 0.05971590802073479, 0.05762607976794243, 0.05762607976794243, 0.0509728267788887, 0.044816967099905014, 0.04220733046531677, 0.040773291140794754]}, "3": {"indices": [7, 388, 361, 390, 391, 5, 90, 385, 389, 141, 145, 209, 383, 239, 266, 267, 252, 30, 6, 8, 716, 955, 4024, 25, 3812, 4014, 9, 17, 26, 27, 253, 254, 1440, 956, 467, 650, 1083, 2075, 2101, 792, 793, 794, 3177, 465, 4008, 4009, 4324, 332, 3041, 3209, 3210, 3211, 3212, 3815, 4321, 29, 41, 338, 48, 2643, 392, 397, 150, 2889, 3695, 717, 4087, 4141, 4169, 2828, 31, 466, 368, 369, 370, 4267, 4293, 2347, 2348, 1447, 1448, 642, 1131, 651, 4028, 4029, 1487, 1495, 1496, 623, 723, 745, 89, 4092, 111, 244, 4048, 32, 1702, 811], "values": [0.998096227645874, 0.998096227645874, 0.96403568983078, 0.96403568983078, 0.96403568983078, 0.8484044671058655, 0.8484044671058655, 0.8484044671058655, 0.8484044671058655, 0.8338472843170166, 0.8338472843170166, 0.8338472843170166, 0.8338472843170166, 0.8323735594749451, 0.8323735594749451, 0.8323735594749451, 0.794776439666748, 0.7915468215942383, 0.7483518123626709, 0.7483518123626709, 0.7483518123626709, 0.7483518123626709, 0.7375943660736084, 0.7054058909416199, 0.6772313117980957, 0.6689291000366211, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6569876670837402, 0.6442821025848389, 0.6365141868591309, 0.6365141868591309, 0.6365141868591309, 0.629892110824585, 0.629892110824585, 0.6196168661117554, 0.6196168661117554, 0.6196168661117554, 0.6178841590881348, 0.6126296520233154, 0.6090183258056641, 0.6090183258056641, 0.6090183258056641, 0.5983366370201111, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5954737663269043, 0.5914321541786194, 0.5911408066749573, 0.5911408066749573, 0.5821852684020996, 0.582026481628418, 0.5792378187179565, 0.5788934230804443, 0.5788934230804443, 0.5758121609687805, 0.5700093507766724, 0.5624727010726929, 0.5617824196815491, 0.5612939596176147, 0.5612939596176147, 0.5605912208557129, 0.5588138103485107, 0.5572633743286133, 0.5572633743286133, 0.5537252426147461, 0.5537252426147461, 0.5537252426147461, 0.5429188013076782, 0.5429188013076782, 0.5398932695388794, 0.5398932695388794, 0.5395883917808533, 0.5395883917808533, 0.5388590693473816, 0.5358274579048157, 0.5333741307258606, 0.5283689498901367, 0.5283689498901367, 0.5277594923973083, 0.5265169143676758, 0.5265169143676758, 0.5252052545547485, 0.5252052545547485, 0.5252052545547485, 0.523034930229187, 0.5196403861045837, 0.5169724225997925, 0.5169724225997925, 0.5166342854499817, 0.514980673789978, 0.5144572257995605, 0.5087020397186279]}, "4": {"indices": [218, 227, 205, 49, 50, 84, 174, 113, 114, 117, 119, 120, 225, 85, 202, 156, 172, 135, 122, 128, 171, 10, 151, 242, 243, 170, 102, 199, 226, 145, 146, 169, 54, 115, 63, 65, 100, 109, 110, 111, 112, 232, 233, 4, 37, 53, 103, 104, 125, 139, 140, 38, 150, 0, 27, 29, 173, 93, 240, 206, 87, 97, 101, 106, 121, 123, 134, 141, 7, 45, 180, 223, 200, 86, 46, 9, 39, 60, 67, 71, 74, 75, 78, 79, 152, 153, 230, 196, 72, 229, 231, 47, 236, 213, 5, 6, 62, 12, 182, 183], "values": [0.6727880835533142, 0.6727880835533142, 0.6654847264289856, 0.6267021298408508, 0.6267021298408508, 0.6267021298408508, 0.6267021298408508, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.5948402881622314, 0.5423608422279358, 0.5334746837615967, 0.5155859589576721, 0.5155859589576721, 0.5112175941467285, 0.4868689179420471, 0.47872042655944824, 0.4711094796657562, 0.46946194767951965, 0.45085155963897705, 0.44897758960723877, 0.44897758960723877, 0.42708897590637207, 0.4212993383407593, 0.4209632873535156, 0.4142408072948456, 0.41334739327430725, 0.41334739327430725, 0.41242584586143494, 0.4122968912124634, 0.4077647924423218, 0.40774673223495483, 0.40774673223495483, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.40648454427719116, 0.40648454427719116, 0.40642672777175903, 0.4036613404750824, 0.4034668803215027, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3908003270626068, 0.3874143958091736, 0.3837318420410156, 0.3837318420410156, 0.3828738331794739, 0.37821635603904724, 0.3762022852897644, 0.3726189136505127, 0.3642114996910095, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.3393932580947876, 0.33476537466049194, 0.330272376537323, 0.3265085220336914, 0.3195851147174835, 0.31815898418426514, 0.3178529143333435, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.31331169605255127, 0.31331169605255127, 0.3130655288696289, 0.3094156086444855, 0.30834561586380005, 0.30727407336235046, 0.3070598840713501, 0.3057669401168823, 0.30340784788131714, 0.2987590730190277, 0.2951938509941101, 0.2951938509941101, 0.2936801314353943, 0.2928728461265564, 0.2914232909679413, 0.2914232909679413]}, "5": {"indices": [853, 854, 860, 920, 1230, 1833, 1875, 2091, 3871, 4120, 3870, 3872, 3873, 2185, 4071, 4473, 2176, 2215, 2216, 2320, 2433, 2481, 2577, 2695, 2697, 2698, 2730, 2731, 2732, 2733, 2855, 2859, 2863, 4155, 4465, 923, 960, 1002, 1020, 4184, 2217, 2208, 4353, 290, 4701, 2615, 2646, 2683, 2692, 4190, 4191, 4192, 2589, 2629, 2640, 2693, 2696, 2701, 2704, 2713, 2716, 2754, 2767, 2786, 4349, 4606, 4624, 4426, 1219, 3266, 3536, 3544, 3548, 3549, 4026, 3546, 2717, 2777, 2785, 3419, 3422, 3440, 4565, 4584, 4607, 4608, 4622, 4826, 3072, 3600, 4699, 3925, 3928, 4189, 4639, 17, 4099, 782, 3541, 3545], "values": [0.997597336769104, 0.997597336769104, 0.997597336769104, 0.997597336769104, 0.9653436541557312, 0.8154235482215881, 0.8154235482215881, 0.7734938859939575, 0.6917835474014282, 0.6856277585029602, 0.6541063785552979, 0.6541063785552979, 0.6541063785552979, 0.6524826884269714, 0.6469953060150146, 0.6469953060150146, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6395663022994995, 0.6333271861076355, 0.622814416885376, 0.622814416885376, 0.622814416885376, 0.622814416885376, 0.6091465950012207, 0.6068289279937744, 0.6058014631271362, 0.6032325029373169, 0.6014668345451355, 0.6011438965797424, 0.6000787615776062, 0.6000787615776062, 0.6000787615776062, 0.6000787615776062, 0.598292350769043, 0.598292350769043, 0.598292350769043, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5893754959106445, 0.5871177911758423, 0.5871177911758423, 0.5850570797920227, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5722397565841675, 0.5717297196388245, 0.5717297196388245, 0.5717297196388245, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5585320591926575, 0.5538491606712341, 0.5538491606712341, 0.5519065856933594, 0.5479217171669006, 0.5479217171669006, 0.5475368499755859, 0.544042706489563, 0.540373682975769, 0.53767991065979, 0.5348735451698303, 0.5346747040748596, 0.5346747040748596]}, "6": {"indices": [284, 240, 143, 144, 146, 147, 148, 827, 285, 145, 3236, 1051, 3060, 1014, 1501, 1502, 149, 327, 328, 329, 2767, 87, 4094, 4095, 4097, 4098, 4313, 996, 1040, 1060, 3858, 2345, 2489, 2492, 2493, 2494, 1710, 2497, 1086, 3497, 3221, 3222, 3474, 3475, 3479, 3480, 116, 2791, 3069, 3070, 3071, 3073, 3081, 3082, 3133, 3134, 3231, 4542, 927, 1767, 115, 4491, 3176, 4656, 4742, 4857, 4500, 1008, 289, 834, 835, 826, 527, 778, 781, 1080, 531, 602, 615, 2760, 3784, 2811, 3137, 3140, 553, 554, 556, 557, 558, 75, 2064, 2065, 2137, 2714, 828, 2523, 2524, 2525, 3477, 3233], "values": [0.7299851179122925, 0.669043779373169, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6370207071304321, 0.6262259483337402, 0.5562720894813538, 0.5162757635116577, 0.510110080242157, 0.49499964714050293, 0.4822402000427246, 0.4616070091724396, 0.4616070091724396, 0.4592355489730835, 0.4427684545516968, 0.4427684545516968, 0.4427684545516968, 0.42584389448165894, 0.42430925369262695, 0.4167355000972748, 0.4167355000972748, 0.4167355000972748, 0.4167355000972748, 0.4137309491634369, 0.41143158078193665, 0.41143158078193665, 0.41143158078193665, 0.3971516191959381, 0.3949267864227295, 0.3925682306289673, 0.3925682306289673, 0.3925682306289673, 0.3925682306289673, 0.39021700620651245, 0.3899136185646057, 0.3894136846065521, 0.3881523013114929, 0.38810819387435913, 0.38810819387435913, 0.38710981607437134, 0.38710981607437134, 0.38710981607437134, 0.38710981607437134, 0.3845381736755371, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38258543610572815, 0.3819728493690491, 0.3819728493690491, 0.3819403350353241, 0.38018208742141724, 0.3773539364337921, 0.3761323392391205, 0.3761323392391205, 0.3761323392391205, 0.37604665756225586, 0.37510091066360474, 0.37265336513519287, 0.3708555996417999, 0.3708555996417999, 0.36594754457473755, 0.3631155490875244, 0.3631155490875244, 0.3631155490875244, 0.35949429869651794, 0.35869044065475464, 0.3522747755050659, 0.3522747755050659, 0.3518388271331787, 0.3494141101837158, 0.34681546688079834, 0.34681546688079834, 0.34681546688079834, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.3431001305580139, 0.3427882790565491, 0.3427882790565491, 0.3427354097366333, 0.3427330255508423, 0.3412012457847595, 0.3410893678665161, 0.3410893678665161, 0.3410893678665161, 0.3374759554862976, 0.33630362153053284]}, "7": {"indices": [34, 7, 11, 14, 123, 12, 46, 33, 36, 41, 42, 13, 15, 29, 43, 35, 104, 30, 98, 8, 9, 77, 6, 207, 31, 27, 28, 37, 172, 40, 137, 138, 139, 140, 68, 63, 99, 193, 184, 112, 113, 32, 155, 91, 26, 10, 25, 199, 200, 201, 39, 158, 159, 160, 161, 162, 163, 52, 54, 55, 56, 57, 210, 110, 118, 108, 150, 152, 153, 97, 89, 24, 194, 180, 94, 197, 93, 44, 58, 213, 59, 38, 166, 174, 5, 96, 53, 50, 121, 122, 212, 103, 105, 164, 205, 71, 101, 100, 45, 188], "values": [0.8355023264884949, 0.681782603263855, 0.6296814680099487, 0.6296814680099487, 0.6047410368919373, 0.6017360091209412, 0.5437449216842651, 0.5329038500785828, 0.5220320820808411, 0.5220320820808411, 0.5220320820808411, 0.5126869678497314, 0.4902424216270447, 0.47889798879623413, 0.4739780128002167, 0.4519338309764862, 0.4385482668876648, 0.42581817507743835, 0.4230218529701233, 0.41863685846328735, 0.41278326511383057, 0.4037785530090332, 0.3941415846347809, 0.38613197207450867, 0.3732340931892395, 0.3725154995918274, 0.3725154995918274, 0.3567515015602112, 0.3514118790626526, 0.34868812561035156, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.3266542851924896, 0.3079492747783661, 0.30712729692459106, 0.299442321062088, 0.2974482476711273, 0.295329213142395, 0.295329213142395, 0.2951374650001526, 0.2871512174606323, 0.2805454134941101, 0.27769935131073, 0.2767360806465149, 0.2754376530647278, 0.2736871838569641, 0.2736871838569641, 0.2736871838569641, 0.2677212953567505, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.2632717192173004, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.2615187168121338, 0.2609832286834717, 0.2609832286834717, 0.25709396600723267, 0.25516828894615173, 0.25516828894615173, 0.25516828894615173, 0.2513769268989563, 0.2447986900806427, 0.24375922977924347, 0.23914113640785217, 0.23304912447929382, 0.22976645827293396, 0.22899697721004486, 0.22809219360351562, 0.22308345139026642, 0.2228611409664154, 0.22057557106018066, 0.2191094160079956, 0.21338146924972534, 0.21199211478233337, 0.21046307682991028, 0.1994990110397339, 0.19864699244499207, 0.19803595542907715, 0.19719743728637695, 0.1955522894859314, 0.1955522894859314, 0.19510281085968018, 0.19480031728744507, 0.19480031728744507, 0.18750925362110138, 0.18647556006908417, 0.176116481423378, 0.17452888190746307, 0.1719837784767151, 0.17080917954444885, 0.1696484535932541]}, "8": {"indices": [446, 473, 575, 597, 604, 605, 606, 607, 614, 461, 1433, 1700, 1348, 611, 1571, 1572, 1573, 412, 413, 414, 627, 621, 132, 133, 525, 526, 130, 1944, 245, 248, 1166, 425, 422, 1943, 2193, 1930, 472, 1781, 145, 146, 127, 1462, 1463, 1845, 894, 2002, 2003, 2014, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 444, 445, 468, 469, 470, 1295, 1296, 1297, 1298, 128, 154, 1379, 1975, 148, 149, 343, 1299, 925, 1658, 2683, 1984, 1993, 2012, 2039, 618, 619, 653, 654, 1522, 1739, 1784, 1785, 1787, 1788, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 198, 1211], "values": [0.902949333190918, 0.902949333190918, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8544977307319641, 0.7976263761520386, 0.7705298662185669, 0.754043698310852, 0.7449561357498169, 0.7004261016845703, 0.7004261016845703, 0.7004261016845703, 0.6543985605239868, 0.6543985605239868, 0.6543985605239868, 0.6541460156440735, 0.6469646692276001, 0.6374890208244324, 0.6374890208244324, 0.6261798143386841, 0.6261798143386841, 0.5893785953521729, 0.5725457668304443, 0.5655273199081421, 0.5655273199081421, 0.552666187286377, 0.5463614463806152, 0.5361401438713074, 0.5359000563621521, 0.5166508555412292, 0.515923023223877, 0.5067808628082275, 0.5060935616493225, 0.5055873990058899, 0.5055873990058899, 0.5048724412918091, 0.5048724412918091, 0.5048724412918091, 0.5004656314849854, 0.49906668066978455, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.483339786529541, 0.483339786529541, 0.483339786529541, 0.483339786529541, 0.4807903468608856, 0.4807903468608856, 0.46956610679626465, 0.46956610679626465, 0.46239835023880005, 0.46239835023880005, 0.4590466022491455, 0.45570850372314453, 0.44624263048171997, 0.4413162171840668, 0.4395539164543152, 0.43554890155792236, 0.43554890155792236, 0.43554890155792236, 0.43554890155792236, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.43139150738716125, 0.4235597252845764]}, "9": {"indices": [1572, 4631, 4562, 4033, 4217, 2593, 2672, 2592, 2228, 2694, 2697, 36, 2226, 2227, 3384, 780, 440, 505, 3957, 31, 580, 588, 504, 4755, 4759, 626, 2710, 175, 2201, 1771, 39, 4136, 581, 587, 828, 829, 830, 1092, 4260, 2229, 2556, 2714, 2184, 3918, 4102, 4103, 4129, 4757, 4758, 4762, 4241, 4187, 4188, 1543, 2669, 383, 15, 4286, 4101, 4104, 2234, 30, 525, 619, 620, 1090, 3383, 377, 506, 507, 658, 437, 439, 3142, 549, 508, 509, 1732, 4756, 4761, 1978, 3563, 3564, 784, 1948, 2061, 4429, 1697, 3933, 55, 1836, 625, 1655, 2327, 953, 954, 4470, 4477, 4478, 4479], "values": [0.9976937770843506, 0.8739716410636902, 0.8480227589607239, 0.7937148213386536, 0.7937148213386536, 0.6930517554283142, 0.6842337846755981, 0.6835708618164062, 0.6583209037780762, 0.6583209037780762, 0.6583209037780762, 0.6419944763183594, 0.6419944763183594, 0.6419944763183594, 0.6286375522613525, 0.5992338061332703, 0.5823986530303955, 0.5785138607025146, 0.5758242011070251, 0.5682200789451599, 0.5628348588943481, 0.5628348588943481, 0.5565475225448608, 0.5520271062850952, 0.5520271062850952, 0.548314094543457, 0.5477088689804077, 0.539419412612915, 0.5339654684066772, 0.5151779651641846, 0.5073480010032654, 0.507339358329773, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.4911983609199524, 0.4906250834465027, 0.4906250834465027, 0.4906250834465027, 0.4868679940700531, 0.47855913639068604, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47748497128486633, 0.476895272731781, 0.476895272731781, 0.47655922174453735, 0.47655922174453735, 0.4758196473121643, 0.46803754568099976, 0.4650793671607971, 0.46462029218673706, 0.46462029218673706, 0.4608319401741028, 0.4583824872970581, 0.4579876959323883, 0.45542824268341064, 0.45542824268341064, 0.45382043719291687, 0.453685462474823, 0.4494936168193817, 0.4494936168193817, 0.4494936168193817, 0.4494936168193817, 0.44627612829208374, 0.44627612829208374, 0.44188714027404785, 0.4414485692977905, 0.43964263796806335, 0.43964263796806335, 0.43504583835601807, 0.43389472365379333, 0.43244120478630066, 0.41538047790527344, 0.4121907949447632, 0.4121907949447632, 0.4105769395828247, 0.4105769395828247, 0.4105769395828247, 0.4105769395828247, 0.40724247694015503, 0.4063498377799988, 0.4035816192626953, 0.40241479873657227, 0.4008207321166992, 0.4008207321166992, 0.39952653646469116, 0.39943891763687134, 0.39943891763687134, 0.39904430508613586, 0.39904430508613586, 0.39904430508613586, 0.39904430508613586]}, "10": {"indices": [190, 195, 207, 967, 390, 1274, 1593, 1231, 1267, 1273, 1302, 1313, 1329, 1429, 1453, 1475, 619, 1214, 1596, 576, 682, 722, 1581, 1582, 1594, 1120, 201, 205, 206, 1584, 1261, 1268, 1449, 1481, 1493, 1625, 1627, 1630, 723, 812, 1373, 607, 613, 720, 429, 658, 498, 1597, 1679, 236, 374, 393, 396, 451, 509, 510, 513, 556, 569, 570, 575, 582, 602, 614, 617, 639, 642, 643, 714, 719, 1352, 1400, 1599, 1601, 1600, 1452, 1476, 430, 1149, 1180, 1207, 1211, 452, 1263, 1619, 1441, 131, 139, 1612, 1628, 1605, 679, 691, 724, 743, 748, 790, 800, 809, 819], "values": [0.997897744178772, 0.997897744178772, 0.997897744178772, 0.8982410430908203, 0.89687180519104, 0.89687180519104, 0.89687180519104, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8817000389099121, 0.8817000389099121, 0.8817000389099121, 0.8746421933174133, 0.8746421933174133, 0.8746421933174133, 0.8609449863433838, 0.8609449863433838, 0.8609449863433838, 0.8599758148193359, 0.8591632843017578, 0.8591632843017578, 0.8591632843017578, 0.844992995262146, 0.843886137008667, 0.843886137008667, 0.8422084450721741, 0.8422084450721741, 0.8422084450721741, 0.8406334519386292, 0.8406334519386292, 0.8406334519386292, 0.8389067649841309, 0.8389067649841309, 0.8385676741600037, 0.8383581042289734, 0.8383581042289734, 0.8383581042289734, 0.8373757004737854, 0.8373757004737854, 0.8321995735168457, 0.8317316770553589, 0.8317316770553589, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8298993110656738, 0.8297579884529114, 0.8266054391860962, 0.8266054391860962, 0.8260214328765869, 0.8247941732406616, 0.8247941732406616, 0.8217582702636719, 0.8191503286361694, 0.8191503286361694, 0.8191503286361694, 0.8191503286361694, 0.8171231746673584, 0.8138290643692017, 0.8082481026649475, 0.8061050176620483, 0.8055320978164673, 0.8055320978164673, 0.8048208355903625, 0.8048208355903625, 0.804445207118988, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782]}, "11": {"indices": [385, 1541, 481, 485, 486, 505, 515, 689, 988, 406, 1579, 1613, 375, 380, 386, 1682, 1328, 1414, 1415, 1417, 473, 1375, 1391, 873, 874, 1266, 881, 1461, 1117, 940, 412, 421, 422, 423, 427, 428, 435, 449, 478, 480, 1270, 1314, 1330, 1402, 1430, 1454, 1466, 1467, 1468, 1530, 442, 968, 1499, 1673, 1707, 408, 1508, 1543, 1306, 418, 447, 893, 757, 1133, 499, 69, 1708, 1199, 1200, 439, 29, 30, 52, 53, 54, 1222, 1573, 1662, 1463, 1478, 1496, 1552, 1555, 1558, 1559, 1576, 1587, 376, 1225, 1226, 1227, 584, 22, 67, 68, 75, 311, 490, 1519, 1534], "values": [0.9980266094207764, 0.972307562828064, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.9466214179992676, 0.9466214179992676, 0.9445673227310181, 0.9385941028594971, 0.9385941028594971, 0.9385941028594971, 0.9351229667663574, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9144363403320312, 0.9061398506164551, 0.9061398506164551, 0.9003400802612305, 0.9003400802612305, 0.8952523469924927, 0.8945959806442261, 0.8901271820068359, 0.8890048265457153, 0.8869749307632446, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8571504354476929, 0.8571504354476929, 0.8571504354476929, 0.8489366769790649, 0.8447636365890503, 0.8447636365890503, 0.8447636365890503, 0.8443904519081116, 0.841084361076355, 0.8402484655380249, 0.8300219178199768, 0.8292279839515686, 0.8286341428756714, 0.8286341428756714, 0.8262461423873901, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8257873058319092, 0.8237709999084473, 0.8227684497833252, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.819891095161438, 0.8193692564964294, 0.8193692564964294, 0.8193692564964294, 0.8189754486083984, 0.8184829354286194, 0.8184688687324524, 0.8184688687324524, 0.8184688687324524, 0.8181168437004089, 0.817807674407959, 0.8173049688339233, 0.8173049688339233]}, "12": {"indices": [224, 225, 226, 221, 227, 218, 219, 220, 170, 198, 185, 210, 169, 183, 76, 70, 73, 233, 280, 300, 330, 361, 362, 16, 223, 376, 128, 33, 43, 146, 358, 106, 176, 239, 192, 272, 273, 274, 277, 360, 147, 209, 62, 126, 196, 387, 388, 389, 390, 391, 392, 2, 186, 112, 113, 39, 64, 68, 153, 154, 199, 350, 351, 211, 278, 152, 355, 177, 56, 44, 322, 377, 378, 250, 18, 156, 157, 174, 114, 117, 119, 120, 175, 144, 30, 31, 32, 28, 59, 332, 247, 51, 27, 71, 72, 327, 96, 93, 214, 248], "values": [0.8441696166992188, 0.8441696166992188, 0.8441696166992188, 0.6860100626945496, 0.6860100626945496, 0.6748876571655273, 0.6748876571655273, 0.6748876571655273, 0.5475037097930908, 0.5125709176063538, 0.5025202631950378, 0.4915836453437805, 0.48502081632614136, 0.4622817635536194, 0.4584325850009918, 0.4546782970428467, 0.4546782970428467, 0.45419225096702576, 0.4423740804195404, 0.44224774837493896, 0.42768895626068115, 0.42768895626068115, 0.42768895626068115, 0.42766499519348145, 0.40943682193756104, 0.4092572033405304, 0.3999294638633728, 0.394265353679657, 0.3896658420562744, 0.382973849773407, 0.37797874212265015, 0.37291574478149414, 0.36719292402267456, 0.36514878273010254, 0.3624955117702484, 0.3624374270439148, 0.3624374270439148, 0.3624374270439148, 0.3624374270439148, 0.3582462668418884, 0.3581291437149048, 0.3581291437149048, 0.3513772785663605, 0.3512735962867737, 0.3512735962867737, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.34719908237457275, 0.34667831659317017, 0.34138110280036926, 0.34138110280036926, 0.3380025029182434, 0.3364969491958618, 0.3360591232776642, 0.3356434404850006, 0.3356434404850006, 0.33248603343963623, 0.3265199661254883, 0.3265199661254883, 0.3217012584209442, 0.3167683482170105, 0.3148108124732971, 0.3144241273403168, 0.3139753043651581, 0.3092193007469177, 0.3000544011592865, 0.2933530807495117, 0.2927124500274658, 0.2927124500274658, 0.29259634017944336, 0.2903120815753937, 0.2876487970352173, 0.2876487970352173, 0.2876487970352173, 0.28751710057258606, 0.2872055470943451, 0.2872055470943451, 0.2872055470943451, 0.2870335876941681, 0.2834073007106781, 0.28044554591178894, 0.28044554591178894, 0.28044554591178894, 0.2798529267311096, 0.278662770986557, 0.27781033515930176, 0.2761780321598053, 0.27488455176353455, 0.27470386028289795, 0.27428141236305237, 0.27428141236305237, 0.2736201882362366, 0.2731330990791321, 0.27218911051750183, 0.27175137400627136, 0.27164822816848755]}, "13": {"indices": [258, 255, 259, 257, 260, 520, 256, 496, 561, 2819, 481, 512, 513, 556, 557, 2461, 2815, 2816, 276, 558, 559, 986, 1014, 1016, 1017, 1018, 1019, 1021, 1026, 1027, 1031, 1032, 1033, 1034, 1036, 1037, 1038, 1847, 590, 585, 279, 212, 213, 281, 425, 2858, 262, 1364, 516, 509, 1050, 1053, 253, 274, 699, 700, 514, 216, 2508, 2242, 2594, 1973, 1974, 1975, 2111, 2112, 2007, 2009, 2440, 277, 278, 237, 2356, 1758, 1759, 2865, 2867, 2868, 471, 2822, 261, 2826, 1009, 2551, 2552, 1013, 1015, 1020, 1022, 1023, 1025, 1028, 1029, 1030, 988, 540, 287, 1361, 1363, 1830], "values": [0.648378312587738, 0.5952406525611877, 0.5797232389450073, 0.5584985017776489, 0.5576417446136475, 0.5485743284225464, 0.5472137928009033, 0.5427061915397644, 0.5205211639404297, 0.5108038187026978, 0.5072110891342163, 0.5050419569015503, 0.5050419569015503, 0.5011329650878906, 0.5011329650878906, 0.4985601007938385, 0.476604700088501, 0.476604700088501, 0.4711463451385498, 0.4683162569999695, 0.4683162569999695, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4637238085269928, 0.45465224981307983, 0.4532126486301422, 0.44437867403030396, 0.4424690008163452, 0.4424690008163452, 0.4381462335586548, 0.43698686361312866, 0.43602001667022705, 0.4339509606361389, 0.43089741468429565, 0.4306219220161438, 0.4233730435371399, 0.4233730435371399, 0.4233730435371399, 0.4228084087371826, 0.41561564803123474, 0.41540253162384033, 0.41540253162384033, 0.4148055613040924, 0.4109310507774353, 0.410712867975235, 0.4086611270904541, 0.4067486822605133, 0.40605926513671875, 0.40605926513671875, 0.40605926513671875, 0.4056534767150879, 0.4056534767150879, 0.4044385254383087, 0.4044385254383087, 0.40214642882347107, 0.39955267310142517, 0.39955267310142517, 0.3926612138748169, 0.3914484679698944, 0.3897818922996521, 0.3897818922996521, 0.3864520490169525, 0.3864520490169525, 0.3864520490169525, 0.3850623071193695, 0.38275575637817383, 0.38257354497909546, 0.3818899691104889, 0.38085299730300903, 0.37997400760650635, 0.37997400760650635, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3744429349899292, 0.37304556369781494, 0.37060385942459106, 0.3695283532142639, 0.3695283532142639, 0.36630064249038696]}, "14": {"indices": [274, 276, 2311, 467, 468, 254, 2387, 256, 2380, 509, 1050, 1053, 255, 2895, 478, 540, 1989, 1990, 1994, 1995, 1999, 2000, 2455, 1046, 1047, 1048, 275, 286, 2869, 281, 287, 489, 1581, 2551, 2552, 2322, 522, 267, 268, 269, 270, 271, 441, 731, 285, 2385, 2386, 733, 288, 2508, 2349, 279, 884, 2506, 263, 264, 940, 2146, 280, 2094, 2095, 277, 278, 252, 457, 283, 403, 648, 558, 559, 730, 732, 300, 1996, 258, 273, 772, 1100, 460, 461, 212, 213, 487, 726, 727, 728, 729, 1991, 1992, 1993, 1051, 472, 2105, 297, 262, 2027, 2039, 2040, 2044, 2046], "values": [0.6430260539054871, 0.6052654981613159, 0.604028582572937, 0.5947726964950562, 0.5947726964950562, 0.5943450331687927, 0.5777553915977478, 0.5759397745132446, 0.5676916837692261, 0.5478818416595459, 0.5478818416595459, 0.5478818416595459, 0.5465532541275024, 0.5372047424316406, 0.5337477922439575, 0.5279549360275269, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5106783509254456, 0.5016142725944519, 0.5016142725944519, 0.5016142725944519, 0.4961423873901367, 0.4960876405239105, 0.4952925443649292, 0.4927310049533844, 0.48135092854499817, 0.47914645075798035, 0.4720046818256378, 0.47103220224380493, 0.47103220224380493, 0.4698319137096405, 0.4657186269760132, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641437232494354, 0.4612700939178467, 0.45961397886276245, 0.45837944746017456, 0.45837944746017456, 0.45672479271888733, 0.45616239309310913, 0.4490036368370056, 0.4445439577102661, 0.4364176094532013, 0.43322116136550903, 0.43124422430992126, 0.42809155583381653, 0.42809155583381653, 0.42677149176597595, 0.4253457188606262, 0.42430466413497925, 0.4229259490966797, 0.4229259490966797, 0.422419011592865, 0.422419011592865, 0.4216668903827667, 0.42151808738708496, 0.4204244017601013, 0.4168950915336609, 0.4128885567188263, 0.41163143515586853, 0.41163143515586853, 0.4114909768104553, 0.4114909768104553, 0.40756136178970337, 0.4066689610481262, 0.3984348177909851, 0.3983434736728668, 0.39788639545440674, 0.39610159397125244, 0.3896326422691345, 0.3896326422691345, 0.3888190984725952, 0.3888190984725952, 0.3887830376625061, 0.38681942224502563, 0.38681942224502563, 0.38681942224502563, 0.38681942224502563, 0.3853195309638977, 0.3853195309638977, 0.3853195309638977, 0.3852720558643341, 0.38502731919288635, 0.3836010992527008, 0.3786434531211853, 0.378050297498703, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786]}, "15": {"indices": [553, 2753, 466, 2089, 2440, 756, 2503, 2547, 2140, 2048, 2389, 2390, 1361, 1363, 1821, 1840, 906, 1191, 228, 229, 232, 233, 234, 235, 236, 2608, 458, 469, 1107, 1277, 1278, 1316, 1582, 1583, 1100, 910, 911, 1024, 1035, 1164, 1165, 1166, 909, 2716, 238, 243, 244, 1248, 1249, 1264, 2352, 349, 174, 175, 2182, 2198, 2199, 2200, 725, 344, 405, 1644, 757, 770, 807, 847, 848, 849, 850, 851, 852, 853, 854, 862, 863, 864, 2695, 2849, 414, 1796, 549, 555, 2592, 982, 772, 444, 507, 508, 368, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 242, 1088, 682, 1106], "values": [0.6607229113578796, 0.6383124589920044, 0.590969443321228, 0.5166010856628418, 0.4858936369419098, 0.4634116590023041, 0.455611914396286, 0.45379626750946045, 0.453165739774704, 0.4498156011104584, 0.44659388065338135, 0.44659388065338135, 0.4438987076282501, 0.4438987076282501, 0.44376248121261597, 0.44376248121261597, 0.44337883591651917, 0.43862152099609375, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.43546491861343384, 0.43113893270492554, 0.43113893270492554, 0.4298458695411682, 0.4279545843601227, 0.4279545843601227, 0.4279545843601227, 0.4266261160373688, 0.4266261160373688, 0.42637932300567627, 0.4245290756225586, 0.4245290756225586, 0.4168010354042053, 0.4168010354042053, 0.4134634733200073, 0.4134634733200073, 0.4134634733200073, 0.4113650918006897, 0.4066130816936493, 0.40506765246391296, 0.40506765246391296, 0.40506765246391296, 0.39771637320518494, 0.39771637320518494, 0.39771637320518494, 0.3947366774082184, 0.39341580867767334, 0.39306145906448364, 0.39306145906448364, 0.391987681388855, 0.391987681388855, 0.391987681388855, 0.391987681388855, 0.38953697681427, 0.38903433084487915, 0.38903433084487915, 0.3882913887500763, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.38701868057250977, 0.3866688013076782, 0.3866102397441864, 0.38591915369033813, 0.3845248222351074, 0.3845248222351074, 0.3833513855934143, 0.38077622652053833, 0.3805825710296631, 0.38051676750183105, 0.38051676750183105, 0.38051676750183105, 0.3767510950565338, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.3745117783546448, 0.37425705790519714, 0.37159454822540283, 0.3709631860256195]}, "16": {"indices": [347, 3310, 3314, 169, 3399, 1846, 1847, 4412, 3122, 1850, 1852, 1855, 1856, 3431, 4854, 3866, 957, 3304, 3305, 3307, 819, 4636, 760, 958, 3937, 1849, 1857, 1858, 3867, 4629, 78, 79, 80, 81, 331, 1848, 3105, 0, 10, 1987, 4832, 1862, 3876, 1939, 3933, 3942, 3944, 3945, 4518, 4562, 4627, 4630, 4834, 3939, 3940, 1827, 4136, 762, 763, 767, 3125, 3946, 4576, 3943, 2625, 4138, 4139, 3745, 3202, 2851, 2852, 2857, 2859, 1829, 961, 4821, 1890, 1892, 4392, 2503, 2447, 2451, 3301, 2647, 3112, 3103, 3306, 1029, 2061, 2062, 2332, 3123, 1828, 2392, 2393, 3104, 1284, 1445, 2498, 2320], "values": [0.8800671100616455, 0.6704864501953125, 0.6704864501953125, 0.6600865125656128, 0.6494617462158203, 0.6312734484672546, 0.6312734484672546, 0.6287825107574463, 0.6251044273376465, 0.6052433252334595, 0.6052433252334595, 0.6052433252334595, 0.6052433252334595, 0.5980453491210938, 0.5862072706222534, 0.5839033126831055, 0.5829372406005859, 0.5820208787918091, 0.5816234946250916, 0.5816234946250916, 0.5814765095710754, 0.5795917510986328, 0.579427182674408, 0.579427182674408, 0.5757958292961121, 0.5745910406112671, 0.5745910406112671, 0.5745910406112671, 0.5743648409843445, 0.571414589881897, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5611867308616638, 0.5540376305580139, 0.5439808964729309, 0.5439808964729309, 0.5439808964729309, 0.5411112308502197, 0.5410830974578857, 0.539827823638916, 0.5377488136291504, 0.5350841283798218, 0.5345295071601868, 0.5345295071601868, 0.5345295071601868, 0.5342665910720825, 0.5342665910720825, 0.5342665910720825, 0.5342665910720825, 0.532304584980011, 0.5306636691093445, 0.5306636691093445, 0.5270891189575195, 0.5246263146400452, 0.5239983797073364, 0.5239983797073364, 0.5239983797073364, 0.5221589207649231, 0.5210816264152527, 0.5159024000167847, 0.5156014561653137, 0.5153573155403137, 0.5141280889511108, 0.5141280889511108, 0.5127895474433899, 0.5085676312446594, 0.5078423619270325, 0.5078423619270325, 0.5078423619270325, 0.5078423619270325, 0.5075790882110596, 0.5056939125061035, 0.5041319727897644, 0.5009607672691345, 0.5009607672691345, 0.49849918484687805, 0.497590035200119, 0.49722155928611755, 0.49722155928611755, 0.49607378244400024, 0.49588140845298767, 0.4958782196044922, 0.4955293536186218, 0.49012184143066406, 0.4893382787704468, 0.4848805367946625, 0.4848805367946625, 0.4848805367946625, 0.48431292176246643, 0.48425307869911194, 0.4833680987358093, 0.4833680987358093, 0.4829413890838623, 0.4812370836734772, 0.4805285930633545, 0.4770299792289734, 0.4758847951889038]}, "17": {"indices": [29, 18, 8, 10, 24, 31, 22, 32, 6, 7, 19, 20, 11, 12, 25, 27, 21, 3, 28, 9, 34, 35, 26, 4, 23, 1, 2, 30, 0, 5, 13, 14, 15, 16, 17, 33], "values": [0.5468879342079163, 0.4839995503425598, 0.4822302758693695, 0.4822302758693695, 0.4798535108566284, 0.4701407551765442, 0.45251086354255676, 0.44526946544647217, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.4345024824142456, 0.4345024824142456, 0.4065087139606476, 0.3430609405040741, 0.32818904519081116, 0.319267213344574, 0.2979602515697479, 0.2974632978439331, 0.29507964849472046, 0.29507964849472046, 0.291653573513031, 0.2882101535797119, 0.2847745418548584, 0.2840638756752014, 0.2840638756752014, 0.17938299477100372, 0.163307324051857, 0.16136708855628967, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 24, 26, 6, 7, 19, 20, 8, 10, 31, 28, 9, 21, 27, 30, 29, 23, 25, 4, 11, 12, 1, 2, 5, 34, 35, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.7020464539527893, 0.6643305420875549, 0.6235466599464417, 0.6023511290550232, 0.5851036310195923, 0.5851036310195923, 0.5851036310195923, 0.5851036310195923, 0.5774502754211426, 0.5774502754211426, 0.5627787113189697, 0.5496057271957397, 0.5416020154953003, 0.5338107347488403, 0.5264153480529785, 0.5121941566467285, 0.4977238178253174, 0.4893684685230255, 0.4486841559410095, 0.44549816846847534, 0.44207727909088135, 0.44207727909088135, 0.4403478503227234, 0.4403478503227234, 0.41417911648750305, 0.39730221033096313, 0.39730221033096313, 0.36293449997901917, 0.34736812114715576, 0.2596661448478699, 0.14642265439033508, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [60, 61, 18, 79, 163, 161, 86, 87, 51, 52, 111, 57, 59, 119, 7, 43, 195, 210, 201, 206, 3, 105, 6, 62, 55, 175, 63, 64, 70, 215, 211, 58, 180, 208, 209, 172, 142, 143, 144, 126, 46, 69, 81, 189, 243, 65, 190, 2, 4, 5, 226, 194, 14, 16, 203, 120, 113, 74, 89, 198, 82, 207, 197, 110, 54, 80, 199, 181, 71, 124, 141, 23, 112, 114, 15, 233, 213, 234, 179, 42, 133, 128, 244, 212, 205, 196, 22, 103, 247, 237, 183, 214, 44, 236, 24, 27, 245, 122, 20, 200], "values": [0.8015655279159546, 0.8015655279159546, 0.800186276435852, 0.61016845703125, 0.5592929124832153, 0.5433822870254517, 0.5012836456298828, 0.5012836456298828, 0.48828208446502686, 0.48828208446502686, 0.47177058458328247, 0.44401758909225464, 0.44309577345848083, 0.4105152487754822, 0.39441967010498047, 0.3933994770050049, 0.37923234701156616, 0.36191999912261963, 0.35309937596321106, 0.34643805027008057, 0.33699870109558105, 0.3357974588871002, 0.33046865463256836, 0.30267563462257385, 0.2901327610015869, 0.28643521666526794, 0.27970343828201294, 0.27970343828201294, 0.2751466631889343, 0.267164409160614, 0.26096034049987793, 0.2465893179178238, 0.23076580464839935, 0.22697599232196808, 0.22697599232196808, 0.22587552666664124, 0.2134752869606018, 0.2134752869606018, 0.2134752869606018, 0.2103939950466156, 0.208261638879776, 0.19150957465171814, 0.18875542283058167, 0.18409103155136108, 0.18268805742263794, 0.18084567785263062, 0.1751769483089447, 0.17503681778907776, 0.17503681778907776, 0.17503681778907776, 0.17299127578735352, 0.16966332495212555, 0.16777139902114868, 0.16777139902114868, 0.16649237275123596, 0.16446571052074432, 0.1624225527048111, 0.15381458401679993, 0.15227308869361877, 0.1514841914176941, 0.148375004529953, 0.14314058423042297, 0.13890354335308075, 0.13805758953094482, 0.13666778802871704, 0.13666778802871704, 0.13666117191314697, 0.13298214972019196, 0.12461508810520172, 0.118744857609272, 0.118744857609272, 0.11287660151720047, 0.11221744120121002, 0.11221744120121002, 0.10249722748994827, 0.10192391276359558, 0.10059460252523422, 0.09994277358055115, 0.09781771153211594, 0.0939168706536293, 0.09305144846439362, 0.09253160655498505, 0.09212134033441544, 0.09025833010673523, 0.08891324698925018, 0.08774791657924652, 0.08358398824930191, 0.0821562260389328, 0.08053833246231079, 0.07908889651298523, 0.07271206378936768, 0.07227188348770142, 0.06770563125610352, 0.06676945090293884, 0.06549020111560822, 0.06529253721237183, 0.06423700600862503, 0.06327737867832184, 0.06179533526301384, 0.061229344457387924]}}

for i in range(20):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])

print('一致性检查')
# v1是v2 1min之后重新执行的结果
v1 = {"0": {"indices": [391, 682, 430, 392, 195, 754, 774, 775, 779, 781, 375, 393, 623, 178, 12, 787, 203, 205, 17, 85, 176, 177, 783, 784, 785, 428, 199, 200, 792, 404, 405, 614, 616, 617, 618, 789, 67, 106, 107, 807, 791, 531, 406, 149, 82, 83, 530, 630, 461, 465, 482, 483, 484, 541, 201, 196, 36, 170, 429, 213, 608, 162, 163, 148, 538, 169, 808, 670, 157, 862, 210, 211, 891, 893, 894, 764, 765, 395, 400, 401, 402, 403, 412, 413, 439, 450, 669, 101, 168, 665, 437, 609, 854, 285, 485, 525, 526, 557, 455, 228], "values": [0.634227991104126, 0.5791579484939575, 0.5569994449615479, 0.5523046851158142, 0.5470099449157715, 0.5457106828689575, 0.5457106828689575, 0.5457106828689575, 0.5335338711738586, 0.5335338711738586, 0.5325450897216797, 0.5325450897216797, 0.5082945227622986, 0.5031034350395203, 0.49784886837005615, 0.49738410115242004, 0.4895824193954468, 0.4895824193954468, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.4803156852722168, 0.4803156852722168, 0.4803156852722168, 0.44719216227531433, 0.44329220056533813, 0.44329220056533813, 0.43824338912963867, 0.43666914105415344, 0.43666914105415344, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.43399766087532043, 0.42945337295532227, 0.42945337295532227, 0.42945337295532227, 0.40701183676719666, 0.4068290591239929, 0.4050838053226471, 0.40103745460510254, 0.3955700993537903, 0.38879138231277466, 0.38879138231277466, 0.3796015679836273, 0.373837411403656, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.36885976791381836, 0.3659629821777344, 0.3619682192802429, 0.3513945937156677, 0.3505263924598694, 0.3496953845024109, 0.34440508484840393, 0.34231650829315186, 0.33986353874206543, 0.33986353874206543, 0.33520692586898804, 0.3319920301437378, 0.3302970826625824, 0.3302970826625824, 0.32643309235572815, 0.326144278049469, 0.3249531388282776, 0.32240742444992065, 0.32240742444992065, 0.31665271520614624, 0.31665271520614624, 0.31665271520614624, 0.31230077147483826, 0.31230077147483826, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30816277861595154, 0.3070964813232422, 0.3070964813232422, 0.30662038922309875, 0.3054530918598175, 0.30521929264068604, 0.3042910397052765, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29580268263816833, 0.29359209537506104]}, "1": {"indices": [62, 63, 66, 75, 77, 78, 79, 80, 81, 82, 83, 87, 88, 57, 58, 50, 76, 39, 1, 12, 130, 0, 36, 38, 44, 85, 86, 52, 51, 53, 54, 31, 89, 128, 67, 6, 148, 149, 150, 151, 49, 65, 133, 131, 129, 144, 145, 126, 98, 103, 105, 68, 69, 23, 71, 72, 122, 97, 99, 100, 101, 102, 106, 118, 123, 124, 115, 146, 116, 134, 8, 37, 84, 132, 48, 26, 27, 45, 42, 43, 138, 125, 73, 19, 15, 16, 18, 10, 25, 70, 17, 21, 22, 24, 139, 140, 141, 104, 40, 64], "values": [0.6368813514709473, 0.6368813514709473, 0.6130360960960388, 0.5421620011329651, 0.5375056266784668, 0.5375056266784668, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.46104589104652405, 0.46104589104652405, 0.4271812438964844, 0.4214109182357788, 0.4121088981628418, 0.4103316068649292, 0.40282025933265686, 0.38614192605018616, 0.3760111331939697, 0.3721415102481842, 0.36416345834732056, 0.3479672372341156, 0.3272571563720703, 0.3272571563720703, 0.3245006203651428, 0.3203641474246979, 0.31175732612609863, 0.31175732612609863, 0.2979270815849304, 0.2969631552696228, 0.29581066966056824, 0.2801106572151184, 0.2711765766143799, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.26397550106048584, 0.26295462250709534, 0.2629290223121643, 0.25873640179634094, 0.2566879689693451, 0.2504494786262512, 0.2504494786262512, 0.2430320680141449, 0.23912516236305237, 0.23912516236305237, 0.23912516236305237, 0.23463478684425354, 0.23463478684425354, 0.23280125856399536, 0.2311907708644867, 0.2311907708644867, 0.22300760447978973, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.21124772727489471, 0.21124772727489471, 0.21124772727489471, 0.2102745771408081, 0.20816147327423096, 0.20361432433128357, 0.2030743658542633, 0.19641417264938354, 0.1943962126970291, 0.1915108859539032, 0.18737488985061646, 0.18413467705249786, 0.17745643854141235, 0.17745643854141235, 0.17745643854141235, 0.1760486364364624, 0.1760486364364624, 0.17468427121639252, 0.1661725640296936, 0.16441266238689423, 0.15954509377479553, 0.15381674468517303, 0.15381674468517303, 0.15104883909225464, 0.14442673325538635, 0.13740898668766022, 0.13240142166614532, 0.13176646828651428, 0.12431777268648148, 0.12279310822486877, 0.12279310822486877, 0.12142190337181091, 0.12142190337181091, 0.12142190337181091, 0.11630198359489441, 0.11599351465702057, 0.11398488283157349]}, "2": {"indices": [34, 35, 23, 9, 46, 28, 29, 30, 22, 24, 21, 47, 32, 33, 26, 27, 45, 25, 0, 65, 6, 39, 13, 14, 79, 80, 81, 82, 83, 87, 88, 17, 147, 40, 3, 4, 5, 66, 36, 146, 1, 20, 7, 15, 16, 67, 31, 77, 78, 76, 132, 142, 148, 149, 150, 151, 85, 86, 84, 144, 145, 8, 37, 89, 75, 38, 122, 11, 12, 44, 42, 43, 137, 118, 123, 124, 138, 134, 129, 10, 115, 41, 139, 140, 141, 60, 61, 128, 71, 72, 125, 62, 63, 131, 135, 136, 19, 48, 133, 50], "values": [0.6814258098602295, 0.6722895503044128, 0.6695839166641235, 0.6259387135505676, 0.6024741530418396, 0.5824971199035645, 0.5824971199035645, 0.5824971199035645, 0.5680152177810669, 0.5680152177810669, 0.4911673069000244, 0.4871631860733032, 0.45073726773262024, 0.45073726773262024, 0.4268677830696106, 0.4268677830696106, 0.4268677830696106, 0.376714289188385, 0.3537614941596985, 0.34439730644226074, 0.33253952860832214, 0.3217625916004181, 0.3176993727684021, 0.3176993727684021, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.2996395528316498, 0.28956639766693115, 0.2835095524787903, 0.2568712830543518, 0.2568712830543518, 0.2568712830543518, 0.2430286705493927, 0.2380228042602539, 0.2379739135503769, 0.2350529432296753, 0.22470572590827942, 0.22204771637916565, 0.22017446160316467, 0.22017446160316467, 0.22016121447086334, 0.21909378468990326, 0.20961999893188477, 0.20961999893188477, 0.2049424648284912, 0.20417499542236328, 0.20354653894901276, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19900810718536377, 0.19900810718536377, 0.19178065657615662, 0.18584679067134857, 0.18584679067134857, 0.18242135643959045, 0.17864935100078583, 0.17281433939933777, 0.17222051322460175, 0.17136624455451965, 0.16804558038711548, 0.16254693269729614, 0.1495170295238495, 0.14899128675460815, 0.14316879212856293, 0.14316879212856293, 0.11986328661441803, 0.11767048388719559, 0.11767048388719559, 0.11767048388719559, 0.11017382144927979, 0.1099415123462677, 0.10691234469413757, 0.10325178503990173, 0.09600088745355606, 0.09595703333616257, 0.0950678139925003, 0.0950678139925003, 0.0950678139925003, 0.09348975121974945, 0.09348975121974945, 0.09135644137859344, 0.07336081564426422, 0.07336081564426422, 0.07084788382053375, 0.06675050407648087, 0.06675050407648087, 0.05971590802073479, 0.05762607976794243, 0.05762607976794243, 0.0509728267788887, 0.044816967099905014, 0.04220733046531677, 0.040773291140794754]}, "3": {"indices": [7, 388, 361, 390, 391, 5, 90, 385, 389, 141, 145, 209, 383, 239, 266, 267, 252, 30, 6, 8, 716, 955, 4024, 25, 3812, 4014, 9, 17, 26, 27, 253, 254, 1440, 956, 2075, 2101, 467, 650, 1083, 465, 3177, 792, 793, 794, 4008, 4009, 4324, 4321, 3041, 3209, 3210, 3211, 3212, 3815, 332, 29, 41, 2643, 150, 392, 397, 48, 338, 3695, 4169, 717, 2889, 4087, 4141, 31, 466, 368, 369, 370, 2828, 623, 723, 745, 1447, 1448, 2347, 2348, 4028, 4029, 1131, 4267, 4293, 1487, 642, 1495, 1496, 651, 4092, 89, 32, 4134, 4048, 4133, 811, 244], "values": [0.9979488253593445, 0.9979488253593445, 0.9651556015014648, 0.9651556015014648, 0.9651556015014648, 0.8522801995277405, 0.8522801995277405, 0.8522801995277405, 0.8522801995277405, 0.8378716111183167, 0.8378716111183167, 0.8378716111183167, 0.8378716111183167, 0.8356759548187256, 0.8356759548187256, 0.8356759548187256, 0.7974945902824402, 0.7929272651672363, 0.7550406455993652, 0.7550406455993652, 0.7550406455993652, 0.7550406455993652, 0.7442336082458496, 0.7125493884086609, 0.682521641254425, 0.6810397505760193, 0.6707931756973267, 0.6707931756973267, 0.6707931756973267, 0.6707931756973267, 0.6707931756973267, 0.6707931756973267, 0.659333348274231, 0.6556143760681152, 0.6417366862297058, 0.6417366862297058, 0.6368964910507202, 0.6368964910507202, 0.6368964910507202, 0.6167159080505371, 0.6137215495109558, 0.6115044951438904, 0.6115044951438904, 0.6115044951438904, 0.6112252473831177, 0.6112252473831177, 0.6112252473831177, 0.5957434773445129, 0.5957340002059937, 0.5957340002059937, 0.5957340002059937, 0.5957340002059937, 0.5957340002059937, 0.5954182147979736, 0.5948020219802856, 0.58990079164505, 0.58990079164505, 0.5860762000083923, 0.5847422480583191, 0.5843809247016907, 0.5843809247016907, 0.5823809504508972, 0.5820242166519165, 0.5816733837127686, 0.5700408816337585, 0.5685372352600098, 0.5682185888290405, 0.5649294257164001, 0.5649294257164001, 0.5591128468513489, 0.5591128468513489, 0.5554800629615784, 0.5554800629615784, 0.5554800629615784, 0.553481936454773, 0.5500681400299072, 0.5500681400299072, 0.5500681400299072, 0.5435666441917419, 0.5435666441917419, 0.5423916578292847, 0.5423916578292847, 0.5414538979530334, 0.5414538979530334, 0.5380361080169678, 0.5373247861862183, 0.5373247861862183, 0.5345026254653931, 0.5334666967391968, 0.5304746627807617, 0.5304746627807617, 0.5299727916717529, 0.5255539417266846, 0.52281653881073, 0.5219366550445557, 0.5196484327316284, 0.5161106586456299, 0.5152928233146667, 0.5140341520309448, 0.5117806792259216]}, "4": {"indices": [218, 227, 205, 49, 50, 84, 174, 113, 114, 117, 119, 120, 225, 85, 202, 156, 172, 135, 122, 128, 171, 10, 242, 243, 151, 199, 102, 226, 115, 63, 65, 4, 37, 54, 145, 146, 169, 100, 109, 110, 111, 112, 170, 232, 233, 53, 103, 104, 125, 139, 140, 38, 206, 0, 27, 150, 173, 29, 240, 93, 87, 97, 101, 106, 121, 123, 134, 141, 223, 7, 45, 180, 86, 46, 200, 196, 9, 39, 60, 67, 71, 74, 75, 78, 79, 230, 236, 229, 152, 153, 47, 213, 231, 42, 72, 12, 62, 5, 6, 182], "values": [0.6624662280082703, 0.6624662280082703, 0.6538089513778687, 0.6180596947669983, 0.6180596947669983, 0.6180596947669983, 0.6180596947669983, 0.590461790561676, 0.590461790561676, 0.590461790561676, 0.590461790561676, 0.590461790561676, 0.584774911403656, 0.5400651097297668, 0.5297893285751343, 0.5104137063026428, 0.5104137063026428, 0.5089584589004517, 0.49172407388687134, 0.4724312126636505, 0.4704616665840149, 0.456088125705719, 0.44305098056793213, 0.44305098056793213, 0.4420997202396393, 0.420012891292572, 0.4124571979045868, 0.4079754948616028, 0.40758848190307617, 0.40701091289520264, 0.40701091289520264, 0.40693140029907227, 0.40532785654067993, 0.4033264219760895, 0.40175214409828186, 0.40175214409828186, 0.40163925290107727, 0.3993483781814575, 0.3993483781814575, 0.3993483781814575, 0.3993483781814575, 0.3993483781814575, 0.3972475230693817, 0.396138072013855, 0.396138072013855, 0.39166194200515747, 0.3885830044746399, 0.3885830044746399, 0.3885830044746399, 0.3885830044746399, 0.3885830044746399, 0.38684624433517456, 0.38549289107322693, 0.3776622712612152, 0.3776622712612152, 0.3749651312828064, 0.3747938871383667, 0.37441879510879517, 0.36369168758392334, 0.3607900142669678, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.35996854305267334, 0.33361607789993286, 0.330651193857193, 0.3225700557231903, 0.3163878619670868, 0.3160133957862854, 0.3148791491985321, 0.31089866161346436, 0.30987998843193054, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30790114402770996, 0.30726519227027893, 0.30553650856018066, 0.3043578863143921, 0.30297577381134033, 0.30297577381134033, 0.29962635040283203, 0.2992946207523346, 0.29839038848876953, 0.2947443127632141, 0.2938704192638397, 0.2883387804031372, 0.28653034567832947, 0.28547409176826477, 0.28547409176826477, 0.2843676805496216]}, "5": {"indices": [853, 854, 860, 920, 1230, 1833, 1875, 2091, 3871, 4120, 2185, 3870, 3872, 3873, 4071, 4473, 2176, 2215, 2216, 2320, 2433, 2481, 2577, 2695, 2697, 2698, 2730, 2731, 2732, 2733, 2855, 2859, 2863, 4155, 4465, 923, 960, 1002, 1020, 4184, 2208, 2217, 4353, 2615, 2646, 2683, 2692, 4701, 4190, 4191, 4192, 290, 4606, 4624, 2589, 2629, 2640, 2693, 2696, 2701, 2704, 2713, 2716, 2754, 2767, 2786, 4349, 4426, 1219, 3266, 3536, 3544, 3548, 3549, 4026, 2717, 2777, 2785, 3546, 3419, 3422, 3440, 4565, 4584, 4607, 4608, 4622, 3072, 3600, 4826, 4639, 3925, 3928, 4189, 4699, 17, 4099, 3929, 782, 2769], "values": [0.9975717663764954, 0.9975717663764954, 0.9975717663764954, 0.9975717663764954, 0.9650293588638306, 0.8163672685623169, 0.8163672685623169, 0.7732126712799072, 0.6847736835479736, 0.6839406490325928, 0.6558017730712891, 0.6538499593734741, 0.6538499593734741, 0.6538499593734741, 0.6457480192184448, 0.6457480192184448, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6424252986907959, 0.6393659114837646, 0.6342839002609253, 0.6177965998649597, 0.6177965998649597, 0.6177965998649597, 0.6177965998649597, 0.6105679273605347, 0.6084917187690735, 0.6055421829223633, 0.6039212346076965, 0.6034246683120728, 0.6034246683120728, 0.6034246683120728, 0.6034246683120728, 0.6029502153396606, 0.6019640564918518, 0.6019640564918518, 0.6019640564918518, 0.5961557030677795, 0.5955086350440979, 0.5955086350440979, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.5945208668708801, 0.593011736869812, 0.5909035801887512, 0.5762010812759399, 0.5762010812759399, 0.5762010812759399, 0.5762010812759399, 0.5762010812759399, 0.5762010812759399, 0.5762010812759399, 0.5732625722885132, 0.5732625722885132, 0.5732625722885132, 0.572223961353302, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5656723976135254, 0.5607101917266846, 0.5607101917266846, 0.5558605194091797, 0.5554854869842529, 0.5547360181808472, 0.5547360181808472, 0.5514175295829773, 0.5465579032897949, 0.5413111448287964, 0.5399091243743896, 0.5389286279678345, 0.537134051322937, 0.536342978477478]}, "6": {"indices": [284, 240, 143, 144, 146, 147, 148, 827, 285, 145, 3236, 1051, 3060, 1014, 1501, 1502, 149, 327, 328, 329, 87, 2767, 4094, 4095, 4097, 4098, 996, 1040, 1060, 3858, 4313, 3221, 3222, 1086, 2345, 4542, 3497, 1710, 3474, 3475, 3479, 3480, 2497, 2489, 2492, 2493, 2494, 116, 3176, 826, 927, 1767, 2791, 3069, 3070, 3071, 3073, 3081, 3082, 3133, 3134, 3231, 834, 835, 4491, 1008, 115, 289, 4656, 4742, 4857, 1080, 4500, 527, 778, 781, 2760, 531, 828, 3784, 553, 554, 556, 557, 558, 602, 615, 2064, 2065, 2523, 2524, 2525, 2714, 3477, 4546, 4403, 2811, 3137, 3140, 2137], "values": [0.730995237827301, 0.6683038473129272, 0.6674759387969971, 0.6674759387969971, 0.6674759387969971, 0.6674759387969971, 0.6674759387969971, 0.649948000907898, 0.6241897940635681, 0.551977276802063, 0.5274350047111511, 0.5187797546386719, 0.5033317804336548, 0.4892764687538147, 0.4764145612716675, 0.4764145612716675, 0.4592311680316925, 0.44344350695610046, 0.44344350695610046, 0.44344350695610046, 0.4321726858615875, 0.4292515814304352, 0.4280864894390106, 0.4280864894390106, 0.4280864894390106, 0.4280864894390106, 0.4152039885520935, 0.4152039885520935, 0.4152039885520935, 0.41513770818710327, 0.4026513993740082, 0.4004923701286316, 0.4004923701286316, 0.39958202838897705, 0.39767032861709595, 0.3948896527290344, 0.3932030200958252, 0.3928636610507965, 0.39217472076416016, 0.39217472076416016, 0.39217472076416016, 0.39217472076416016, 0.3875327408313751, 0.3868595361709595, 0.3868595361709595, 0.3868595361709595, 0.3868595361709595, 0.3858029246330261, 0.38511621952056885, 0.38357314467430115, 0.3825725317001343, 0.3825725317001343, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.3812006115913391, 0.380725234746933, 0.380725234746933, 0.37928682565689087, 0.37602758407592773, 0.37475308775901794, 0.3682484030723572, 0.36735105514526367, 0.36735105514526367, 0.36735105514526367, 0.3639729917049408, 0.3620259761810303, 0.36081549525260925, 0.36081549525260925, 0.36081549525260925, 0.35981011390686035, 0.35753655433654785, 0.3571113348007202, 0.3534057140350342, 0.34981483221054077, 0.34981483221054077, 0.34981483221054077, 0.34981483221054077, 0.34981483221054077, 0.348390132188797, 0.348390132188797, 0.3461308777332306, 0.3461308777332306, 0.34584879875183105, 0.34584879875183105, 0.34584879875183105, 0.3456774353981018, 0.3456295430660248, 0.34395453333854675, 0.34284889698028564, 0.3423461616039276, 0.3423461616039276, 0.3423461616039276, 0.342101514339447]}, "7": {"indices": [34, 7, 11, 14, 123, 12, 46, 33, 36, 41, 42, 13, 15, 29, 43, 35, 104, 30, 98, 8, 9, 77, 6, 207, 31, 27, 28, 37, 172, 40, 137, 138, 139, 140, 68, 63, 99, 193, 184, 112, 113, 32, 155, 91, 26, 10, 25, 199, 200, 201, 39, 158, 159, 160, 161, 162, 163, 52, 54, 55, 56, 57, 210, 110, 118, 108, 150, 152, 153, 97, 89, 24, 194, 180, 94, 197, 93, 44, 58, 213, 59, 38, 166, 174, 5, 96, 53, 50, 121, 122, 212, 103, 105, 164, 205, 71, 101, 100, 45, 188], "values": [0.8355023264884949, 0.681782603263855, 0.6296814680099487, 0.6296814680099487, 0.6047410368919373, 0.6017360091209412, 0.5437449216842651, 0.5329038500785828, 0.5220320820808411, 0.5220320820808411, 0.5220320820808411, 0.5126869678497314, 0.4902424216270447, 0.47889798879623413, 0.4739780128002167, 0.4519338309764862, 0.4385482668876648, 0.42581817507743835, 0.4230218529701233, 0.41863685846328735, 0.41278326511383057, 0.4037785530090332, 0.3941415846347809, 0.38613197207450867, 0.3732340931892395, 0.3725154995918274, 0.3725154995918274, 0.3567515015602112, 0.3514118790626526, 0.34868812561035156, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.3266542851924896, 0.3079492747783661, 0.30712729692459106, 0.299442321062088, 0.2974482476711273, 0.295329213142395, 0.295329213142395, 0.2951374650001526, 0.2871512174606323, 0.2805454134941101, 0.27769935131073, 0.2767360806465149, 0.2754376530647278, 0.2736871838569641, 0.2736871838569641, 0.2736871838569641, 0.2677212953567505, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.2632717192173004, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.2615187168121338, 0.2609832286834717, 0.2609832286834717, 0.25709396600723267, 0.25516828894615173, 0.25516828894615173, 0.25516828894615173, 0.2513769268989563, 0.2447986900806427, 0.24375922977924347, 0.23914113640785217, 0.23304912447929382, 0.22976645827293396, 0.22899697721004486, 0.22809219360351562, 0.22308345139026642, 0.2228611409664154, 0.22057557106018066, 0.2191094160079956, 0.21338146924972534, 0.21199211478233337, 0.21046307682991028, 0.1994990110397339, 0.19864699244499207, 0.19803595542907715, 0.19719743728637695, 0.1955522894859314, 0.1955522894859314, 0.19510281085968018, 0.19480031728744507, 0.19480031728744507, 0.18750925362110138, 0.18647556006908417, 0.176116481423378, 0.17452888190746307, 0.1719837784767151, 0.17080917954444885, 0.1696484535932541]}, "8": {"indices": [446, 473, 575, 597, 604, 605, 606, 607, 614, 461, 1433, 1700, 1348, 611, 1571, 1572, 1573, 412, 413, 414, 627, 621, 525, 526, 132, 133, 130, 1944, 245, 248, 425, 1943, 422, 1166, 1930, 2193, 127, 1462, 1463, 145, 146, 472, 2002, 2003, 2014, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 1781, 894, 1295, 1296, 1297, 1298, 444, 445, 468, 469, 470, 1845, 128, 154, 148, 149, 1299, 1379, 1975, 925, 343, 1984, 1993, 2012, 2039, 1658, 618, 619, 653, 654, 1522, 1739, 1784, 1785, 1787, 1788, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 2683, 118, 241], "values": [0.9076100587844849, 0.9076100587844849, 0.8777502775192261, 0.8777502775192261, 0.8777502775192261, 0.8777502775192261, 0.8777502775192261, 0.8777502775192261, 0.8777502775192261, 0.8582329750061035, 0.7976073026657104, 0.7777746915817261, 0.7623568773269653, 0.7452483773231506, 0.7133110761642456, 0.7133110761642456, 0.7133110761642456, 0.6665670871734619, 0.6665670871734619, 0.6665670871734619, 0.6592218279838562, 0.646052360534668, 0.6449793577194214, 0.6449793577194214, 0.6406489610671997, 0.6406489610671997, 0.5974026918411255, 0.571960985660553, 0.5700643062591553, 0.5700643062591553, 0.5551310777664185, 0.5465741753578186, 0.541375994682312, 0.533860445022583, 0.5245523452758789, 0.5203770399093628, 0.5145975351333618, 0.5145975351333618, 0.5145975351333618, 0.5104277729988098, 0.5104277729988098, 0.5047951936721802, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5040544867515564, 0.5016750693321228, 0.49775296449661255, 0.49603748321533203, 0.49603748321533203, 0.49603748321533203, 0.49603748321533203, 0.49383530020713806, 0.49383530020713806, 0.49383530020713806, 0.49383530020713806, 0.49383530020713806, 0.4938022196292877, 0.4864377975463867, 0.4864377975463867, 0.47073084115982056, 0.47073084115982056, 0.469828724861145, 0.4689115881919861, 0.4689115881919861, 0.46095010638237, 0.44929176568984985, 0.4477250576019287, 0.4477250576019287, 0.4477250576019287, 0.4477250576019287, 0.4458758234977722, 0.4356685280799866, 0.4356685280799866, 0.4356685280799866, 0.4356685280799866, 0.4356685280799866, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43488937616348267, 0.43382465839385986, 0.4325707256793976, 0.4271843731403351]}, "9": {"indices": [1572, 4631, 4562, 4033, 4217, 2593, 2672, 2592, 2228, 2694, 2697, 36, 2226, 2227, 3384, 780, 440, 505, 3957, 580, 588, 31, 504, 626, 4755, 4759, 2710, 175, 2201, 39, 1771, 4136, 581, 587, 828, 829, 830, 1092, 4260, 2229, 2556, 2714, 2184, 4241, 383, 4102, 4103, 4129, 4757, 4758, 4762, 3918, 4187, 4188, 4101, 4104, 1543, 2669, 4286, 15, 30, 525, 619, 620, 1090, 377, 506, 507, 658, 2234, 508, 509, 549, 437, 439, 3142, 4756, 3383, 4761, 1732, 1978, 1697, 3563, 3564, 625, 1655, 3933, 784, 1948, 2061, 4429, 502, 657, 953, 954, 4470, 4477, 4478, 4479, 55], "values": [0.9978220462799072, 0.8763213157653809, 0.8479495048522949, 0.7933797836303711, 0.7933797836303711, 0.6932381987571716, 0.6864486932754517, 0.6863817572593689, 0.6566119194030762, 0.6566119194030762, 0.6566119194030762, 0.647615909576416, 0.647615909576416, 0.647615909576416, 0.6266840696334839, 0.6013986468315125, 0.5888066291809082, 0.5808532238006592, 0.5707608461380005, 0.5681923627853394, 0.5681923627853394, 0.5647482872009277, 0.5605255961418152, 0.5556399822235107, 0.5537847280502319, 0.5537847280502319, 0.5511037111282349, 0.5383667349815369, 0.5343554615974426, 0.5116606950759888, 0.5090343356132507, 0.5088871717453003, 0.49970543384552, 0.49970543384552, 0.49970543384552, 0.49970543384552, 0.49970543384552, 0.49970543384552, 0.4948728084564209, 0.4914616048336029, 0.4914616048336029, 0.4914616048336029, 0.4875922203063965, 0.48159167170524597, 0.4801886975765228, 0.4798235297203064, 0.4798235297203064, 0.4798235297203064, 0.4798235297203064, 0.4798235297203064, 0.4798235297203064, 0.47699832916259766, 0.47443246841430664, 0.47443246841430664, 0.4699189066886902, 0.4699189066886902, 0.4697082042694092, 0.4697082042694092, 0.4656161367893219, 0.4650801420211792, 0.4640430212020874, 0.4631590247154236, 0.4594484567642212, 0.4594484567642212, 0.4575251340866089, 0.45634275674819946, 0.45634275674819946, 0.45634275674819946, 0.45634275674819946, 0.4558556377887726, 0.44891834259033203, 0.44891834259033203, 0.4453718960285187, 0.4451095461845398, 0.4451095461845398, 0.4446064233779907, 0.4380851984024048, 0.43418455123901367, 0.4338517487049103, 0.4301283359527588, 0.4133793115615845, 0.40489351749420166, 0.40446990728378296, 0.40446990728378296, 0.40358856320381165, 0.40358856320381165, 0.40358322858810425, 0.40305742621421814, 0.40305742621421814, 0.40305742621421814, 0.40305742621421814, 0.40104323625564575, 0.40104323625564575, 0.40057915449142456, 0.40057915449142456, 0.39965641498565674, 0.39965641498565674, 0.39965641498565674, 0.39965641498565674, 0.39720314741134644]}, "10": {"indices": [190, 195, 207, 967, 390, 1274, 1593, 1231, 1267, 1273, 1302, 1313, 1329, 1429, 1453, 1475, 619, 1214, 1596, 576, 682, 722, 1581, 1582, 1594, 201, 205, 206, 1120, 1584, 1449, 1481, 1493, 1261, 1268, 723, 812, 429, 658, 1625, 1627, 1630, 607, 613, 720, 1373, 498, 236, 374, 393, 396, 451, 509, 510, 513, 556, 569, 570, 575, 582, 602, 614, 617, 639, 642, 643, 714, 719, 1352, 1597, 1679, 1400, 1452, 1476, 1599, 1601, 1600, 430, 1263, 1149, 1180, 1207, 1211, 452, 1619, 679, 687, 691, 724, 743, 748, 749, 790, 800, 809, 819, 824, 1451, 1474, 1477], "values": [0.9979815483093262, 0.9979815483093262, 0.9979815483093262, 0.8951594233512878, 0.8938400149345398, 0.8938400149345398, 0.8938400149345398, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8919435143470764, 0.8767865896224976, 0.8767865896224976, 0.8767865896224976, 0.871703028678894, 0.871703028678894, 0.871703028678894, 0.8588069677352905, 0.8588069677352905, 0.8588069677352905, 0.855060338973999, 0.855060338973999, 0.855060338973999, 0.8546804189682007, 0.8442789912223816, 0.8409860134124756, 0.8409860134124756, 0.8409860134124756, 0.838634192943573, 0.838634192943573, 0.8360822200775146, 0.8360822200775146, 0.8347796201705933, 0.8347796201705933, 0.8344399929046631, 0.8344399929046631, 0.8344399929046631, 0.8334075212478638, 0.8334075212478638, 0.8334075212478638, 0.8328464031219482, 0.8276299238204956, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.8263828754425049, 0.826082170009613, 0.8247325420379639, 0.8247325420379639, 0.8229616284370422, 0.8217343091964722, 0.8217343091964722, 0.8216971158981323, 0.8216971158981323, 0.8212363123893738, 0.8160495758056641, 0.813835859298706, 0.8128534555435181, 0.8128534555435181, 0.8128534555435181, 0.8128534555435181, 0.812751054763794, 0.803875744342804, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032, 0.8011280298233032]}, "11": {"indices": [385, 1541, 481, 485, 486, 505, 515, 689, 988, 406, 1579, 1613, 375, 380, 386, 1682, 1328, 1414, 1415, 1417, 473, 1375, 1391, 873, 874, 1266, 881, 1461, 1117, 940, 412, 421, 422, 423, 427, 428, 435, 449, 478, 480, 1270, 1314, 1330, 1402, 1430, 1454, 1466, 1467, 1468, 1530, 442, 968, 1499, 1673, 1707, 408, 1508, 1543, 1306, 418, 447, 893, 757, 1133, 499, 69, 1708, 1199, 1200, 439, 29, 30, 52, 53, 54, 1222, 1573, 1662, 1463, 1478, 1496, 1552, 1555, 1558, 1559, 1576, 1587, 376, 1225, 1226, 1227, 584, 22, 67, 68, 75, 311, 490, 1519, 1534], "values": [0.9980266094207764, 0.972307562828064, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.9466214179992676, 0.9466214179992676, 0.9445673227310181, 0.9385941028594971, 0.9385941028594971, 0.9385941028594971, 0.9351229667663574, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9144363403320312, 0.9061398506164551, 0.9061398506164551, 0.9003400802612305, 0.9003400802612305, 0.8952523469924927, 0.8945959806442261, 0.8901271820068359, 0.8890048265457153, 0.8869749307632446, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8571504354476929, 0.8571504354476929, 0.8571504354476929, 0.8489366769790649, 0.8447636365890503, 0.8447636365890503, 0.8447636365890503, 0.8443904519081116, 0.841084361076355, 0.8402484655380249, 0.8300219178199768, 0.8292279839515686, 0.8286341428756714, 0.8286341428756714, 0.8262461423873901, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8257873058319092, 0.8237709999084473, 0.8227684497833252, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.819891095161438, 0.8193692564964294, 0.8193692564964294, 0.8193692564964294, 0.8189754486083984, 0.8184829354286194, 0.8184688687324524, 0.8184688687324524, 0.8184688687324524, 0.8181168437004089, 0.817807674407959, 0.8173049688339233, 0.8173049688339233]}, "12": {"indices": [224, 225, 226, 221, 227, 218, 219, 220, 170, 198, 185, 169, 210, 76, 183, 233, 70, 73, 280, 300, 330, 361, 362, 16, 223, 376, 33, 128, 146, 43, 358, 106, 176, 192, 147, 209, 239, 272, 273, 274, 277, 62, 2, 360, 126, 196, 199, 387, 388, 389, 390, 391, 392, 186, 39, 153, 154, 68, 350, 351, 112, 113, 64, 211, 355, 152, 177, 278, 322, 44, 377, 378, 56, 250, 175, 18, 144, 114, 30, 31, 32, 247, 248, 249, 51, 28, 332, 117, 119, 120, 156, 157, 174, 71, 72, 96, 214, 59, 82, 327], "values": [0.845034658908844, 0.845034658908844, 0.845034658908844, 0.6827110648155212, 0.6827110648155212, 0.6731554269790649, 0.6731554269790649, 0.6731554269790649, 0.5505293011665344, 0.5154500007629395, 0.500328540802002, 0.4924737811088562, 0.49068790674209595, 0.4562056064605713, 0.45556965470314026, 0.45509985089302063, 0.4527530074119568, 0.4527530074119568, 0.4436771869659424, 0.4350309371948242, 0.43170076608657837, 0.43170076608657837, 0.43170076608657837, 0.4263782203197479, 0.4092657268047333, 0.4024960994720459, 0.3963586091995239, 0.3954005837440491, 0.3948978781700134, 0.3866543769836426, 0.38191258907318115, 0.37630367279052734, 0.36330997943878174, 0.35787689685821533, 0.3564271330833435, 0.3564271330833435, 0.35559695959091187, 0.3552466630935669, 0.3552466630935669, 0.3552466630935669, 0.3552466630935669, 0.3528159558773041, 0.34951621294021606, 0.34575971961021423, 0.3443472683429718, 0.3443472683429718, 0.3438647985458374, 0.34332939982414246, 0.34332939982414246, 0.34332939982414246, 0.34332939982414246, 0.34332939982414246, 0.34332939982414246, 0.3414677381515503, 0.3394390046596527, 0.33747002482414246, 0.33747002482414246, 0.3347141146659851, 0.33435165882110596, 0.33435165882110596, 0.3329886794090271, 0.3329886794090271, 0.32651811838150024, 0.3237639367580414, 0.313526451587677, 0.3116151690483093, 0.3114677667617798, 0.3106314241886139, 0.2964622378349304, 0.29618382453918457, 0.29555341601371765, 0.29555341601371765, 0.2940047085285187, 0.2928857207298279, 0.28622862696647644, 0.28601256012916565, 0.2836395502090454, 0.2822023630142212, 0.2814539074897766, 0.2814539074897766, 0.2814539074897766, 0.2771882712841034, 0.2765211760997772, 0.2765211760997772, 0.27650296688079834, 0.27546653151512146, 0.27514371275901794, 0.27491772174835205, 0.27491772174835205, 0.27491772174835205, 0.2731354832649231, 0.2731354832649231, 0.2731354832649231, 0.27266547083854675, 0.27266547083854675, 0.26953279972076416, 0.2684052288532257, 0.2679885923862457, 0.2679038643836975, 0.2665124535560608]}, "13": {"indices": [258, 255, 259, 257, 260, 520, 256, 496, 561, 2819, 556, 557, 512, 513, 481, 2461, 2815, 2816, 276, 1847, 558, 559, 986, 1014, 1016, 1017, 1018, 1019, 1021, 1026, 1027, 1031, 1032, 1033, 1034, 1036, 1037, 1038, 590, 585, 279, 2858, 516, 281, 212, 213, 262, 425, 509, 1050, 1053, 253, 699, 700, 1364, 274, 514, 2508, 2594, 216, 277, 278, 2242, 2007, 2009, 2440, 2111, 2112, 1973, 1974, 1975, 2356, 1009, 1013, 1015, 1020, 1022, 1023, 1025, 1028, 1029, 1030, 237, 1758, 1759, 261, 2865, 2867, 2868, 2826, 471, 988, 2822, 540, 2551, 2552, 1361, 1363, 1828, 1829], "values": [0.6506220698356628, 0.6013747453689575, 0.5832432508468628, 0.556429386138916, 0.5558450818061829, 0.549297571182251, 0.5444773435592651, 0.5424180626869202, 0.5298036336898804, 0.5126038789749146, 0.5051047801971436, 0.5051047801971436, 0.5035924315452576, 0.5035924315452576, 0.5032760500907898, 0.501578152179718, 0.4821825623512268, 0.4821825623512268, 0.47824788093566895, 0.4726598262786865, 0.4710346460342407, 0.4710346460342407, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.46704983711242676, 0.4530290365219116, 0.4528168737888336, 0.44394451379776, 0.4431415796279907, 0.4425126016139984, 0.4401441812515259, 0.4383005201816559, 0.4383005201816559, 0.4373414218425751, 0.4363479018211365, 0.4302546977996826, 0.4302546977996826, 0.4302546977996826, 0.4286022186279297, 0.42652028799057007, 0.42652028799057007, 0.4242955446243286, 0.4224325120449066, 0.41844791173934937, 0.41483068466186523, 0.41387760639190674, 0.4115879237651825, 0.4113643169403076, 0.4113643169403076, 0.4093468189239502, 0.4062332808971405, 0.4062332808971405, 0.40316906571388245, 0.4005658030509949, 0.4005658030509949, 0.399765282869339, 0.399765282869339, 0.399765282869339, 0.3971439003944397, 0.39537596702575684, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3946966528892517, 0.3922862708568573, 0.38440555334091187, 0.38440555334091187, 0.3843822479248047, 0.38361161947250366, 0.38361161947250366, 0.38361161947250366, 0.3832286298274994, 0.38281434774398804, 0.3799726963043213, 0.3774702250957489, 0.3768573999404907, 0.3751167058944702, 0.3751167058944702, 0.3735175132751465, 0.3735175132751465, 0.3717365562915802, 0.3717365562915802]}, "14": {"indices": [274, 2311, 276, 467, 468, 254, 2387, 2380, 256, 255, 509, 1050, 1053, 2895, 478, 540, 1989, 1990, 1994, 1995, 1999, 2000, 2455, 1046, 1047, 1048, 286, 281, 275, 2869, 287, 489, 2551, 2552, 1581, 441, 267, 268, 269, 270, 271, 2322, 522, 2385, 2386, 285, 288, 731, 2508, 733, 2349, 2506, 277, 278, 279, 263, 264, 940, 884, 2094, 2095, 283, 252, 280, 2146, 403, 457, 648, 558, 559, 300, 1996, 273, 730, 732, 258, 1100, 460, 461, 1051, 472, 487, 772, 1991, 1992, 1993, 212, 213, 663, 1071, 726, 727, 728, 729, 297, 2105, 2027, 2040, 2043, 2046], "values": [0.6456102728843689, 0.6186203956604004, 0.6140081286430359, 0.5995039939880371, 0.5995039939880371, 0.5936238765716553, 0.5882829427719116, 0.5721109509468079, 0.5711149573326111, 0.5506012439727783, 0.5500175356864929, 0.5500175356864929, 0.5500175356864929, 0.5474610328674316, 0.5316798686981201, 0.5288993120193481, 0.5263676643371582, 0.5263676643371582, 0.5263676643371582, 0.5263676643371582, 0.5263676643371582, 0.5263676643371582, 0.5135780572891235, 0.5031654834747314, 0.5031654834747314, 0.5031654834747314, 0.502328634262085, 0.4980015456676483, 0.49800020456314087, 0.4974108636379242, 0.4838331639766693, 0.4774831533432007, 0.47508135437965393, 0.47508135437965393, 0.4699227213859558, 0.4664098620414734, 0.46469587087631226, 0.46469587087631226, 0.46469587087631226, 0.46469587087631226, 0.46469587087631226, 0.4632863402366638, 0.46294543147087097, 0.4617144763469696, 0.4617144763469696, 0.4591939449310303, 0.45853981375694275, 0.45643100142478943, 0.4526904225349426, 0.44399338960647583, 0.4421665370464325, 0.4405735731124878, 0.43857383728027344, 0.43857383728027344, 0.434902548789978, 0.43028849363327026, 0.43028849363327026, 0.4295559525489807, 0.4288368821144104, 0.4270123243331909, 0.4270123243331909, 0.4265296459197998, 0.42404264211654663, 0.4239342510700226, 0.4228504002094269, 0.42167574167251587, 0.4205103814601898, 0.41496267914772034, 0.41409486532211304, 0.41409486532211304, 0.4096783995628357, 0.406272292137146, 0.4004042446613312, 0.39985716342926025, 0.39985716342926025, 0.3984697461128235, 0.3980695605278015, 0.39073067903518677, 0.39073067903518677, 0.38887864351272583, 0.3888111114501953, 0.388560950756073, 0.3849451243877411, 0.38381069898605347, 0.38381069898605347, 0.38381069898605347, 0.38219180703163147, 0.38219180703163147, 0.3817657232284546, 0.37901341915130615, 0.3772784173488617, 0.3772784173488617, 0.3772784173488617, 0.3772784173488617, 0.37692341208457947, 0.37564998865127563, 0.3743099570274353, 0.3743099570274353, 0.3743099570274353, 0.3743099570274353]}, "15": {"indices": [553, 2753, 466, 2089, 2440, 2503, 2140, 756, 2547, 2389, 2390, 1191, 2048, 1361, 1363, 1100, 1821, 1840, 228, 229, 232, 233, 234, 235, 236, 2608, 906, 1582, 1583, 458, 469, 1107, 910, 911, 1277, 1278, 1316, 1024, 1035, 2716, 909, 2352, 238, 243, 244, 1248, 1249, 1264, 1164, 1165, 1166, 344, 405, 174, 175, 2182, 2198, 2199, 2200, 1796, 444, 507, 508, 549, 555, 1644, 757, 770, 807, 847, 848, 849, 850, 851, 852, 853, 854, 862, 863, 864, 725, 368, 2695, 349, 1088, 414, 2506, 2849, 2592, 2487, 2541, 2542, 982, 794, 796, 797, 1106, 403, 1507, 716], "values": [0.6483927965164185, 0.6336904764175415, 0.5947412848472595, 0.5154439210891724, 0.49830177426338196, 0.46457064151763916, 0.46379438042640686, 0.4613261818885803, 0.4604536294937134, 0.45645493268966675, 0.45645493268966675, 0.4546237289905548, 0.4525710344314575, 0.45070716738700867, 0.45070716738700867, 0.4443349242210388, 0.4439530372619629, 0.4439530372619629, 0.438649982213974, 0.438649982213974, 0.438649982213974, 0.438649982213974, 0.438649982213974, 0.438649982213974, 0.438649982213974, 0.43759214878082275, 0.433292031288147, 0.4312346577644348, 0.4312346577644348, 0.42970341444015503, 0.42970341444015503, 0.4266602396965027, 0.4221786558628082, 0.4221786558628082, 0.4212469160556793, 0.4212469160556793, 0.4212469160556793, 0.41277074813842773, 0.41277074813842773, 0.41255509853363037, 0.4117877781391144, 0.40648725628852844, 0.4063313603401184, 0.4063313603401184, 0.4063313603401184, 0.3993592858314514, 0.3993592858314514, 0.3993592858314514, 0.3973104655742645, 0.3973104655742645, 0.3973104655742645, 0.3968777358531952, 0.3968777358531952, 0.39596426486968994, 0.39596426486968994, 0.392508327960968, 0.392508327960968, 0.392508327960968, 0.392508327960968, 0.392331063747406, 0.3912818431854248, 0.3912818431854248, 0.3912818431854248, 0.3901594877243042, 0.3901594877243042, 0.38937029242515564, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3888622522354126, 0.3880632221698761, 0.38502198457717896, 0.38439521193504333, 0.3839232921600342, 0.38221773505210876, 0.38150298595428467, 0.37957870960235596, 0.3780834376811981, 0.37768322229385376, 0.37652167677879333, 0.37652167677879333, 0.37652167677879333, 0.37534135580062866, 0.37286198139190674, 0.37286198139190674, 0.37286198139190674, 0.37161752581596375, 0.37130647897720337, 0.3698253333568573, 0.3691405653953552]}, "16": {"indices": [347, 169, 3310, 3314, 3399, 1846, 1847, 4412, 3122, 1850, 1852, 1855, 1856, 4629, 3431, 3304, 760, 958, 957, 78, 79, 80, 81, 331, 4854, 1849, 1857, 1858, 819, 3866, 3305, 3307, 4636, 1848, 3937, 3867, 0, 10, 1987, 3105, 1939, 1862, 4136, 762, 763, 767, 2851, 2852, 2857, 2859, 2625, 961, 3876, 3202, 3933, 4832, 4834, 3939, 3940, 3942, 3944, 3945, 4518, 4562, 4627, 4630, 4138, 4139, 3125, 1890, 1892, 1827, 3112, 2503, 4576, 4821, 3946, 3745, 3943, 2447, 2451, 3103, 2061, 2062, 2332, 1284, 1829, 1029, 1445, 1828, 4392, 83, 163, 2307, 2320, 2321, 2322, 2392, 2393, 3123], "values": [0.8840317726135254, 0.6718734502792358, 0.6537448763847351, 0.6537448763847351, 0.6512521505355835, 0.6301039457321167, 0.6301039457321167, 0.6282128095626831, 0.6105889081954956, 0.6040351390838623, 0.6040351390838623, 0.6040351390838623, 0.6040351390838623, 0.5915427207946777, 0.5831764936447144, 0.5829981565475464, 0.5823358297348022, 0.5823358297348022, 0.5814557075500488, 0.5799894332885742, 0.5799894332885742, 0.5799894332885742, 0.5799894332885742, 0.5799894332885742, 0.5739918947219849, 0.5731285214424133, 0.5731285214424133, 0.5731285214424133, 0.5711052417755127, 0.5685056447982788, 0.5652432441711426, 0.5652432441711426, 0.559929609298706, 0.5588529109954834, 0.5584572553634644, 0.5573736429214478, 0.5449738502502441, 0.5449738502502441, 0.5449738502502441, 0.5396889448165894, 0.5345984697341919, 0.5328839421272278, 0.5319066047668457, 0.5304932594299316, 0.5304932594299316, 0.5304932594299316, 0.5275616645812988, 0.5275616645812988, 0.5275616645812988, 0.5275616645812988, 0.5270005464553833, 0.5222349166870117, 0.5222094058990479, 0.5218428373336792, 0.5217505693435669, 0.5208682417869568, 0.518717885017395, 0.5185621380805969, 0.5185621380805969, 0.5181900262832642, 0.5181900262832642, 0.5181900262832642, 0.5155783891677856, 0.5155783891677856, 0.5155783891677856, 0.5155783891677856, 0.5145581364631653, 0.5145581364631653, 0.5141702890396118, 0.5139532685279846, 0.5139532685279846, 0.5117168426513672, 0.5071406960487366, 0.5067119598388672, 0.5065099000930786, 0.505845844745636, 0.503296971321106, 0.5000717639923096, 0.5000078678131104, 0.4989422559738159, 0.4989422559738159, 0.49699273705482483, 0.49539220333099365, 0.49539220333099365, 0.49539220333099365, 0.4937666952610016, 0.49362343549728394, 0.4936218857765198, 0.49066561460494995, 0.48937758803367615, 0.48884689807891846, 0.48761287331581116, 0.48761287331581116, 0.48761287331581116, 0.48761287331581116, 0.48761287331581116, 0.48761287331581116, 0.486355185508728, 0.486355185508728, 0.484890341758728]}, "17": {"indices": [29, 18, 8, 10, 24, 31, 22, 32, 6, 7, 19, 20, 11, 12, 25, 27, 21, 3, 28, 9, 34, 35, 26, 4, 23, 1, 2, 30, 0, 5, 13, 14, 15, 16, 17, 33], "values": [0.5468879342079163, 0.4839995503425598, 0.4822302758693695, 0.4822302758693695, 0.4798535108566284, 0.4701407551765442, 0.45251086354255676, 0.44526946544647217, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.4345024824142456, 0.4345024824142456, 0.4065087139606476, 0.3430609405040741, 0.32818904519081116, 0.319267213344574, 0.2979602515697479, 0.2974632978439331, 0.29507964849472046, 0.29507964849472046, 0.291653573513031, 0.2882101535797119, 0.2847745418548584, 0.2840638756752014, 0.2840638756752014, 0.17938299477100372, 0.163307324051857, 0.16136708855628967, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 24, 26, 8, 10, 6, 7, 19, 20, 31, 28, 9, 21, 27, 30, 23, 29, 25, 11, 12, 4, 1, 2, 5, 34, 35, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.7056763172149658, 0.658897876739502, 0.6234023571014404, 0.6139054298400879, 0.58753502368927, 0.58753502368927, 0.5869445204734802, 0.5869445204734802, 0.5869445204734802, 0.5869445204734802, 0.558692216873169, 0.551642894744873, 0.5397974848747253, 0.5385558605194092, 0.5277528762817383, 0.5183757543563843, 0.49443337321281433, 0.49378466606140137, 0.4482795298099518, 0.44160589575767517, 0.44160589575767517, 0.43440842628479004, 0.4318804144859314, 0.4318804144859314, 0.4135266840457916, 0.3994906544685364, 0.3994906544685364, 0.35441505908966064, 0.3503527045249939, 0.25450003147125244, 0.14664733409881592, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [60, 61, 18, 79, 163, 161, 86, 87, 51, 52, 111, 57, 59, 119, 7, 43, 195, 210, 201, 206, 3, 105, 6, 62, 55, 175, 63, 64, 70, 215, 211, 58, 180, 208, 209, 172, 142, 143, 144, 126, 46, 69, 81, 189, 243, 65, 190, 2, 4, 5, 226, 194, 14, 16, 203, 120, 113, 74, 89, 198, 82, 207, 197, 110, 54, 80, 199, 181, 71, 124, 141, 23, 112, 114, 15, 233, 213, 234, 179, 42, 133, 128, 244, 212, 205, 196, 22, 103, 247, 237, 183, 214, 44, 236, 24, 27, 245, 122, 20, 200], "values": [0.8015655279159546, 0.8015655279159546, 0.800186276435852, 0.61016845703125, 0.5592929124832153, 0.5433822870254517, 0.5012836456298828, 0.5012836456298828, 0.48828208446502686, 0.48828208446502686, 0.47177058458328247, 0.44401758909225464, 0.44309577345848083, 0.4105152487754822, 0.39441967010498047, 0.3933994770050049, 0.37923234701156616, 0.36191999912261963, 0.35309937596321106, 0.34643805027008057, 0.33699870109558105, 0.3357974588871002, 0.33046865463256836, 0.30267563462257385, 0.2901327610015869, 0.28643521666526794, 0.27970343828201294, 0.27970343828201294, 0.2751466631889343, 0.267164409160614, 0.26096034049987793, 0.2465893179178238, 0.23076580464839935, 0.22697599232196808, 0.22697599232196808, 0.22587552666664124, 0.2134752869606018, 0.2134752869606018, 0.2134752869606018, 0.2103939950466156, 0.208261638879776, 0.19150957465171814, 0.18875542283058167, 0.18409103155136108, 0.18268805742263794, 0.18084567785263062, 0.1751769483089447, 0.17503681778907776, 0.17503681778907776, 0.17503681778907776, 0.17299127578735352, 0.16966332495212555, 0.16777139902114868, 0.16777139902114868, 0.16649237275123596, 0.16446571052074432, 0.1624225527048111, 0.15381458401679993, 0.15227308869361877, 0.1514841914176941, 0.148375004529953, 0.14314058423042297, 0.13890354335308075, 0.13805758953094482, 0.13666778802871704, 0.13666778802871704, 0.13666117191314697, 0.13298214972019196, 0.12461508810520172, 0.118744857609272, 0.118744857609272, 0.11287660151720047, 0.11221744120121002, 0.11221744120121002, 0.10249722748994827, 0.10192391276359558, 0.10059460252523422, 0.09994277358055115, 0.09781771153211594, 0.0939168706536293, 0.09305144846439362, 0.09253160655498505, 0.09212134033441544, 0.09025833010673523, 0.08891324698925018, 0.08774791657924652, 0.08358398824930191, 0.0821562260389328, 0.08053833246231079, 0.07908889651298523, 0.07271206378936768, 0.07227188348770142, 0.06770563125610352, 0.06676945090293884, 0.06549020111560822, 0.06529253721237183, 0.06423700600862503, 0.06327737867832184, 0.06179533526301384, 0.061229344457387924]}}

v2 = {"0": {"indices": [391, 682, 430, 392, 195, 754, 774, 775, 779, 781, 375, 393, 623, 178, 12, 787, 203, 205, 17, 85, 176, 177, 783, 784, 785, 428, 199, 200, 792, 404, 405, 614, 616, 617, 618, 789, 67, 106, 107, 807, 791, 531, 406, 149, 82, 83, 530, 630, 461, 465, 482, 483, 484, 541, 201, 196, 36, 170, 429, 213, 608, 162, 163, 148, 538, 169, 808, 670, 157, 862, 210, 211, 891, 893, 894, 764, 765, 395, 400, 401, 402, 403, 412, 413, 439, 450, 669, 101, 168, 665, 437, 609, 854, 285, 485, 525, 526, 557, 455, 228], "values": [0.634227991104126, 0.5791579484939575, 0.5569994449615479, 0.5523046851158142, 0.5470099449157715, 0.5457106828689575, 0.5457106828689575, 0.5457106828689575, 0.5335338711738586, 0.5335338711738586, 0.5325450897216797, 0.5325450897216797, 0.5082945227622986, 0.5031034350395203, 0.49784886837005615, 0.49738410115242004, 0.4895824193954468, 0.4895824193954468, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.48553937673568726, 0.4803156852722168, 0.4803156852722168, 0.4803156852722168, 0.44719216227531433, 0.44329220056533813, 0.44329220056533813, 0.43824338912963867, 0.43666914105415344, 0.43666914105415344, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.4362137019634247, 0.43399766087532043, 0.42945337295532227, 0.42945337295532227, 0.42945337295532227, 0.40701183676719666, 0.4068290591239929, 0.4050838053226471, 0.40103745460510254, 0.3955700993537903, 0.38879138231277466, 0.38879138231277466, 0.3796015679836273, 0.373837411403656, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.3711092472076416, 0.36885976791381836, 0.3659629821777344, 0.3619682192802429, 0.3513945937156677, 0.3505263924598694, 0.3496953845024109, 0.34440508484840393, 0.34231650829315186, 0.33986353874206543, 0.33986353874206543, 0.33520692586898804, 0.3319920301437378, 0.3302970826625824, 0.3302970826625824, 0.32643309235572815, 0.326144278049469, 0.3249531388282776, 0.32240742444992065, 0.32240742444992065, 0.31665271520614624, 0.31665271520614624, 0.31665271520614624, 0.31230077147483826, 0.31230077147483826, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.31222230195999146, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30878886580467224, 0.30816277861595154, 0.3070964813232422, 0.3070964813232422, 0.30662038922309875, 0.3054530918598175, 0.30521929264068604, 0.3042910397052765, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29811403155326843, 0.29580268263816833, 0.29359209537506104]}, "1": {"indices": [62, 63, 66, 75, 77, 78, 79, 80, 81, 82, 83, 87, 88, 57, 58, 50, 76, 39, 1, 12, 130, 0, 36, 38, 44, 85, 86, 52, 51, 53, 54, 31, 89, 128, 67, 6, 148, 149, 150, 151, 49, 65, 133, 131, 129, 144, 145, 126, 98, 103, 105, 68, 69, 23, 71, 72, 122, 97, 99, 100, 101, 102, 106, 118, 123, 124, 115, 146, 116, 134, 8, 37, 84, 132, 48, 26, 27, 45, 42, 43, 138, 125, 73, 19, 15, 16, 18, 10, 25, 70, 17, 21, 22, 24, 139, 140, 141, 104, 40, 64], "values": [0.6368813514709473, 0.6368813514709473, 0.6130360960960388, 0.5421620011329651, 0.5375056266784668, 0.5375056266784668, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.49568283557891846, 0.46104589104652405, 0.46104589104652405, 0.4271812438964844, 0.4214109182357788, 0.4121088981628418, 0.4103316068649292, 0.40282025933265686, 0.38614192605018616, 0.3760111331939697, 0.3721415102481842, 0.36416345834732056, 0.3479672372341156, 0.3272571563720703, 0.3272571563720703, 0.3245006203651428, 0.3203641474246979, 0.31175732612609863, 0.31175732612609863, 0.2979270815849304, 0.2969631552696228, 0.29581066966056824, 0.2801106572151184, 0.2711765766143799, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.2657364308834076, 0.26397550106048584, 0.26295462250709534, 0.2629290223121643, 0.25873640179634094, 0.2566879689693451, 0.2504494786262512, 0.2504494786262512, 0.2430320680141449, 0.23912516236305237, 0.23912516236305237, 0.23912516236305237, 0.23463478684425354, 0.23463478684425354, 0.23280125856399536, 0.2311907708644867, 0.2311907708644867, 0.22300760447978973, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.2225874364376068, 0.21124772727489471, 0.21124772727489471, 0.21124772727489471, 0.2102745771408081, 0.20816147327423096, 0.20361432433128357, 0.2030743658542633, 0.19641417264938354, 0.1943962126970291, 0.1915108859539032, 0.18737488985061646, 0.18413467705249786, 0.17745643854141235, 0.17745643854141235, 0.17745643854141235, 0.1760486364364624, 0.1760486364364624, 0.17468427121639252, 0.1661725640296936, 0.16441266238689423, 0.15954509377479553, 0.15381674468517303, 0.15381674468517303, 0.15104883909225464, 0.14442673325538635, 0.13740898668766022, 0.13240142166614532, 0.13176646828651428, 0.12431777268648148, 0.12279310822486877, 0.12279310822486877, 0.12142190337181091, 0.12142190337181091, 0.12142190337181091, 0.11630198359489441, 0.11599351465702057, 0.11398488283157349]}, "2": {"indices": [34, 35, 23, 9, 46, 28, 29, 30, 22, 24, 21, 47, 32, 33, 26, 27, 45, 25, 0, 65, 6, 39, 13, 14, 79, 80, 81, 82, 83, 87, 88, 17, 147, 40, 3, 4, 5, 66, 36, 146, 1, 20, 7, 15, 16, 67, 31, 77, 78, 76, 132, 142, 148, 149, 150, 151, 85, 86, 84, 144, 145, 8, 37, 89, 75, 38, 122, 11, 12, 44, 42, 43, 137, 118, 123, 124, 138, 134, 129, 10, 115, 41, 139, 140, 141, 60, 61, 128, 71, 72, 125, 62, 63, 131, 135, 136, 19, 48, 133, 50], "values": [0.6814258098602295, 0.6722895503044128, 0.6695839166641235, 0.6259387135505676, 0.6024741530418396, 0.5824971199035645, 0.5824971199035645, 0.5824971199035645, 0.5680152177810669, 0.5680152177810669, 0.4911673069000244, 0.4871631860733032, 0.45073726773262024, 0.45073726773262024, 0.4268677830696106, 0.4268677830696106, 0.4268677830696106, 0.376714289188385, 0.3537614941596985, 0.34439730644226074, 0.33253952860832214, 0.3217625916004181, 0.3176993727684021, 0.3176993727684021, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.3020108938217163, 0.2996395528316498, 0.28956639766693115, 0.2835095524787903, 0.2568712830543518, 0.2568712830543518, 0.2568712830543518, 0.2430286705493927, 0.2380228042602539, 0.2379739135503769, 0.2350529432296753, 0.22470572590827942, 0.22204771637916565, 0.22017446160316467, 0.22017446160316467, 0.22016121447086334, 0.21909378468990326, 0.20961999893188477, 0.20961999893188477, 0.2049424648284912, 0.20417499542236328, 0.20354653894901276, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19913601875305176, 0.19900810718536377, 0.19900810718536377, 0.19178065657615662, 0.18584679067134857, 0.18584679067134857, 0.18242135643959045, 0.17864935100078583, 0.17281433939933777, 0.17222051322460175, 0.17136624455451965, 0.16804558038711548, 0.16254693269729614, 0.1495170295238495, 0.14899128675460815, 0.14316879212856293, 0.14316879212856293, 0.11986328661441803, 0.11767048388719559, 0.11767048388719559, 0.11767048388719559, 0.11017382144927979, 0.1099415123462677, 0.10691234469413757, 0.10325178503990173, 0.09600088745355606, 0.09595703333616257, 0.0950678139925003, 0.0950678139925003, 0.0950678139925003, 0.09348975121974945, 0.09348975121974945, 0.09135644137859344, 0.07336081564426422, 0.07336081564426422, 0.07084788382053375, 0.06675050407648087, 0.06675050407648087, 0.05971590802073479, 0.05762607976794243, 0.05762607976794243, 0.0509728267788887, 0.044816967099905014, 0.04220733046531677, 0.040773291140794754]}, "3": {"indices": [7, 388, 361, 390, 391, 5, 90, 385, 389, 141, 145, 209, 383, 239, 266, 267, 252, 30, 6, 8, 716, 955, 4024, 25, 3812, 4014, 9, 17, 26, 27, 253, 254, 1440, 956, 467, 650, 1083, 2075, 2101, 792, 793, 794, 3177, 465, 4008, 4009, 4324, 332, 3041, 3209, 3210, 3211, 3212, 3815, 4321, 29, 41, 338, 48, 2643, 392, 397, 150, 2889, 3695, 717, 4087, 4141, 4169, 2828, 31, 466, 368, 369, 370, 4267, 4293, 2347, 2348, 1447, 1448, 642, 1131, 651, 4028, 4029, 1487, 1495, 1496, 623, 723, 745, 89, 4092, 111, 244, 4048, 32, 1702, 811], "values": [0.998096227645874, 0.998096227645874, 0.96403568983078, 0.96403568983078, 0.96403568983078, 0.8484044671058655, 0.8484044671058655, 0.8484044671058655, 0.8484044671058655, 0.8338472843170166, 0.8338472843170166, 0.8338472843170166, 0.8338472843170166, 0.8323735594749451, 0.8323735594749451, 0.8323735594749451, 0.794776439666748, 0.7915468215942383, 0.7483518123626709, 0.7483518123626709, 0.7483518123626709, 0.7483518123626709, 0.7375943660736084, 0.7054058909416199, 0.6772313117980957, 0.6689291000366211, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6636675000190735, 0.6569876670837402, 0.6442821025848389, 0.6365141868591309, 0.6365141868591309, 0.6365141868591309, 0.629892110824585, 0.629892110824585, 0.6196168661117554, 0.6196168661117554, 0.6196168661117554, 0.6178841590881348, 0.6126296520233154, 0.6090183258056641, 0.6090183258056641, 0.6090183258056641, 0.5983366370201111, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5976386070251465, 0.5954737663269043, 0.5914321541786194, 0.5911408066749573, 0.5911408066749573, 0.5821852684020996, 0.582026481628418, 0.5792378187179565, 0.5788934230804443, 0.5788934230804443, 0.5758121609687805, 0.5700093507766724, 0.5624727010726929, 0.5617824196815491, 0.5612939596176147, 0.5612939596176147, 0.5605912208557129, 0.5588138103485107, 0.5572633743286133, 0.5572633743286133, 0.5537252426147461, 0.5537252426147461, 0.5537252426147461, 0.5429188013076782, 0.5429188013076782, 0.5398932695388794, 0.5398932695388794, 0.5395883917808533, 0.5395883917808533, 0.5388590693473816, 0.5358274579048157, 0.5333741307258606, 0.5283689498901367, 0.5283689498901367, 0.5277594923973083, 0.5265169143676758, 0.5265169143676758, 0.5252052545547485, 0.5252052545547485, 0.5252052545547485, 0.523034930229187, 0.5196403861045837, 0.5169724225997925, 0.5169724225997925, 0.5166342854499817, 0.514980673789978, 0.5144572257995605, 0.5087020397186279]}, "4": {"indices": [218, 227, 205, 49, 50, 84, 174, 113, 114, 117, 119, 120, 225, 85, 202, 156, 172, 135, 122, 128, 171, 10, 151, 242, 243, 170, 102, 199, 226, 145, 146, 169, 54, 115, 63, 65, 100, 109, 110, 111, 112, 232, 233, 4, 37, 53, 103, 104, 125, 139, 140, 38, 150, 0, 27, 29, 173, 93, 240, 206, 87, 97, 101, 106, 121, 123, 134, 141, 7, 45, 180, 223, 200, 86, 46, 9, 39, 60, 67, 71, 74, 75, 78, 79, 152, 153, 230, 196, 72, 229, 231, 47, 236, 213, 5, 6, 62, 12, 182, 183], "values": [0.6727880835533142, 0.6727880835533142, 0.6654847264289856, 0.6267021298408508, 0.6267021298408508, 0.6267021298408508, 0.6267021298408508, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.6004139184951782, 0.5948402881622314, 0.5423608422279358, 0.5334746837615967, 0.5155859589576721, 0.5155859589576721, 0.5112175941467285, 0.4868689179420471, 0.47872042655944824, 0.4711094796657562, 0.46946194767951965, 0.45085155963897705, 0.44897758960723877, 0.44897758960723877, 0.42708897590637207, 0.4212993383407593, 0.4209632873535156, 0.4142408072948456, 0.41334739327430725, 0.41334739327430725, 0.41242584586143494, 0.4122968912124634, 0.4077647924423218, 0.40774673223495483, 0.40774673223495483, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.407214879989624, 0.40648454427719116, 0.40648454427719116, 0.40642672777175903, 0.4036613404750824, 0.4034668803215027, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3961010277271271, 0.3908003270626068, 0.3874143958091736, 0.3837318420410156, 0.3837318420410156, 0.3828738331794739, 0.37821635603904724, 0.3762022852897644, 0.3726189136505127, 0.3642114996910095, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.36254653334617615, 0.3393932580947876, 0.33476537466049194, 0.330272376537323, 0.3265085220336914, 0.3195851147174835, 0.31815898418426514, 0.3178529143333435, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.3163018822669983, 0.31331169605255127, 0.31331169605255127, 0.3130655288696289, 0.3094156086444855, 0.30834561586380005, 0.30727407336235046, 0.3070598840713501, 0.3057669401168823, 0.30340784788131714, 0.2987590730190277, 0.2951938509941101, 0.2951938509941101, 0.2936801314353943, 0.2928728461265564, 0.2914232909679413, 0.2914232909679413]}, "5": {"indices": [853, 854, 860, 920, 1230, 1833, 1875, 2091, 3871, 4120, 3870, 3872, 3873, 2185, 4071, 4473, 2176, 2215, 2216, 2320, 2433, 2481, 2577, 2695, 2697, 2698, 2730, 2731, 2732, 2733, 2855, 2859, 2863, 4155, 4465, 923, 960, 1002, 1020, 4184, 2217, 2208, 4353, 290, 4701, 2615, 2646, 2683, 2692, 4190, 4191, 4192, 2589, 2629, 2640, 2693, 2696, 2701, 2704, 2713, 2716, 2754, 2767, 2786, 4349, 4606, 4624, 4426, 1219, 3266, 3536, 3544, 3548, 3549, 4026, 3546, 2717, 2777, 2785, 3419, 3422, 3440, 4565, 4584, 4607, 4608, 4622, 4826, 3072, 3600, 4699, 3925, 3928, 4189, 4639, 17, 4099, 782, 3541, 3545], "values": [0.997597336769104, 0.997597336769104, 0.997597336769104, 0.997597336769104, 0.9653436541557312, 0.8154235482215881, 0.8154235482215881, 0.7734938859939575, 0.6917835474014282, 0.6856277585029602, 0.6541063785552979, 0.6541063785552979, 0.6541063785552979, 0.6524826884269714, 0.6469953060150146, 0.6469953060150146, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6438274383544922, 0.6395663022994995, 0.6333271861076355, 0.622814416885376, 0.622814416885376, 0.622814416885376, 0.622814416885376, 0.6091465950012207, 0.6068289279937744, 0.6058014631271362, 0.6032325029373169, 0.6014668345451355, 0.6011438965797424, 0.6000787615776062, 0.6000787615776062, 0.6000787615776062, 0.6000787615776062, 0.598292350769043, 0.598292350769043, 0.598292350769043, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5902436971664429, 0.5893754959106445, 0.5871177911758423, 0.5871177911758423, 0.5850570797920227, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5756991505622864, 0.5722397565841675, 0.5717297196388245, 0.5717297196388245, 0.5717297196388245, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5609341263771057, 0.5585320591926575, 0.5538491606712341, 0.5538491606712341, 0.5519065856933594, 0.5479217171669006, 0.5479217171669006, 0.5475368499755859, 0.544042706489563, 0.540373682975769, 0.53767991065979, 0.5348735451698303, 0.5346747040748596, 0.5346747040748596]}, "6": {"indices": [284, 240, 143, 144, 146, 147, 148, 827, 285, 145, 3236, 1051, 3060, 1014, 1501, 1502, 149, 327, 328, 329, 2767, 87, 4094, 4095, 4097, 4098, 4313, 996, 1040, 1060, 3858, 2345, 2489, 2492, 2493, 2494, 1710, 2497, 1086, 3497, 3221, 3222, 3474, 3475, 3479, 3480, 116, 2791, 3069, 3070, 3071, 3073, 3081, 3082, 3133, 3134, 3231, 4542, 927, 1767, 115, 4491, 3176, 4656, 4742, 4857, 4500, 1008, 289, 834, 835, 826, 527, 778, 781, 1080, 531, 602, 615, 2760, 3784, 2811, 3137, 3140, 553, 554, 556, 557, 558, 75, 2064, 2065, 2137, 2714, 828, 2523, 2524, 2525, 3477, 3233], "values": [0.7299851179122925, 0.669043779373169, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6653361320495605, 0.6370207071304321, 0.6262259483337402, 0.5562720894813538, 0.5162757635116577, 0.510110080242157, 0.49499964714050293, 0.4822402000427246, 0.4616070091724396, 0.4616070091724396, 0.4592355489730835, 0.4427684545516968, 0.4427684545516968, 0.4427684545516968, 0.42584389448165894, 0.42430925369262695, 0.4167355000972748, 0.4167355000972748, 0.4167355000972748, 0.4167355000972748, 0.4137309491634369, 0.41143158078193665, 0.41143158078193665, 0.41143158078193665, 0.3971516191959381, 0.3949267864227295, 0.3925682306289673, 0.3925682306289673, 0.3925682306289673, 0.3925682306289673, 0.39021700620651245, 0.3899136185646057, 0.3894136846065521, 0.3881523013114929, 0.38810819387435913, 0.38810819387435913, 0.38710981607437134, 0.38710981607437134, 0.38710981607437134, 0.38710981607437134, 0.3845381736755371, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38422542810440063, 0.38258543610572815, 0.3819728493690491, 0.3819728493690491, 0.3819403350353241, 0.38018208742141724, 0.3773539364337921, 0.3761323392391205, 0.3761323392391205, 0.3761323392391205, 0.37604665756225586, 0.37510091066360474, 0.37265336513519287, 0.3708555996417999, 0.3708555996417999, 0.36594754457473755, 0.3631155490875244, 0.3631155490875244, 0.3631155490875244, 0.35949429869651794, 0.35869044065475464, 0.3522747755050659, 0.3522747755050659, 0.3518388271331787, 0.3494141101837158, 0.34681546688079834, 0.34681546688079834, 0.34681546688079834, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.34418147802352905, 0.3431001305580139, 0.3427882790565491, 0.3427882790565491, 0.3427354097366333, 0.3427330255508423, 0.3412012457847595, 0.3410893678665161, 0.3410893678665161, 0.3410893678665161, 0.3374759554862976, 0.33630362153053284]}, "7": {"indices": [34, 7, 11, 14, 123, 12, 46, 33, 36, 41, 42, 13, 15, 29, 43, 35, 104, 30, 98, 8, 9, 77, 6, 207, 31, 27, 28, 37, 172, 40, 137, 138, 139, 140, 68, 63, 99, 193, 184, 112, 113, 32, 155, 91, 26, 10, 25, 199, 200, 201, 39, 158, 159, 160, 161, 162, 163, 52, 54, 55, 56, 57, 210, 110, 118, 108, 150, 152, 153, 97, 89, 24, 194, 180, 94, 197, 93, 44, 58, 213, 59, 38, 166, 174, 5, 96, 53, 50, 121, 122, 212, 103, 105, 164, 205, 71, 101, 100, 45, 188], "values": [0.8355023264884949, 0.681782603263855, 0.6296814680099487, 0.6296814680099487, 0.6047410368919373, 0.6017360091209412, 0.5437449216842651, 0.5329038500785828, 0.5220320820808411, 0.5220320820808411, 0.5220320820808411, 0.5126869678497314, 0.4902424216270447, 0.47889798879623413, 0.4739780128002167, 0.4519338309764862, 0.4385482668876648, 0.42581817507743835, 0.4230218529701233, 0.41863685846328735, 0.41278326511383057, 0.4037785530090332, 0.3941415846347809, 0.38613197207450867, 0.3732340931892395, 0.3725154995918274, 0.3725154995918274, 0.3567515015602112, 0.3514118790626526, 0.34868812561035156, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.34569475054740906, 0.3266542851924896, 0.3079492747783661, 0.30712729692459106, 0.299442321062088, 0.2974482476711273, 0.295329213142395, 0.295329213142395, 0.2951374650001526, 0.2871512174606323, 0.2805454134941101, 0.27769935131073, 0.2767360806465149, 0.2754376530647278, 0.2736871838569641, 0.2736871838569641, 0.2736871838569641, 0.2677212953567505, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.264226496219635, 0.2632717192173004, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.26155272126197815, 0.2615187168121338, 0.2609832286834717, 0.2609832286834717, 0.25709396600723267, 0.25516828894615173, 0.25516828894615173, 0.25516828894615173, 0.2513769268989563, 0.2447986900806427, 0.24375922977924347, 0.23914113640785217, 0.23304912447929382, 0.22976645827293396, 0.22899697721004486, 0.22809219360351562, 0.22308345139026642, 0.2228611409664154, 0.22057557106018066, 0.2191094160079956, 0.21338146924972534, 0.21199211478233337, 0.21046307682991028, 0.1994990110397339, 0.19864699244499207, 0.19803595542907715, 0.19719743728637695, 0.1955522894859314, 0.1955522894859314, 0.19510281085968018, 0.19480031728744507, 0.19480031728744507, 0.18750925362110138, 0.18647556006908417, 0.176116481423378, 0.17452888190746307, 0.1719837784767151, 0.17080917954444885, 0.1696484535932541]}, "8": {"indices": [446, 473, 575, 597, 604, 605, 606, 607, 614, 461, 1433, 1700, 1348, 611, 1571, 1572, 1573, 412, 413, 414, 627, 621, 132, 133, 525, 526, 130, 1944, 245, 248, 1166, 425, 422, 1943, 2193, 1930, 472, 1781, 145, 146, 127, 1462, 1463, 1845, 894, 2002, 2003, 2014, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 444, 445, 468, 469, 470, 1295, 1296, 1297, 1298, 128, 154, 1379, 1975, 148, 149, 343, 1299, 925, 1658, 2683, 1984, 1993, 2012, 2039, 618, 619, 653, 654, 1522, 1739, 1784, 1785, 1787, 1788, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 198, 1211], "values": [0.902949333190918, 0.902949333190918, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8728811740875244, 0.8544977307319641, 0.7976263761520386, 0.7705298662185669, 0.754043698310852, 0.7449561357498169, 0.7004261016845703, 0.7004261016845703, 0.7004261016845703, 0.6543985605239868, 0.6543985605239868, 0.6543985605239868, 0.6541460156440735, 0.6469646692276001, 0.6374890208244324, 0.6374890208244324, 0.6261798143386841, 0.6261798143386841, 0.5893785953521729, 0.5725457668304443, 0.5655273199081421, 0.5655273199081421, 0.552666187286377, 0.5463614463806152, 0.5361401438713074, 0.5359000563621521, 0.5166508555412292, 0.515923023223877, 0.5067808628082275, 0.5060935616493225, 0.5055873990058899, 0.5055873990058899, 0.5048724412918091, 0.5048724412918091, 0.5048724412918091, 0.5004656314849854, 0.49906668066978455, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.49158504605293274, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.48708489537239075, 0.483339786529541, 0.483339786529541, 0.483339786529541, 0.483339786529541, 0.4807903468608856, 0.4807903468608856, 0.46956610679626465, 0.46956610679626465, 0.46239835023880005, 0.46239835023880005, 0.4590466022491455, 0.45570850372314453, 0.44624263048171997, 0.4413162171840668, 0.4395539164543152, 0.43554890155792236, 0.43554890155792236, 0.43554890155792236, 0.43554890155792236, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.43318670988082886, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.433139830827713, 0.43139150738716125, 0.4235597252845764]}, "9": {"indices": [1572, 4631, 4562, 4033, 4217, 2593, 2672, 2592, 2228, 2694, 2697, 36, 2226, 2227, 3384, 780, 440, 505, 3957, 31, 580, 588, 504, 4755, 4759, 626, 2710, 175, 2201, 1771, 39, 4136, 581, 587, 828, 829, 830, 1092, 4260, 2229, 2556, 2714, 2184, 3918, 4102, 4103, 4129, 4757, 4758, 4762, 4241, 4187, 4188, 1543, 2669, 383, 15, 4286, 4101, 4104, 2234, 30, 525, 619, 620, 1090, 3383, 377, 506, 507, 658, 437, 439, 3142, 549, 508, 509, 1732, 4756, 4761, 1978, 3563, 3564, 784, 1948, 2061, 4429, 1697, 3933, 55, 1836, 625, 1655, 2327, 953, 954, 4470, 4477, 4478, 4479], "values": [0.9976937770843506, 0.8739716410636902, 0.8480227589607239, 0.7937148213386536, 0.7937148213386536, 0.6930517554283142, 0.6842337846755981, 0.6835708618164062, 0.6583209037780762, 0.6583209037780762, 0.6583209037780762, 0.6419944763183594, 0.6419944763183594, 0.6419944763183594, 0.6286375522613525, 0.5992338061332703, 0.5823986530303955, 0.5785138607025146, 0.5758242011070251, 0.5682200789451599, 0.5628348588943481, 0.5628348588943481, 0.5565475225448608, 0.5520271062850952, 0.5520271062850952, 0.548314094543457, 0.5477088689804077, 0.539419412612915, 0.5339654684066772, 0.5151779651641846, 0.5073480010032654, 0.507339358329773, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.502739429473877, 0.4911983609199524, 0.4906250834465027, 0.4906250834465027, 0.4906250834465027, 0.4868679940700531, 0.47855913639068604, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47816193103790283, 0.47748497128486633, 0.476895272731781, 0.476895272731781, 0.47655922174453735, 0.47655922174453735, 0.4758196473121643, 0.46803754568099976, 0.4650793671607971, 0.46462029218673706, 0.46462029218673706, 0.4608319401741028, 0.4583824872970581, 0.4579876959323883, 0.45542824268341064, 0.45542824268341064, 0.45382043719291687, 0.453685462474823, 0.4494936168193817, 0.4494936168193817, 0.4494936168193817, 0.4494936168193817, 0.44627612829208374, 0.44627612829208374, 0.44188714027404785, 0.4414485692977905, 0.43964263796806335, 0.43964263796806335, 0.43504583835601807, 0.43389472365379333, 0.43244120478630066, 0.41538047790527344, 0.4121907949447632, 0.4121907949447632, 0.4105769395828247, 0.4105769395828247, 0.4105769395828247, 0.4105769395828247, 0.40724247694015503, 0.4063498377799988, 0.4035816192626953, 0.40241479873657227, 0.4008207321166992, 0.4008207321166992, 0.39952653646469116, 0.39943891763687134, 0.39943891763687134, 0.39904430508613586, 0.39904430508613586, 0.39904430508613586, 0.39904430508613586]}, "10": {"indices": [190, 195, 207, 967, 390, 1274, 1593, 1231, 1267, 1273, 1302, 1313, 1329, 1429, 1453, 1475, 619, 1214, 1596, 576, 682, 722, 1581, 1582, 1594, 1120, 201, 205, 206, 1584, 1261, 1268, 1449, 1481, 1493, 1625, 1627, 1630, 723, 812, 1373, 607, 613, 720, 429, 658, 498, 1597, 1679, 236, 374, 393, 396, 451, 509, 510, 513, 556, 569, 570, 575, 582, 602, 614, 617, 639, 642, 643, 714, 719, 1352, 1400, 1599, 1601, 1600, 1452, 1476, 430, 1149, 1180, 1207, 1211, 452, 1263, 1619, 1441, 131, 139, 1612, 1628, 1605, 679, 691, 724, 743, 748, 790, 800, 809, 819], "values": [0.997897744178772, 0.997897744178772, 0.997897744178772, 0.8982410430908203, 0.89687180519104, 0.89687180519104, 0.89687180519104, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8950674533843994, 0.8817000389099121, 0.8817000389099121, 0.8817000389099121, 0.8746421933174133, 0.8746421933174133, 0.8746421933174133, 0.8609449863433838, 0.8609449863433838, 0.8609449863433838, 0.8599758148193359, 0.8591632843017578, 0.8591632843017578, 0.8591632843017578, 0.844992995262146, 0.843886137008667, 0.843886137008667, 0.8422084450721741, 0.8422084450721741, 0.8422084450721741, 0.8406334519386292, 0.8406334519386292, 0.8406334519386292, 0.8389067649841309, 0.8389067649841309, 0.8385676741600037, 0.8383581042289734, 0.8383581042289734, 0.8383581042289734, 0.8373757004737854, 0.8373757004737854, 0.8321995735168457, 0.8317316770553589, 0.8317316770553589, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8305123448371887, 0.8298993110656738, 0.8297579884529114, 0.8266054391860962, 0.8266054391860962, 0.8260214328765869, 0.8247941732406616, 0.8247941732406616, 0.8217582702636719, 0.8191503286361694, 0.8191503286361694, 0.8191503286361694, 0.8191503286361694, 0.8171231746673584, 0.8138290643692017, 0.8082481026649475, 0.8061050176620483, 0.8055320978164673, 0.8055320978164673, 0.8048208355903625, 0.8048208355903625, 0.804445207118988, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782, 0.8028064966201782]}, "11": {"indices": [385, 1541, 481, 485, 486, 505, 515, 689, 988, 406, 1579, 1613, 375, 380, 386, 1682, 1328, 1414, 1415, 1417, 473, 1375, 1391, 873, 874, 1266, 881, 1461, 1117, 940, 412, 421, 422, 423, 427, 428, 435, 449, 478, 480, 1270, 1314, 1330, 1402, 1430, 1454, 1466, 1467, 1468, 1530, 442, 968, 1499, 1673, 1707, 408, 1508, 1543, 1306, 418, 447, 893, 757, 1133, 499, 69, 1708, 1199, 1200, 439, 29, 30, 52, 53, 54, 1222, 1573, 1662, 1463, 1478, 1496, 1552, 1555, 1558, 1559, 1576, 1587, 376, 1225, 1226, 1227, 584, 22, 67, 68, 75, 311, 490, 1519, 1534], "values": [0.9980266094207764, 0.972307562828064, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.953616201877594, 0.9466214179992676, 0.9466214179992676, 0.9445673227310181, 0.9385941028594971, 0.9385941028594971, 0.9385941028594971, 0.9351229667663574, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9341208934783936, 0.9144363403320312, 0.9061398506164551, 0.9061398506164551, 0.9003400802612305, 0.9003400802612305, 0.8952523469924927, 0.8945959806442261, 0.8901271820068359, 0.8890048265457153, 0.8869749307632446, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8708895444869995, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8646390438079834, 0.8571504354476929, 0.8571504354476929, 0.8571504354476929, 0.8489366769790649, 0.8447636365890503, 0.8447636365890503, 0.8447636365890503, 0.8443904519081116, 0.841084361076355, 0.8402484655380249, 0.8300219178199768, 0.8292279839515686, 0.8286341428756714, 0.8286341428756714, 0.8262461423873901, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8259555101394653, 0.8257873058319092, 0.8237709999084473, 0.8227684497833252, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.8221181035041809, 0.819891095161438, 0.8193692564964294, 0.8193692564964294, 0.8193692564964294, 0.8189754486083984, 0.8184829354286194, 0.8184688687324524, 0.8184688687324524, 0.8184688687324524, 0.8181168437004089, 0.817807674407959, 0.8173049688339233, 0.8173049688339233]}, "12": {"indices": [224, 225, 226, 221, 227, 218, 219, 220, 170, 198, 185, 210, 169, 183, 76, 70, 73, 233, 280, 300, 330, 361, 362, 16, 223, 376, 128, 33, 43, 146, 358, 106, 176, 239, 192, 272, 273, 274, 277, 360, 147, 209, 62, 126, 196, 387, 388, 389, 390, 391, 392, 2, 186, 112, 113, 39, 64, 68, 153, 154, 199, 350, 351, 211, 278, 152, 355, 177, 56, 44, 322, 377, 378, 250, 18, 156, 157, 174, 114, 117, 119, 120, 175, 144, 30, 31, 32, 28, 59, 332, 247, 51, 27, 71, 72, 327, 96, 93, 214, 248], "values": [0.8441696166992188, 0.8441696166992188, 0.8441696166992188, 0.6860100626945496, 0.6860100626945496, 0.6748876571655273, 0.6748876571655273, 0.6748876571655273, 0.5475037097930908, 0.5125709176063538, 0.5025202631950378, 0.4915836453437805, 0.48502081632614136, 0.4622817635536194, 0.4584325850009918, 0.4546782970428467, 0.4546782970428467, 0.45419225096702576, 0.4423740804195404, 0.44224774837493896, 0.42768895626068115, 0.42768895626068115, 0.42768895626068115, 0.42766499519348145, 0.40943682193756104, 0.4092572033405304, 0.3999294638633728, 0.394265353679657, 0.3896658420562744, 0.382973849773407, 0.37797874212265015, 0.37291574478149414, 0.36719292402267456, 0.36514878273010254, 0.3624955117702484, 0.3624374270439148, 0.3624374270439148, 0.3624374270439148, 0.3624374270439148, 0.3582462668418884, 0.3581291437149048, 0.3581291437149048, 0.3513772785663605, 0.3512735962867737, 0.3512735962867737, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.3495045602321625, 0.34719908237457275, 0.34667831659317017, 0.34138110280036926, 0.34138110280036926, 0.3380025029182434, 0.3364969491958618, 0.3360591232776642, 0.3356434404850006, 0.3356434404850006, 0.33248603343963623, 0.3265199661254883, 0.3265199661254883, 0.3217012584209442, 0.3167683482170105, 0.3148108124732971, 0.3144241273403168, 0.3139753043651581, 0.3092193007469177, 0.3000544011592865, 0.2933530807495117, 0.2927124500274658, 0.2927124500274658, 0.29259634017944336, 0.2903120815753937, 0.2876487970352173, 0.2876487970352173, 0.2876487970352173, 0.28751710057258606, 0.2872055470943451, 0.2872055470943451, 0.2872055470943451, 0.2870335876941681, 0.2834073007106781, 0.28044554591178894, 0.28044554591178894, 0.28044554591178894, 0.2798529267311096, 0.278662770986557, 0.27781033515930176, 0.2761780321598053, 0.27488455176353455, 0.27470386028289795, 0.27428141236305237, 0.27428141236305237, 0.2736201882362366, 0.2731330990791321, 0.27218911051750183, 0.27175137400627136, 0.27164822816848755]}, "13": {"indices": [258, 255, 259, 257, 260, 520, 256, 496, 561, 2819, 481, 512, 513, 556, 557, 2461, 2815, 2816, 276, 558, 559, 986, 1014, 1016, 1017, 1018, 1019, 1021, 1026, 1027, 1031, 1032, 1033, 1034, 1036, 1037, 1038, 1847, 590, 585, 279, 212, 213, 281, 425, 2858, 262, 1364, 516, 509, 1050, 1053, 253, 274, 699, 700, 514, 216, 2508, 2242, 2594, 1973, 1974, 1975, 2111, 2112, 2007, 2009, 2440, 277, 278, 237, 2356, 1758, 1759, 2865, 2867, 2868, 471, 2822, 261, 2826, 1009, 2551, 2552, 1013, 1015, 1020, 1022, 1023, 1025, 1028, 1029, 1030, 988, 540, 287, 1361, 1363, 1830], "values": [0.648378312587738, 0.5952406525611877, 0.5797232389450073, 0.5584985017776489, 0.5576417446136475, 0.5485743284225464, 0.5472137928009033, 0.5427061915397644, 0.5205211639404297, 0.5108038187026978, 0.5072110891342163, 0.5050419569015503, 0.5050419569015503, 0.5011329650878906, 0.5011329650878906, 0.4985601007938385, 0.476604700088501, 0.476604700088501, 0.4711463451385498, 0.4683162569999695, 0.4683162569999695, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4665243625640869, 0.4637238085269928, 0.45465224981307983, 0.4532126486301422, 0.44437867403030396, 0.4424690008163452, 0.4424690008163452, 0.4381462335586548, 0.43698686361312866, 0.43602001667022705, 0.4339509606361389, 0.43089741468429565, 0.4306219220161438, 0.4233730435371399, 0.4233730435371399, 0.4233730435371399, 0.4228084087371826, 0.41561564803123474, 0.41540253162384033, 0.41540253162384033, 0.4148055613040924, 0.4109310507774353, 0.410712867975235, 0.4086611270904541, 0.4067486822605133, 0.40605926513671875, 0.40605926513671875, 0.40605926513671875, 0.4056534767150879, 0.4056534767150879, 0.4044385254383087, 0.4044385254383087, 0.40214642882347107, 0.39955267310142517, 0.39955267310142517, 0.3926612138748169, 0.3914484679698944, 0.3897818922996521, 0.3897818922996521, 0.3864520490169525, 0.3864520490169525, 0.3864520490169525, 0.3850623071193695, 0.38275575637817383, 0.38257354497909546, 0.3818899691104889, 0.38085299730300903, 0.37997400760650635, 0.37997400760650635, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3779275119304657, 0.3744429349899292, 0.37304556369781494, 0.37060385942459106, 0.3695283532142639, 0.3695283532142639, 0.36630064249038696]}, "14": {"indices": [274, 276, 2311, 467, 468, 254, 2387, 256, 2380, 509, 1050, 1053, 255, 2895, 478, 540, 1989, 1990, 1994, 1995, 1999, 2000, 2455, 1046, 1047, 1048, 275, 286, 2869, 281, 287, 489, 1581, 2551, 2552, 2322, 522, 267, 268, 269, 270, 271, 441, 731, 285, 2385, 2386, 733, 288, 2508, 2349, 279, 884, 2506, 263, 264, 940, 2146, 280, 2094, 2095, 277, 278, 252, 457, 283, 403, 648, 558, 559, 730, 732, 300, 1996, 258, 273, 772, 1100, 460, 461, 212, 213, 487, 726, 727, 728, 729, 1991, 1992, 1993, 1051, 472, 2105, 297, 262, 2027, 2039, 2040, 2044, 2046], "values": [0.6430260539054871, 0.6052654981613159, 0.604028582572937, 0.5947726964950562, 0.5947726964950562, 0.5943450331687927, 0.5777553915977478, 0.5759397745132446, 0.5676916837692261, 0.5478818416595459, 0.5478818416595459, 0.5478818416595459, 0.5465532541275024, 0.5372047424316406, 0.5337477922439575, 0.5279549360275269, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5274603962898254, 0.5106783509254456, 0.5016142725944519, 0.5016142725944519, 0.5016142725944519, 0.4961423873901367, 0.4960876405239105, 0.4952925443649292, 0.4927310049533844, 0.48135092854499817, 0.47914645075798035, 0.4720046818256378, 0.47103220224380493, 0.47103220224380493, 0.4698319137096405, 0.4657186269760132, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641680419445038, 0.4641437232494354, 0.4612700939178467, 0.45961397886276245, 0.45837944746017456, 0.45837944746017456, 0.45672479271888733, 0.45616239309310913, 0.4490036368370056, 0.4445439577102661, 0.4364176094532013, 0.43322116136550903, 0.43124422430992126, 0.42809155583381653, 0.42809155583381653, 0.42677149176597595, 0.4253457188606262, 0.42430466413497925, 0.4229259490966797, 0.4229259490966797, 0.422419011592865, 0.422419011592865, 0.4216668903827667, 0.42151808738708496, 0.4204244017601013, 0.4168950915336609, 0.4128885567188263, 0.41163143515586853, 0.41163143515586853, 0.4114909768104553, 0.4114909768104553, 0.40756136178970337, 0.4066689610481262, 0.3984348177909851, 0.3983434736728668, 0.39788639545440674, 0.39610159397125244, 0.3896326422691345, 0.3896326422691345, 0.3888190984725952, 0.3888190984725952, 0.3887830376625061, 0.38681942224502563, 0.38681942224502563, 0.38681942224502563, 0.38681942224502563, 0.3853195309638977, 0.3853195309638977, 0.3853195309638977, 0.3852720558643341, 0.38502731919288635, 0.3836010992527008, 0.3786434531211853, 0.378050297498703, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786, 0.37698155641555786]}, "15": {"indices": [553, 2753, 466, 2089, 2440, 756, 2503, 2547, 2140, 2048, 2389, 2390, 1361, 1363, 1821, 1840, 906, 1191, 228, 229, 232, 233, 234, 235, 236, 2608, 458, 469, 1107, 1277, 1278, 1316, 1582, 1583, 1100, 910, 911, 1024, 1035, 1164, 1165, 1166, 909, 2716, 238, 243, 244, 1248, 1249, 1264, 2352, 349, 174, 175, 2182, 2198, 2199, 2200, 725, 344, 405, 1644, 757, 770, 807, 847, 848, 849, 850, 851, 852, 853, 854, 862, 863, 864, 2695, 2849, 414, 1796, 549, 555, 2592, 982, 772, 444, 507, 508, 368, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 242, 1088, 682, 1106], "values": [0.6607229113578796, 0.6383124589920044, 0.590969443321228, 0.5166010856628418, 0.4858936369419098, 0.4634116590023041, 0.455611914396286, 0.45379626750946045, 0.453165739774704, 0.4498156011104584, 0.44659388065338135, 0.44659388065338135, 0.4438987076282501, 0.4438987076282501, 0.44376248121261597, 0.44376248121261597, 0.44337883591651917, 0.43862152099609375, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.4356463849544525, 0.43546491861343384, 0.43113893270492554, 0.43113893270492554, 0.4298458695411682, 0.4279545843601227, 0.4279545843601227, 0.4279545843601227, 0.4266261160373688, 0.4266261160373688, 0.42637932300567627, 0.4245290756225586, 0.4245290756225586, 0.4168010354042053, 0.4168010354042053, 0.4134634733200073, 0.4134634733200073, 0.4134634733200073, 0.4113650918006897, 0.4066130816936493, 0.40506765246391296, 0.40506765246391296, 0.40506765246391296, 0.39771637320518494, 0.39771637320518494, 0.39771637320518494, 0.3947366774082184, 0.39341580867767334, 0.39306145906448364, 0.39306145906448364, 0.391987681388855, 0.391987681388855, 0.391987681388855, 0.391987681388855, 0.38953697681427, 0.38903433084487915, 0.38903433084487915, 0.3882913887500763, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.3879203796386719, 0.38701868057250977, 0.3866688013076782, 0.3866102397441864, 0.38591915369033813, 0.3845248222351074, 0.3845248222351074, 0.3833513855934143, 0.38077622652053833, 0.3805825710296631, 0.38051676750183105, 0.38051676750183105, 0.38051676750183105, 0.3767510950565338, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.37559974193573, 0.3745117783546448, 0.37425705790519714, 0.37159454822540283, 0.3709631860256195]}, "16": {"indices": [347, 3310, 3314, 169, 3399, 1846, 1847, 4412, 3122, 1850, 1852, 1855, 1856, 3431, 4854, 3866, 957, 3304, 3305, 3307, 819, 4636, 760, 958, 3937, 1849, 1857, 1858, 3867, 4629, 78, 79, 80, 81, 331, 1848, 3105, 0, 10, 1987, 4832, 1862, 3876, 1939, 3933, 3942, 3944, 3945, 4518, 4562, 4627, 4630, 4834, 3939, 3940, 1827, 4136, 762, 763, 767, 3125, 3946, 4576, 3943, 2625, 4138, 4139, 3745, 3202, 2851, 2852, 2857, 2859, 1829, 961, 4821, 1890, 1892, 4392, 2503, 2447, 2451, 3301, 2647, 3112, 3103, 3306, 1029, 2061, 2062, 2332, 3123, 1828, 2392, 2393, 3104, 1284, 1445, 2498, 2320], "values": [0.8800671100616455, 0.6704864501953125, 0.6704864501953125, 0.6600865125656128, 0.6494617462158203, 0.6312734484672546, 0.6312734484672546, 0.6287825107574463, 0.6251044273376465, 0.6052433252334595, 0.6052433252334595, 0.6052433252334595, 0.6052433252334595, 0.5980453491210938, 0.5862072706222534, 0.5839033126831055, 0.5829372406005859, 0.5820208787918091, 0.5816234946250916, 0.5816234946250916, 0.5814765095710754, 0.5795917510986328, 0.579427182674408, 0.579427182674408, 0.5757958292961121, 0.5745910406112671, 0.5745910406112671, 0.5745910406112671, 0.5743648409843445, 0.571414589881897, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5691721439361572, 0.5611867308616638, 0.5540376305580139, 0.5439808964729309, 0.5439808964729309, 0.5439808964729309, 0.5411112308502197, 0.5410830974578857, 0.539827823638916, 0.5377488136291504, 0.5350841283798218, 0.5345295071601868, 0.5345295071601868, 0.5345295071601868, 0.5342665910720825, 0.5342665910720825, 0.5342665910720825, 0.5342665910720825, 0.532304584980011, 0.5306636691093445, 0.5306636691093445, 0.5270891189575195, 0.5246263146400452, 0.5239983797073364, 0.5239983797073364, 0.5239983797073364, 0.5221589207649231, 0.5210816264152527, 0.5159024000167847, 0.5156014561653137, 0.5153573155403137, 0.5141280889511108, 0.5141280889511108, 0.5127895474433899, 0.5085676312446594, 0.5078423619270325, 0.5078423619270325, 0.5078423619270325, 0.5078423619270325, 0.5075790882110596, 0.5056939125061035, 0.5041319727897644, 0.5009607672691345, 0.5009607672691345, 0.49849918484687805, 0.497590035200119, 0.49722155928611755, 0.49722155928611755, 0.49607378244400024, 0.49588140845298767, 0.4958782196044922, 0.4955293536186218, 0.49012184143066406, 0.4893382787704468, 0.4848805367946625, 0.4848805367946625, 0.4848805367946625, 0.48431292176246643, 0.48425307869911194, 0.4833680987358093, 0.4833680987358093, 0.4829413890838623, 0.4812370836734772, 0.4805285930633545, 0.4770299792289734, 0.4758847951889038]}, "17": {"indices": [29, 18, 8, 10, 24, 31, 22, 32, 6, 7, 19, 20, 11, 12, 25, 27, 21, 3, 28, 9, 34, 35, 26, 4, 23, 1, 2, 30, 0, 5, 13, 14, 15, 16, 17, 33], "values": [0.5468879342079163, 0.4839995503425598, 0.4822302758693695, 0.4822302758693695, 0.4798535108566284, 0.4701407551765442, 0.45251086354255676, 0.44526946544647217, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.441669225692749, 0.4345024824142456, 0.4345024824142456, 0.4065087139606476, 0.3430609405040741, 0.32818904519081116, 0.319267213344574, 0.2979602515697479, 0.2974632978439331, 0.29507964849472046, 0.29507964849472046, 0.291653573513031, 0.2882101535797119, 0.2847745418548584, 0.2840638756752014, 0.2840638756752014, 0.17938299477100372, 0.163307324051857, 0.16136708855628967, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 24, 26, 6, 7, 19, 20, 8, 10, 31, 28, 9, 21, 27, 30, 29, 23, 25, 4, 11, 12, 1, 2, 5, 34, 35, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.7020464539527893, 0.6643305420875549, 0.6235466599464417, 0.6023511290550232, 0.5851036310195923, 0.5851036310195923, 0.5851036310195923, 0.5851036310195923, 0.5774502754211426, 0.5774502754211426, 0.5627787113189697, 0.5496057271957397, 0.5416020154953003, 0.5338107347488403, 0.5264153480529785, 0.5121941566467285, 0.4977238178253174, 0.4893684685230255, 0.4486841559410095, 0.44549816846847534, 0.44207727909088135, 0.44207727909088135, 0.4403478503227234, 0.4403478503227234, 0.41417911648750305, 0.39730221033096313, 0.39730221033096313, 0.36293449997901917, 0.34736812114715576, 0.2596661448478699, 0.14642265439033508, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [60, 61, 18, 79, 163, 161, 86, 87, 51, 52, 111, 57, 59, 119, 7, 43, 195, 210, 201, 206, 3, 105, 6, 62, 55, 175, 63, 64, 70, 215, 211, 58, 180, 208, 209, 172, 142, 143, 144, 126, 46, 69, 81, 189, 243, 65, 190, 2, 4, 5, 226, 194, 14, 16, 203, 120, 113, 74, 89, 198, 82, 207, 197, 110, 54, 80, 199, 181, 71, 124, 141, 23, 112, 114, 15, 233, 213, 234, 179, 42, 133, 128, 244, 212, 205, 196, 22, 103, 247, 237, 183, 214, 44, 236, 24, 27, 245, 122, 20, 200], "values": [0.8015655279159546, 0.8015655279159546, 0.800186276435852, 0.61016845703125, 0.5592929124832153, 0.5433822870254517, 0.5012836456298828, 0.5012836456298828, 0.48828208446502686, 0.48828208446502686, 0.47177058458328247, 0.44401758909225464, 0.44309577345848083, 0.4105152487754822, 0.39441967010498047, 0.3933994770050049, 0.37923234701156616, 0.36191999912261963, 0.35309937596321106, 0.34643805027008057, 0.33699870109558105, 0.3357974588871002, 0.33046865463256836, 0.30267563462257385, 0.2901327610015869, 0.28643521666526794, 0.27970343828201294, 0.27970343828201294, 0.2751466631889343, 0.267164409160614, 0.26096034049987793, 0.2465893179178238, 0.23076580464839935, 0.22697599232196808, 0.22697599232196808, 0.22587552666664124, 0.2134752869606018, 0.2134752869606018, 0.2134752869606018, 0.2103939950466156, 0.208261638879776, 0.19150957465171814, 0.18875542283058167, 0.18409103155136108, 0.18268805742263794, 0.18084567785263062, 0.1751769483089447, 0.17503681778907776, 0.17503681778907776, 0.17503681778907776, 0.17299127578735352, 0.16966332495212555, 0.16777139902114868, 0.16777139902114868, 0.16649237275123596, 0.16446571052074432, 0.1624225527048111, 0.15381458401679993, 0.15227308869361877, 0.1514841914176941, 0.148375004529953, 0.14314058423042297, 0.13890354335308075, 0.13805758953094482, 0.13666778802871704, 0.13666778802871704, 0.13666117191314697, 0.13298214972019196, 0.12461508810520172, 0.118744857609272, 0.118744857609272, 0.11287660151720047, 0.11221744120121002, 0.11221744120121002, 0.10249722748994827, 0.10192391276359558, 0.10059460252523422, 0.09994277358055115, 0.09781771153211594, 0.0939168706536293, 0.09305144846439362, 0.09253160655498505, 0.09212134033441544, 0.09025833010673523, 0.08891324698925018, 0.08774791657924652, 0.08358398824930191, 0.0821562260389328, 0.08053833246231079, 0.07908889651298523, 0.07271206378936768, 0.07227188348770142, 0.06770563125610352, 0.06676945090293884, 0.06549020111560822, 0.06529253721237183, 0.06423700600862503, 0.06327737867832184, 0.06179533526301384, 0.061229344457387924]}}

for i in range(20):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])

v1 =  {"0": {"indices": [395, 686, 434, 783, 785, 199, 396, 379, 397, 758, 778, 779, 787, 788, 789, 627, 182, 21, 89, 180, 181, 791, 203, 204, 207, 209, 811, 408, 409, 432, 410, 71, 110, 111, 796, 618, 620, 621, 622, 86, 87, 634, 535, 174, 534, 433, 545, 465, 469, 486, 487, 488, 105, 172, 214, 215, 163, 400, 200, 795, 205, 16, 793, 542, 161, 441, 40, 768, 769, 429, 217, 866, 626, 668, 674, 364, 673, 388, 398, 612, 166, 167, 613, 767, 411, 232, 631, 633, 558, 399, 404, 405, 406, 407, 152, 88, 173, 289, 489, 812], "values": [0.6240234375, 0.5869140625, 0.56005859375, 0.55126953125, 0.55126953125, 0.53759765625, 0.53515625, 0.52978515625, 0.52978515625, 0.51953125, 0.51953125, 0.51953125, 0.51904296875, 0.51904296875, 0.51904296875, 0.51220703125, 0.50390625, 0.492431640625, 0.492431640625, 0.492431640625, 0.492431640625, 0.487548828125, 0.455078125, 0.455078125, 0.449462890625, 0.449462890625, 0.443115234375, 0.439453125, 0.439453125, 0.422607421875, 0.4169921875, 0.403076171875, 0.403076171875, 0.403076171875, 0.392822265625, 0.38623046875, 0.38623046875, 0.38623046875, 0.38623046875, 0.383544921875, 0.383544921875, 0.378662109375, 0.373291015625, 0.36669921875, 0.36328125, 0.359130859375, 0.358642578125, 0.352294921875, 0.352294921875, 0.352294921875, 0.352294921875, 0.352294921875, 0.34716796875, 0.34716796875, 0.338623046875, 0.338623046875, 0.3359375, 0.3359375, 0.3330078125, 0.3310546875, 0.32861328125, 0.32568359375, 0.324951171875, 0.324462890625, 0.32421875, 0.31884765625, 0.31640625, 0.314453125, 0.314453125, 0.31298828125, 0.3125, 0.31005859375, 0.308349609375, 0.308349609375, 0.305419921875, 0.30419921875, 0.3037109375, 0.302978515625, 0.302978515625, 0.302001953125, 0.300537109375, 0.300537109375, 0.29833984375, 0.2978515625, 0.29541015625, 0.294189453125, 0.29248046875, 0.29248046875, 0.29150390625, 0.288330078125, 0.288330078125, 0.288330078125, 0.288330078125, 0.288330078125, 0.28564453125, 0.2841796875, 0.283935546875, 0.283935546875, 0.283935546875, 0.283935546875]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 88, 89, 90, 91, 92, 96, 97, 66, 67, 59, 9, 85, 48, 10, 21, 45, 139, 47, 60, 61, 62, 63, 94, 95, 53, 137, 98, 76, 15, 40, 157, 158, 159, 160, 138, 142, 74, 58, 0, 140, 77, 78, 17, 32, 106, 108, 109, 110, 111, 115, 135, 93, 80, 81, 107, 112, 114, 153, 154, 124, 127, 132, 133, 24, 25, 28, 134, 131, 57, 143, 82, 155, 27, 46, 125, 141, 22, 23, 31, 33, 35, 36, 54, 16, 147, 148, 149, 150, 19, 68, 79, 51, 52, 113], "values": [0.65478515625, 0.6494140625, 0.6494140625, 0.57177734375, 0.5302734375, 0.5302734375, 0.50634765625, 0.50634765625, 0.50634765625, 0.50634765625, 0.50634765625, 0.50634765625, 0.50634765625, 0.474853515625, 0.474853515625, 0.47314453125, 0.450439453125, 0.447265625, 0.444091796875, 0.432373046875, 0.432373046875, 0.428466796875, 0.410400390625, 0.40576171875, 0.388671875, 0.386962890625, 0.3671875, 0.3671875, 0.366943359375, 0.366943359375, 0.33837890625, 0.336669921875, 0.323486328125, 0.319580078125, 0.317138671875, 0.3095703125, 0.3095703125, 0.3095703125, 0.3095703125, 0.3095703125, 0.302490234375, 0.302001953125, 0.300048828125, 0.29833984375, 0.28955078125, 0.285888671875, 0.28271484375, 0.28271484375, 0.275390625, 0.27490234375, 0.274658203125, 0.274658203125, 0.274658203125, 0.274658203125, 0.274658203125, 0.274658203125, 0.27099609375, 0.26220703125, 0.259765625, 0.259765625, 0.25244140625, 0.25244140625, 0.25244140625, 0.247314453125, 0.247314453125, 0.2451171875, 0.2381591796875, 0.2381591796875, 0.2381591796875, 0.2349853515625, 0.2349853515625, 0.22216796875, 0.221435546875, 0.2161865234375, 0.2108154296875, 0.2012939453125, 0.201171875, 0.2010498046875, 0.197265625, 0.19091796875, 0.1907958984375, 0.1859130859375, 0.1844482421875, 0.1844482421875, 0.1776123046875, 0.1776123046875, 0.1754150390625, 0.1754150390625, 0.1754150390625, 0.16455078125, 0.157470703125, 0.1551513671875, 0.1551513671875, 0.1551513671875, 0.1490478515625, 0.1439208984375, 0.141845703125, 0.140869140625, 0.140869140625, 0.140625]}, "2": {"indices": [5, 32, 44, 4, 43, 55, 31, 33, 3, 18, 37, 38, 39, 8, 6, 30, 56, 7, 41, 42, 35, 36, 54, 34, 9, 15, 26, 75, 156, 74, 22, 23, 10, 48, 16, 88, 89, 90, 91, 92, 96, 97, 12, 13, 14, 29, 151, 86, 87, 40, 24, 25, 49, 76, 157, 158, 159, 160, 84, 85, 155, 46, 98, 94, 95, 0, 51, 52, 17, 131, 141, 21, 153, 154, 53, 2, 20, 19, 93, 47, 146, 71, 72, 69, 70, 138, 137, 148, 149, 150, 45, 50, 144, 145, 127, 132, 133, 147, 77, 78], "values": [0.7509765625, 0.6982421875, 0.673828125, 0.6513671875, 0.65087890625, 0.630859375, 0.619140625, 0.619140625, 0.6142578125, 0.6142578125, 0.5947265625, 0.5947265625, 0.5947265625, 0.54736328125, 0.5341796875, 0.497802734375, 0.490234375, 0.454833984375, 0.454833984375, 0.454833984375, 0.44580078125, 0.44580078125, 0.44580078125, 0.379150390625, 0.3359375, 0.33544921875, 0.315673828125, 0.311767578125, 0.310791015625, 0.31005859375, 0.303955078125, 0.303955078125, 0.29443359375, 0.281494140625, 0.28076171875, 0.278564453125, 0.278564453125, 0.278564453125, 0.278564453125, 0.278564453125, 0.278564453125, 0.278564453125, 0.277587890625, 0.277587890625, 0.277587890625, 0.27099609375, 0.26904296875, 0.266357421875, 0.266357421875, 0.263671875, 0.2568359375, 0.2568359375, 0.25439453125, 0.2489013671875, 0.2420654296875, 0.2420654296875, 0.2420654296875, 0.2420654296875, 0.239013671875, 0.23681640625, 0.2208251953125, 0.216552734375, 0.2135009765625, 0.1939697265625, 0.1939697265625, 0.190185546875, 0.185791015625, 0.185791015625, 0.18408203125, 0.1829833984375, 0.1781005859375, 0.1717529296875, 0.168701171875, 0.168701171875, 0.1541748046875, 0.1510009765625, 0.143310546875, 0.1414794921875, 0.140869140625, 0.13427734375, 0.1317138671875, 0.11724853515625, 0.11724853515625, 0.098876953125, 0.098876953125, 0.09735107421875, 0.0906982421875, 0.08843994140625, 0.08843994140625, 0.08843994140625, 0.0859375, 0.080810546875, 0.07574462890625, 0.07574462890625, 0.0745849609375, 0.0745849609375, 0.0745849609375, 0.07073974609375, 0.06982421875, 0.06982421875]}, "3": {"indices": [61, 442, 415, 444, 445, 59, 144, 439, 443, 293, 320, 321, 195, 199, 263, 437, 84, 306, 4078, 4068, 41, 45, 60, 62, 770, 1009, 79, 2129, 2155, 3866, 63, 71, 80, 81, 307, 308, 3231, 4062, 4063, 4378, 521, 704, 1137, 1010, 3, 519, 1494, 446, 451, 3095, 3263, 3264, 3265, 3266, 4321, 4347, 3869, 846, 847, 848, 2882, 4082, 4083, 3749, 392, 4772, 4773, 4774, 204, 4141, 4195, 85, 520, 1549, 1550, 422, 423, 424, 4223, 4375, 677, 777, 799, 2401, 2402, 386, 83, 95, 703, 705, 102, 4188, 52, 1541, 4146, 2943, 3837, 3847, 3849, 3857], "values": [0.998046875, 0.998046875, 0.9677734375, 0.9677734375, 0.9677734375, 0.85595703125, 0.85595703125, 0.85595703125, 0.85595703125, 0.84423828125, 0.84423828125, 0.84423828125, 0.8427734375, 0.8427734375, 0.8427734375, 0.8427734375, 0.80859375, 0.734375, 0.7197265625, 0.7041015625, 0.701171875, 0.701171875, 0.701171875, 0.701171875, 0.701171875, 0.701171875, 0.6904296875, 0.68994140625, 0.68994140625, 0.6689453125, 0.6474609375, 0.6474609375, 0.6474609375, 0.6474609375, 0.6474609375, 0.6474609375, 0.63916015625, 0.63720703125, 0.63720703125, 0.63720703125, 0.63623046875, 0.63623046875, 0.63623046875, 0.63427734375, 0.62939453125, 0.625, 0.61572265625, 0.609375, 0.609375, 0.60888671875, 0.60888671875, 0.60888671875, 0.60888671875, 0.60888671875, 0.6083984375, 0.6083984375, 0.60595703125, 0.6044921875, 0.6044921875, 0.6044921875, 0.60302734375, 0.5986328125, 0.5986328125, 0.58984375, 0.5830078125, 0.58251953125, 0.58251953125, 0.58251953125, 0.57958984375, 0.57958984375, 0.57958984375, 0.57763671875, 0.57763671875, 0.57666015625, 0.57666015625, 0.57275390625, 0.57275390625, 0.57275390625, 0.57080078125, 0.5703125, 0.56982421875, 0.56982421875, 0.56982421875, 0.56640625, 0.56640625, 0.560546875, 0.56005859375, 0.56005859375, 0.55908203125, 0.55859375, 0.556640625, 0.55517578125, 0.5546875, 0.55322265625, 0.552734375, 0.55224609375, 0.548828125, 0.548828125, 0.548828125, 0.548828125]}, "4": {"indices": [222, 231, 209, 53, 54, 88, 178, 117, 118, 121, 123, 124, 229, 89, 160, 176, 139, 175, 206, 132, 14, 246, 247, 174, 155, 67, 69, 119, 58, 236, 237, 33, 4, 31, 106, 104, 113, 114, 115, 116, 230, 57, 107, 108, 129, 143, 144, 126, 149, 150, 91, 101, 105, 110, 125, 127, 138, 145, 210, 41, 203, 42, 173, 154, 2, 8, 97, 156, 157, 50, 244, 49, 177, 11, 234, 227, 235, 233, 204, 12, 90, 218, 186, 187, 184, 1, 13, 43, 64, 71, 75, 78, 79, 82, 83, 223, 224, 243, 9, 10], "values": [0.68798828125, 0.68798828125, 0.685546875, 0.6240234375, 0.6240234375, 0.6240234375, 0.6240234375, 0.60888671875, 0.60888671875, 0.60888671875, 0.60888671875, 0.60888671875, 0.5888671875, 0.541015625, 0.5400390625, 0.5400390625, 0.51953125, 0.51953125, 0.5185546875, 0.50244140625, 0.461669921875, 0.460693359375, 0.460693359375, 0.457763671875, 0.45703125, 0.454345703125, 0.454345703125, 0.44970703125, 0.447021484375, 0.44287109375, 0.44287109375, 0.442138671875, 0.440185546875, 0.440185546875, 0.4384765625, 0.436279296875, 0.436279296875, 0.436279296875, 0.436279296875, 0.436279296875, 0.43603515625, 0.43359375, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.432373046875, 0.431640625, 0.431640625, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.424560546875, 0.422119140625, 0.416259765625, 0.3994140625, 0.39892578125, 0.39794921875, 0.397705078125, 0.3896484375, 0.386962890625, 0.386474609375, 0.386474609375, 0.3671875, 0.365478515625, 0.3642578125, 0.36376953125, 0.359130859375, 0.35595703125, 0.353515625, 0.353515625, 0.35205078125, 0.348388671875, 0.330078125, 0.329345703125, 0.32861328125, 0.319580078125, 0.319580078125, 0.316650390625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.308837890625, 0.308837890625, 0.30810546875, 0.3076171875, 0.3076171875]}, "5": {"indices": [953, 954, 960, 1020, 1330, 1933, 1975, 2191, 4220, 3971, 2276, 2315, 2316, 2420, 2533, 2581, 2677, 2795, 2797, 2798, 2830, 2831, 2832, 2833, 2955, 2959, 2963, 4453, 2285, 3970, 3972, 3973, 2715, 2746, 2783, 2792, 2689, 2729, 2740, 2793, 2796, 2801, 2804, 2813, 2816, 2854, 2867, 2886, 4284, 2308, 4255, 1023, 1060, 1102, 1120, 4801, 4526, 2817, 2877, 2885, 4171, 4573, 4290, 4291, 4292, 4289, 4706, 4724, 4565, 4449, 2317, 390, 1319, 3366, 3636, 3644, 3648, 3649, 4126, 4926, 3172, 3700, 3519, 3522, 3540, 4665, 4684, 4707, 4708, 4722, 2869, 4025, 4028, 882, 1050, 1053, 3646, 1051, 1037, 750], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.9658203125, 0.8193359375, 0.8193359375, 0.8037109375, 0.708984375, 0.70166015625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.68212890625, 0.67529296875, 0.67333984375, 0.66259765625, 0.66259765625, 0.66259765625, 0.65380859375, 0.65380859375, 0.65380859375, 0.65380859375, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.6533203125, 0.64697265625, 0.646484375, 0.64404296875, 0.638671875, 0.638671875, 0.638671875, 0.638671875, 0.6376953125, 0.63525390625, 0.6298828125, 0.6298828125, 0.6298828125, 0.626953125, 0.626953125, 0.62255859375, 0.62255859375, 0.62255859375, 0.619140625, 0.6181640625, 0.6181640625, 0.6162109375, 0.61474609375, 0.6142578125, 0.61083984375, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.60546875, 0.60009765625, 0.60009765625, 0.59228515625, 0.59228515625, 0.59228515625, 0.59228515625, 0.59228515625, 0.59228515625, 0.59228515625, 0.59228515625, 0.58642578125, 0.57666015625, 0.57666015625, 0.56591796875, 0.5654296875, 0.5654296875, 0.56298828125, 0.56005859375, 0.55810546875, 0.556640625]}, "6": {"indices": [304, 260, 163, 164, 166, 167, 168, 847, 305, 1071, 165, 3080, 3256, 1521, 1522, 1034, 169, 107, 2811, 3089, 3090, 3091, 3093, 3101, 3102, 3153, 3154, 3251, 1016, 1060, 1080, 347, 348, 349, 309, 3494, 3495, 3499, 3500, 135, 2787, 3241, 3242, 3517, 1730, 3878, 2517, 4484, 136, 1106, 3903, 3904, 551, 2780, 4333, 4114, 4115, 4117, 4118, 263, 3905, 2734, 947, 1787, 573, 574, 576, 577, 578, 854, 855, 95, 501, 2509, 2512, 2513, 2514, 3196, 547, 798, 801, 3909, 4076, 3497, 2365, 4511, 310, 1725, 1726, 1731, 1746, 1747, 846, 1100, 4520, 137, 1533, 1534, 1540, 1541], "values": [0.73046875, 0.67431640625, 0.6640625, 0.6640625, 0.6640625, 0.6640625, 0.6640625, 0.64306640625, 0.6123046875, 0.5302734375, 0.5263671875, 0.52099609375, 0.5107421875, 0.46630859375, 0.46630859375, 0.465087890625, 0.453857421875, 0.441650390625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.43017578125, 0.43017578125, 0.43017578125, 0.42626953125, 0.42626953125, 0.42626953125, 0.423828125, 0.422607421875, 0.422607421875, 0.422607421875, 0.422607421875, 0.4189453125, 0.407958984375, 0.40771484375, 0.40771484375, 0.407470703125, 0.406494140625, 0.40478515625, 0.39892578125, 0.3974609375, 0.397216796875, 0.397216796875, 0.397216796875, 0.397216796875, 0.396728515625, 0.392822265625, 0.392578125, 0.3916015625, 0.3916015625, 0.3916015625, 0.3916015625, 0.385498046875, 0.385009765625, 0.383056640625, 0.38232421875, 0.38232421875, 0.382080078125, 0.382080078125, 0.382080078125, 0.382080078125, 0.382080078125, 0.3818359375, 0.3818359375, 0.38037109375, 0.37939453125, 0.37939453125, 0.37939453125, 0.37939453125, 0.37939453125, 0.376220703125, 0.375732421875, 0.375732421875, 0.375732421875, 0.369873046875, 0.369873046875, 0.369140625, 0.36865234375, 0.365478515625, 0.362060546875, 0.36083984375, 0.36083984375, 0.36083984375, 0.36083984375, 0.36083984375, 0.3583984375, 0.353759765625, 0.353271484375, 0.35107421875, 0.350830078125, 0.350830078125, 0.350830078125, 0.350830078125]}, "7": {"indices": [51, 24, 28, 31, 29, 140, 30, 63, 50, 16, 32, 53, 58, 59, 60, 52, 46, 25, 47, 23, 26, 44, 45, 115, 121, 224, 7, 85, 48, 43, 54, 172, 42, 201, 94, 154, 155, 156, 157, 116, 80, 57, 129, 130, 6, 125, 71, 72, 73, 74, 69, 106, 189, 108, 27, 227, 56, 216, 217, 218, 15, 167, 169, 170, 41, 210, 175, 176, 177, 178, 179, 180, 49, 70, 111, 113, 127, 135, 211, 118, 110, 117, 120, 122, 114, 55, 191, 197, 222, 214, 61, 8, 9, 11, 12, 13, 230, 0, 2, 3], "values": [0.8447265625, 0.71630859375, 0.6591796875, 0.6591796875, 0.6220703125, 0.61083984375, 0.59130859375, 0.56396484375, 0.54296875, 0.5400390625, 0.52880859375, 0.515625, 0.515625, 0.515625, 0.51220703125, 0.45849609375, 0.4560546875, 0.4521484375, 0.451904296875, 0.44775390625, 0.442626953125, 0.442626953125, 0.442626953125, 0.44091796875, 0.4404296875, 0.43505859375, 0.409912109375, 0.391357421875, 0.390380859375, 0.3857421875, 0.384765625, 0.378662109375, 0.369140625, 0.365966796875, 0.36376953125, 0.36279296875, 0.36279296875, 0.36279296875, 0.36279296875, 0.35791015625, 0.35546875, 0.347900390625, 0.346923828125, 0.346923828125, 0.34619140625, 0.345458984375, 0.3427734375, 0.3427734375, 0.3427734375, 0.3427734375, 0.3388671875, 0.337646484375, 0.333740234375, 0.322265625, 0.314453125, 0.314453125, 0.307373046875, 0.3056640625, 0.3056640625, 0.3056640625, 0.302490234375, 0.3017578125, 0.3017578125, 0.3017578125, 0.301513671875, 0.293212890625, 0.29150390625, 0.29150390625, 0.29150390625, 0.29150390625, 0.29150390625, 0.29150390625, 0.283447265625, 0.275390625, 0.270751953125, 0.265869140625, 0.263916015625, 0.263916015625, 0.2626953125, 0.262451171875, 0.26123046875, 0.259521484375, 0.250732421875, 0.250732421875, 0.246826171875, 0.2459716796875, 0.2452392578125, 0.2431640625, 0.2362060546875, 0.23486328125, 0.233642578125, 0.22900390625, 0.22900390625, 0.22900390625, 0.22900390625, 0.22900390625, 0.2237548828125, 0.217529296875, 0.217529296875, 0.217529296875]}, "8": {"indices": [472, 499, 601, 623, 630, 631, 632, 633, 640, 487, 1459, 1726, 637, 1374, 1597, 1598, 1599, 653, 438, 439, 440, 647, 158, 159, 551, 552, 156, 271, 274, 451, 1970, 1969, 448, 153, 1488, 1489, 1192, 1956, 1871, 920, 2219, 1807, 498, 171, 172, 470, 471, 494, 495, 496, 154, 180, 2028, 2029, 2040, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 174, 175, 1321, 1322, 1323, 1324, 224, 1405, 2001, 1403, 1752, 1760, 1793, 1838, 1840, 1841, 1933, 369, 677, 333, 335, 336, 375, 379, 415, 503, 504, 505, 562, 563, 564, 600, 646, 1075, 1076, 1200, 1201], "values": [0.916015625, 0.916015625, 0.8828125, 0.8828125, 0.8828125, 0.8828125, 0.8828125, 0.8828125, 0.8828125, 0.8720703125, 0.81005859375, 0.76220703125, 0.748046875, 0.724609375, 0.67138671875, 0.67138671875, 0.67138671875, 0.64208984375, 0.640625, 0.640625, 0.640625, 0.63720703125, 0.619140625, 0.619140625, 0.61474609375, 0.61474609375, 0.57275390625, 0.5625, 0.5625, 0.55126953125, 0.54443359375, 0.52734375, 0.51513671875, 0.50439453125, 0.50439453125, 0.50439453125, 0.5, 0.49462890625, 0.494384765625, 0.492431640625, 0.492431640625, 0.4912109375, 0.490478515625, 0.490234375, 0.490234375, 0.486083984375, 0.486083984375, 0.486083984375, 0.486083984375, 0.486083984375, 0.48583984375, 0.48583984375, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.477294921875, 0.47314453125, 0.47314453125, 0.45849609375, 0.45849609375, 0.45849609375, 0.45849609375, 0.452880859375, 0.450439453125, 0.450439453125, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.439453125, 0.43701171875, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625, 0.43603515625]}, "9": {"indices": [1721, 4780, 4711, 4182, 4366, 2742, 2741, 2821, 185, 2375, 2376, 3533, 654, 2377, 2843, 2846, 114, 115, 122, 123, 589, 653, 4106, 929, 324, 775, 76, 180, 4904, 4908, 1920, 729, 737, 188, 4067, 730, 736, 977, 978, 979, 1241, 2859, 4390, 2350, 1692, 2818, 532, 4435, 4285, 657, 658, 674, 2333, 698, 179, 1228, 4409, 4250, 4253, 164, 526, 655, 656, 807, 4251, 4252, 4278, 4906, 4907, 4911, 2383, 1881, 1846, 91, 2378, 2705, 2863, 4905, 768, 769, 651, 806, 586, 588, 774, 1804, 4336, 4337, 3532, 1045, 1985, 4910, 2476, 4082, 933, 2097, 2210, 4578, 1102, 1103], "values": [0.99755859375, 0.89404296875, 0.84326171875, 0.8056640625, 0.8056640625, 0.6748046875, 0.66455078125, 0.6640625, 0.6591796875, 0.6591796875, 0.6591796875, 0.65478515625, 0.63623046875, 0.6240234375, 0.6240234375, 0.6240234375, 0.6181640625, 0.6181640625, 0.6181640625, 0.6181640625, 0.6181640625, 0.6103515625, 0.607421875, 0.60107421875, 0.5947265625, 0.58642578125, 0.56982421875, 0.56982421875, 0.560546875, 0.560546875, 0.54150390625, 0.53955078125, 0.53955078125, 0.5390625, 0.5390625, 0.52685546875, 0.52685546875, 0.52685546875, 0.52685546875, 0.52685546875, 0.52685546875, 0.52685546875, 0.5244140625, 0.521484375, 0.51318359375, 0.51318359375, 0.51123046875, 0.50830078125, 0.50146484375, 0.49755859375, 0.49755859375, 0.489990234375, 0.488525390625, 0.48486328125, 0.482177734375, 0.4814453125, 0.4794921875, 0.4775390625, 0.4775390625, 0.47412109375, 0.473388671875, 0.473388671875, 0.473388671875, 0.473388671875, 0.47216796875, 0.47216796875, 0.47216796875, 0.47216796875, 0.47216796875, 0.47216796875, 0.47021484375, 0.468994140625, 0.468505859375, 0.46240234375, 0.46240234375, 0.46240234375, 0.46240234375, 0.4619140625, 0.46142578125, 0.46142578125, 0.45849609375, 0.45849609375, 0.452880859375, 0.452880859375, 0.452392578125, 0.452392578125, 0.44482421875, 0.44482421875, 0.44189453125, 0.440185546875, 0.439208984375, 0.436279296875, 0.43212890625, 0.428955078125, 0.425537109375, 0.425537109375, 0.425537109375, 0.425537109375, 0.4228515625, 0.4228515625]}, "10": {"indices": [213, 218, 230, 413, 1297, 1616, 990, 642, 1237, 1619, 1254, 1290, 1296, 1325, 1336, 1352, 1452, 1476, 1498, 224, 228, 229, 1472, 1504, 1516, 1604, 1605, 1617, 1607, 599, 705, 745, 1143, 452, 681, 1284, 1291, 630, 636, 743, 521, 1648, 1650, 1653, 259, 397, 416, 419, 474, 532, 533, 536, 579, 592, 593, 598, 605, 625, 637, 640, 662, 665, 666, 737, 742, 1622, 1624, 1423, 1286, 291, 453, 1396, 1475, 1499, 746, 835, 1620, 1702, 457, 471, 1172, 1203, 1230, 1234, 1623, 1642, 1375, 475, 285, 1295, 1572, 1628, 74, 433, 548, 1574, 1597, 1615, 813, 823], "values": [0.998046875, 0.998046875, 0.998046875, 0.900390625, 0.900390625, 0.900390625, 0.892578125, 0.8828125, 0.8828125, 0.8828125, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.88037109375, 0.86962890625, 0.86962890625, 0.86962890625, 0.86767578125, 0.86767578125, 0.86767578125, 0.86572265625, 0.86572265625, 0.86572265625, 0.85302734375, 0.85107421875, 0.85107421875, 0.85107421875, 0.8505859375, 0.8486328125, 0.8486328125, 0.84375, 0.84375, 0.8408203125, 0.8408203125, 0.8408203125, 0.83740234375, 0.83740234375, 0.83740234375, 0.83740234375, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83642578125, 0.83447265625, 0.83447265625, 0.833984375, 0.8310546875, 0.8251953125, 0.82373046875, 0.8232421875, 0.8212890625, 0.8212890625, 0.82080078125, 0.82080078125, 0.81884765625, 0.81884765625, 0.818359375, 0.818359375, 0.81787109375, 0.81787109375, 0.81787109375, 0.81787109375, 0.81689453125, 0.81689453125, 0.81298828125, 0.8115234375, 0.81103515625, 0.80712890625, 0.806640625, 0.806640625, 0.80517578125, 0.80517578125, 0.802734375, 0.8017578125, 0.8017578125, 0.8017578125, 0.79931640625, 0.79931640625]}, "11": {"indices": [408, 1564, 429, 1602, 504, 508, 509, 528, 538, 712, 1011, 1636, 1351, 1437, 1438, 1440, 398, 403, 409, 1705, 1398, 1414, 496, 963, 1289, 1140, 1484, 904, 896, 897, 431, 1531, 1566, 435, 444, 445, 446, 450, 451, 458, 472, 501, 503, 1293, 1337, 1353, 1425, 1453, 1477, 1489, 1490, 1491, 1553, 465, 991, 1522, 1696, 1730, 1329, 780, 441, 470, 916, 522, 462, 1486, 1501, 1519, 1575, 1578, 1581, 1582, 1599, 1610, 1156, 513, 1685, 1731, 399, 1669, 1749, 334, 258, 476, 90, 91, 98, 52, 53, 75, 76, 77, 255, 818, 866, 647, 1354, 1478, 1511, 1512], "values": [0.998046875, 0.97265625, 0.95166015625, 0.95166015625, 0.951171875, 0.951171875, 0.951171875, 0.951171875, 0.951171875, 0.951171875, 0.951171875, 0.94482421875, 0.93017578125, 0.93017578125, 0.93017578125, 0.93017578125, 0.92236328125, 0.92236328125, 0.92236328125, 0.92236328125, 0.9140625, 0.9140625, 0.90234375, 0.896484375, 0.896484375, 0.8955078125, 0.8935546875, 0.89306640625, 0.8876953125, 0.8876953125, 0.86767578125, 0.86767578125, 0.86767578125, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.86669921875, 0.8662109375, 0.8662109375, 0.8662109375, 0.8662109375, 0.8662109375, 0.85791015625, 0.85400390625, 0.85107421875, 0.85107421875, 0.85107421875, 0.84375, 0.84228515625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.83447265625, 0.833984375, 0.82666015625, 0.82568359375, 0.8251953125, 0.82470703125, 0.82421875, 0.822265625, 0.81982421875, 0.81884765625, 0.81884765625, 0.818359375, 0.818359375, 0.818359375, 0.8173828125, 0.8173828125, 0.8173828125, 0.8173828125, 0.8173828125, 0.81689453125, 0.81591796875, 0.81591796875, 0.81494140625, 0.81494140625, 0.81494140625, 0.81494140625, 0.81494140625]}, "12": {"indices": [226, 227, 228, 223, 229, 220, 221, 222, 172, 187, 200, 72, 75, 302, 171, 212, 332, 363, 364, 78, 282, 235, 178, 225, 35, 185, 201, 378, 18, 274, 275, 276, 279, 360, 130, 45, 241, 188, 114, 115, 389, 390, 391, 392, 393, 394, 362, 128, 198, 116, 149, 211, 64, 70, 379, 380, 58, 352, 353, 194, 4, 41, 108, 179, 46, 280, 213, 66, 324, 146, 32, 33, 34, 249, 154, 59, 61, 73, 74, 155, 156, 119, 121, 122, 57, 158, 159, 176, 177, 84, 29, 23, 24, 25, 314, 199, 98, 230, 7, 95], "values": [0.845703125, 0.845703125, 0.845703125, 0.7177734375, 0.7177734375, 0.67529296875, 0.67529296875, 0.67529296875, 0.53564453125, 0.525390625, 0.52392578125, 0.486083984375, 0.486083984375, 0.47607421875, 0.473876953125, 0.471435546875, 0.446044921875, 0.446044921875, 0.446044921875, 0.43310546875, 0.426513671875, 0.419677734375, 0.41455078125, 0.413330078125, 0.409912109375, 0.404052734375, 0.398681640625, 0.397705078125, 0.3935546875, 0.389892578125, 0.389892578125, 0.389892578125, 0.389892578125, 0.3857421875, 0.38525390625, 0.384521484375, 0.381103515625, 0.378173828125, 0.377685546875, 0.377685546875, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370361328125, 0.36279296875, 0.36279296875, 0.3603515625, 0.357421875, 0.357421875, 0.356201171875, 0.355712890625, 0.351318359375, 0.351318359375, 0.349365234375, 0.348388671875, 0.348388671875, 0.345703125, 0.3447265625, 0.342529296875, 0.34130859375, 0.339599609375, 0.338134765625, 0.338134765625, 0.33251953125, 0.323974609375, 0.323486328125, 0.3212890625, 0.3193359375, 0.3193359375, 0.3193359375, 0.314697265625, 0.31201171875, 0.31103515625, 0.30908203125, 0.3076171875, 0.3076171875, 0.3076171875, 0.3076171875, 0.301513671875, 0.301513671875, 0.301513671875, 0.299072265625, 0.298095703125, 0.298095703125, 0.298095703125, 0.298095703125, 0.296630859375, 0.2958984375, 0.2939453125, 0.2939453125, 0.2939453125, 0.290283203125, 0.2880859375, 0.283447265625, 0.28173828125, 0.28125, 0.27978515625]}, "13": {"indices": [272, 271, 273, 269, 274, 270, 534, 510, 2475, 575, 570, 571, 495, 2833, 2829, 2830, 1000, 1028, 1030, 1031, 1032, 1033, 1035, 1040, 1041, 1045, 1046, 1047, 1048, 1050, 1051, 1052, 526, 527, 1861, 572, 573, 2256, 226, 227, 604, 2872, 2608, 2403, 2404, 1378, 523, 1064, 1067, 290, 295, 2454, 528, 530, 2455, 439, 1987, 1988, 1989, 2879, 2881, 2882, 2125, 2126, 599, 1375, 1377, 1023, 2840, 2522, 713, 714, 1027, 1029, 1034, 1036, 1037, 1039, 1042, 1043, 1044, 293, 267, 288, 1161, 1248, 907, 2836, 1772, 1773, 2370, 345, 346, 347, 360, 362, 367, 368, 2565, 2566], "values": [0.611328125, 0.5556640625, 0.54541015625, 0.5439453125, 0.52734375, 0.5166015625, 0.51416015625, 0.51123046875, 0.5078125, 0.50732421875, 0.50341796875, 0.50341796875, 0.489990234375, 0.48779296875, 0.469482421875, 0.469482421875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.46826171875, 0.463134765625, 0.463134765625, 0.45849609375, 0.458251953125, 0.458251953125, 0.4541015625, 0.453857421875, 0.453857421875, 0.45263671875, 0.442138671875, 0.438232421875, 0.4365234375, 0.4365234375, 0.433837890625, 0.4296875, 0.4296875, 0.4296875, 0.427978515625, 0.4228515625, 0.41845703125, 0.4169921875, 0.41455078125, 0.41455078125, 0.412841796875, 0.41259765625, 0.41259765625, 0.41259765625, 0.411865234375, 0.411865234375, 0.411865234375, 0.406494140625, 0.406494140625, 0.406005859375, 0.404052734375, 0.404052734375, 0.40283203125, 0.401611328125, 0.401123046875, 0.399658203125, 0.399658203125, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.393310546875, 0.391845703125, 0.39013671875, 0.389404296875, 0.389404296875, 0.38525390625, 0.38427734375, 0.38330078125, 0.382080078125, 0.382080078125, 0.38134765625, 0.3759765625, 0.3759765625, 0.3759765625, 0.3759765625, 0.3759765625, 0.3759765625, 0.3759765625, 0.36962890625, 0.36962890625]}, "14": {"indices": [288, 2325, 2401, 2394, 290, 481, 482, 269, 268, 270, 523, 1064, 1067, 2003, 2004, 2008, 2009, 2013, 2014, 2909, 492, 2883, 2469, 300, 2336, 295, 1060, 1061, 1062, 301, 289, 302, 554, 2363, 291, 292, 281, 282, 283, 284, 285, 2399, 2400, 2520, 503, 745, 2160, 2108, 2109, 294, 1595, 2565, 2566, 297, 293, 455, 299, 474, 475, 954, 2119, 266, 898, 2522, 747, 417, 277, 278, 633, 276, 314, 2395, 2396, 744, 746, 2005, 2006, 2007, 311, 287, 572, 573, 2414, 304, 305, 307, 308, 309, 310, 1065, 2413, 2684, 2685, 740, 741, 742, 743, 230, 770, 445], "values": [0.63134765625, 0.62109375, 0.61181640625, 0.59375, 0.57470703125, 0.57080078125, 0.57080078125, 0.55712890625, 0.548828125, 0.5458984375, 0.533203125, 0.533203125, 0.533203125, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52197265625, 0.5185546875, 0.51123046875, 0.494140625, 0.49169921875, 0.4794921875, 0.4775390625, 0.46533203125, 0.46533203125, 0.46533203125, 0.463623046875, 0.460693359375, 0.456787109375, 0.456787109375, 0.453125, 0.452392578125, 0.452392578125, 0.450439453125, 0.450439453125, 0.450439453125, 0.450439453125, 0.450439453125, 0.4501953125, 0.4501953125, 0.44873046875, 0.445068359375, 0.43408203125, 0.429931640625, 0.4296875, 0.4296875, 0.426513671875, 0.424560546875, 0.42333984375, 0.42333984375, 0.420654296875, 0.416015625, 0.413818359375, 0.412353515625, 0.409912109375, 0.409912109375, 0.40625, 0.40380859375, 0.400634765625, 0.3984375, 0.398193359375, 0.39794921875, 0.38427734375, 0.382568359375, 0.382568359375, 0.381103515625, 0.38037109375, 0.376953125, 0.374267578125, 0.374267578125, 0.372314453125, 0.372314453125, 0.369384765625, 0.369384765625, 0.369384765625, 0.3671875, 0.3662109375, 0.3662109375, 0.3662109375, 0.3662109375, 0.365478515625, 0.365478515625, 0.365478515625, 0.365478515625, 0.365478515625, 0.365478515625, 0.364990234375, 0.364501953125, 0.36083984375, 0.36083984375, 0.3603515625, 0.3603515625, 0.3603515625, 0.3603515625, 0.356689453125, 0.35595703125, 0.35546875]}, "15": {"indices": [567, 2767, 480, 2103, 2454, 1835, 1854, 472, 483, 770, 1375, 1377, 1205, 2154, 252, 257, 258, 1114, 242, 243, 246, 247, 248, 249, 250, 2403, 2404, 2196, 2212, 2213, 2214, 2062, 1121, 1596, 1597, 1178, 1179, 1180, 1038, 1049, 2517, 924, 925, 363, 1262, 1263, 1278, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1904, 2561, 188, 189, 923, 1658, 2366, 358, 419, 439, 417, 920, 3029, 1291, 1292, 1330, 786, 256, 2193, 1068, 1069, 2622, 2709, 739, 2770, 2775, 2793, 2813, 458, 521, 522, 1378, 428, 1810, 2863, 771, 784, 862, 863, 864, 865, 866, 868, 876, 877, 878], "values": [0.712890625, 0.6513671875, 0.64404296875, 0.5419921875, 0.5205078125, 0.468994140625, 0.468994140625, 0.46044921875, 0.46044921875, 0.45849609375, 0.456787109375, 0.456787109375, 0.454833984375, 0.45068359375, 0.44677734375, 0.44677734375, 0.44677734375, 0.4462890625, 0.44482421875, 0.44482421875, 0.44482421875, 0.44482421875, 0.44482421875, 0.44482421875, 0.44482421875, 0.440673828125, 0.440673828125, 0.43994140625, 0.43994140625, 0.43994140625, 0.43994140625, 0.437744140625, 0.43701171875, 0.435302734375, 0.435302734375, 0.431884765625, 0.431884765625, 0.431884765625, 0.424560546875, 0.424560546875, 0.424072265625, 0.420654296875, 0.420654296875, 0.419921875, 0.41943359375, 0.41943359375, 0.41943359375, 0.419189453125, 0.419189453125, 0.419189453125, 0.419189453125, 0.419189453125, 0.419189453125, 0.419189453125, 0.4169921875, 0.416259765625, 0.415283203125, 0.415283203125, 0.413818359375, 0.413818359375, 0.41357421875, 0.413330078125, 0.413330078125, 0.41064453125, 0.409912109375, 0.40869140625, 0.408203125, 0.40673828125, 0.40673828125, 0.40673828125, 0.404541015625, 0.404052734375, 0.403076171875, 0.40234375, 0.40234375, 0.4013671875, 0.400634765625, 0.400390625, 0.399658203125, 0.399658203125, 0.399658203125, 0.399658203125, 0.3955078125, 0.3955078125, 0.3955078125, 0.393310546875, 0.390869140625, 0.388916015625, 0.38818359375, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125, 0.387939453125]}, "16": {"indices": [380, 3432, 202, 3343, 3347, 4445, 3899, 1879, 1880, 3900, 2658, 2884, 2885, 2890, 2892, 3338, 3340, 3972, 3973, 852, 4662, 4887, 1895, 3970, 3337, 4669, 1883, 1885, 1888, 1889, 990, 111, 112, 113, 114, 364, 1882, 1890, 1891, 3155, 3909, 1881, 793, 991, 3464, 359, 3334, 1860, 2994, 1972, 3979, 891, 1827, 1832, 1016, 2680, 3778, 2016, 4609, 4580, 33, 43, 2020, 3158, 3136, 3975, 3977, 3978, 3137, 4531, 2480, 2484, 881, 4171, 4172, 2593, 785, 786, 1317, 1353, 952, 1903, 2536, 3235, 1539, 795, 796, 800, 2886, 1478, 640, 3966, 4867, 1062, 4551, 4595, 4660, 4663, 977, 4169], "values": [0.88134765625, 0.70947265625, 0.6728515625, 0.6630859375, 0.6630859375, 0.642578125, 0.6181640625, 0.6123046875, 0.6123046875, 0.59716796875, 0.59423828125, 0.59423828125, 0.59423828125, 0.59423828125, 0.59423828125, 0.58935546875, 0.58935546875, 0.5869140625, 0.5869140625, 0.58642578125, 0.58349609375, 0.5830078125, 0.58056640625, 0.576171875, 0.5751953125, 0.57177734375, 0.5673828125, 0.5673828125, 0.5673828125, 0.5673828125, 0.56201171875, 0.560546875, 0.560546875, 0.560546875, 0.560546875, 0.560546875, 0.55908203125, 0.55908203125, 0.55908203125, 0.55908203125, 0.55810546875, 0.55126953125, 0.55029296875, 0.55029296875, 0.54833984375, 0.5419921875, 0.54052734375, 0.53173828125, 0.5283203125, 0.52783203125, 0.5244140625, 0.52392578125, 0.52294921875, 0.52197265625, 0.5185546875, 0.51806640625, 0.513671875, 0.51318359375, 0.5126953125, 0.5078125, 0.50732421875, 0.50732421875, 0.50732421875, 0.5068359375, 0.50439453125, 0.50341796875, 0.50341796875, 0.50341796875, 0.50146484375, 0.50048828125, 0.49951171875, 0.49951171875, 0.4990234375, 0.497802734375, 0.497802734375, 0.4970703125, 0.495849609375, 0.495849609375, 0.494140625, 0.494140625, 0.493896484375, 0.493408203125, 0.490478515625, 0.489501953125, 0.4892578125, 0.4873046875, 0.4873046875, 0.4873046875, 0.486572265625, 0.485595703125, 0.484375, 0.484130859375, 0.484130859375, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.48095703125, 0.479736328125, 0.478759765625]}, "17": {"indices": [29, 24, 8, 10, 6, 7, 19, 20, 11, 12, 31, 18, 32, 25, 22, 27, 21, 23, 9, 28, 26, 34, 35, 1, 2, 4, 3, 30, 5, 0, 33, 13, 14, 15, 16, 17], "values": [0.55517578125, 0.501953125, 0.4970703125, 0.4970703125, 0.448486328125, 0.448486328125, 0.448486328125, 0.448486328125, 0.447998046875, 0.447998046875, 0.44140625, 0.4404296875, 0.43701171875, 0.435791015625, 0.412353515625, 0.38232421875, 0.363525390625, 0.3447265625, 0.337646484375, 0.32861328125, 0.30078125, 0.295654296875, 0.295654296875, 0.2890625, 0.2890625, 0.265869140625, 0.249755859375, 0.1990966796875, 0.1925048828125, 0.1175537109375, 0.0308990478515625, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 24, 6, 7, 19, 20, 26, 8, 10, 28, 21, 31, 9, 27, 30, 23, 29, 25, 11, 12, 4, 5, 1, 2, 34, 35, 32, 3, 0, 33, 13, 14, 15, 16, 17], "values": [0.70849609375, 0.640625, 0.638671875, 0.5947265625, 0.5947265625, 0.5947265625, 0.5947265625, 0.59375, 0.572265625, 0.572265625, 0.54150390625, 0.5400390625, 0.52880859375, 0.52197265625, 0.515625, 0.5146484375, 0.50537109375, 0.492431640625, 0.431884765625, 0.4296875, 0.4296875, 0.396484375, 0.3935546875, 0.351806640625, 0.351806640625, 0.3505859375, 0.3505859375, 0.33447265625, 0.286376953125, 0.2158203125, 0.1331787109375, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [18, 60, 61, 79, 163, 161, 86, 87, 57, 111, 51, 52, 59, 105, 210, 43, 206, 201, 119, 195, 55, 7, 62, 211, 215, 63, 64, 58, 70, 3, 69, 180, 46, 142, 143, 144, 175, 6, 226, 74, 2, 4, 5, 172, 113, 198, 54, 80, 208, 209, 65, 196, 243, 194, 126, 14, 16, 82, 81, 124, 141, 205, 197, 207, 23, 190, 120, 112, 114, 203, 128, 199, 244, 189, 10, 110, 15, 183, 89, 181, 212, 191, 179, 22, 233, 247, 202, 133, 237, 71, 20, 200, 235, 245, 213, 238, 103, 145, 185, 27], "values": [0.82421875, 0.8037109375, 0.8037109375, 0.578125, 0.55126953125, 0.5244140625, 0.5146484375, 0.5146484375, 0.471923828125, 0.4697265625, 0.46630859375, 0.46630859375, 0.438232421875, 0.410400390625, 0.398681640625, 0.386474609375, 0.384765625, 0.379150390625, 0.37744140625, 0.36474609375, 0.34814453125, 0.3359375, 0.324462890625, 0.30908203125, 0.301513671875, 0.29833984375, 0.29833984375, 0.28466796875, 0.279052734375, 0.27734375, 0.27294921875, 0.2410888671875, 0.240234375, 0.2398681640625, 0.2398681640625, 0.2398681640625, 0.235107421875, 0.2318115234375, 0.2261962890625, 0.2259521484375, 0.2215576171875, 0.2215576171875, 0.2215576171875, 0.216064453125, 0.21533203125, 0.2066650390625, 0.198974609375, 0.198974609375, 0.197509765625, 0.197509765625, 0.195068359375, 0.1890869140625, 0.180419921875, 0.179443359375, 0.177978515625, 0.1717529296875, 0.1717529296875, 0.1695556640625, 0.168701171875, 0.166259765625, 0.166259765625, 0.165771484375, 0.161376953125, 0.156005859375, 0.1552734375, 0.153076171875, 0.151611328125, 0.14599609375, 0.14599609375, 0.1453857421875, 0.14453125, 0.1434326171875, 0.1400146484375, 0.139404296875, 0.130859375, 0.1300048828125, 0.128662109375, 0.1234130859375, 0.12139892578125, 0.1212158203125, 0.1171875, 0.116943359375, 0.11224365234375, 0.11041259765625, 0.104736328125, 0.10015869140625, 0.09967041015625, 0.09613037109375, 0.09149169921875, 0.0911865234375, 0.08953857421875, 0.08831787109375, 0.08782958984375, 0.08648681640625, 0.08599853515625, 0.083740234375, 0.0833740234375, 0.0833740234375, 0.081298828125, 0.07830810546875]}}

v2 =  {"0": {"indices": [395, 686, 396, 434, 199, 21, 89, 180, 181, 783, 785, 379, 397, 758, 778, 779, 182, 627, 787, 788, 789, 203, 204, 791, 432, 811, 207, 209, 535, 626, 668, 408, 409, 634, 71, 110, 111, 410, 618, 620, 621, 622, 796, 793, 534, 86, 87, 153, 433, 105, 172, 465, 469, 486, 487, 488, 545, 200, 166, 167, 174, 205, 429, 40, 866, 795, 214, 215, 768, 769, 16, 631, 633, 767, 420, 436, 438, 444, 64, 161, 399, 404, 405, 406, 407, 558, 289, 489, 529, 530, 561, 88, 674, 613, 612, 152, 256, 364, 441, 163], "values": [0.6376953125, 0.59521484375, 0.5703125, 0.56787109375, 0.56298828125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5400390625, 0.5400390625, 0.5380859375, 0.5380859375, 0.521484375, 0.521484375, 0.521484375, 0.51806640625, 0.50244140625, 0.498779296875, 0.498779296875, 0.498779296875, 0.4951171875, 0.4951171875, 0.479736328125, 0.46337890625, 0.452392578125, 0.449462890625, 0.449462890625, 0.435546875, 0.4287109375, 0.4287109375, 0.42431640625, 0.42431640625, 0.421630859375, 0.4150390625, 0.4150390625, 0.4150390625, 0.397705078125, 0.3916015625, 0.3916015625, 0.3916015625, 0.3916015625, 0.390869140625, 0.38818359375, 0.380126953125, 0.376708984375, 0.376708984375, 0.376220703125, 0.36474609375, 0.3623046875, 0.3623046875, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.349365234375, 0.347900390625, 0.343017578125, 0.343017578125, 0.33935546875, 0.336181640625, 0.33203125, 0.329345703125, 0.319580078125, 0.3193359375, 0.318115234375, 0.318115234375, 0.313720703125, 0.313720703125, 0.310546875, 0.306884765625, 0.306884765625, 0.305419921875, 0.3046875, 0.3046875, 0.3046875, 0.3046875, 0.299560546875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.291748046875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28759765625, 0.28564453125, 0.283203125, 0.28076171875, 0.2802734375, 0.280029296875, 0.278564453125, 0.27783203125, 0.277587890625]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 59, 88, 89, 90, 91, 92, 96, 97, 66, 67, 21, 85, 139, 10, 45, 9, 47, 60, 48, 61, 94, 95, 62, 63, 40, 0, 137, 77, 78, 140, 53, 98, 138, 157, 158, 159, 160, 76, 74, 58, 80, 81, 15, 142, 135, 32, 131, 153, 154, 93, 57, 107, 112, 114, 106, 108, 109, 110, 111, 115, 27, 141, 127, 132, 133, 35, 36, 54, 28, 17, 24, 25, 124, 134, 82, 155, 125, 2, 46, 26, 51, 52, 22, 23, 34, 79, 73, 113, 143, 31, 33, 156, 147, 149], "values": [0.61376953125, 0.59765625, 0.59765625, 0.53369140625, 0.486572265625, 0.486572265625, 0.4541015625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.440673828125, 0.440673828125, 0.41015625, 0.388671875, 0.382080078125, 0.3759765625, 0.3505859375, 0.349609375, 0.333984375, 0.331787109375, 0.33154296875, 0.3310546875, 0.31689453125, 0.31689453125, 0.298583984375, 0.298583984375, 0.291748046875, 0.291259765625, 0.291015625, 0.28271484375, 0.28271484375, 0.282470703125, 0.27685546875, 0.258544921875, 0.253173828125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2452392578125, 0.240478515625, 0.2384033203125, 0.23828125, 0.23828125, 0.2330322265625, 0.2298583984375, 0.2275390625, 0.2108154296875, 0.2108154296875, 0.204833984375, 0.204833984375, 0.1888427734375, 0.1864013671875, 0.18505859375, 0.18505859375, 0.18505859375, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.17626953125, 0.175048828125, 0.1632080078125, 0.1632080078125, 0.1632080078125, 0.159912109375, 0.159912109375, 0.159912109375, 0.154052734375, 0.1519775390625, 0.1451416015625, 0.1451416015625, 0.1407470703125, 0.1402587890625, 0.138671875, 0.137451171875, 0.1328125, 0.13134765625, 0.1298828125, 0.1251220703125, 0.1240234375, 0.1240234375, 0.1138916015625, 0.1138916015625, 0.10711669921875, 0.10614013671875, 0.10205078125, 0.0994873046875, 0.0968017578125, 0.0933837890625, 0.0933837890625, 0.0924072265625, 0.08441162109375, 0.07696533203125]}, "2": {"indices": [5, 32, 43, 4, 44, 3, 18, 55, 37, 38, 39, 31, 33, 8, 56, 6, 7, 41, 42, 30, 35, 36, 54, 34, 15, 9, 156, 22, 23, 88, 89, 90, 91, 92, 96, 97, 74, 16, 10, 94, 95, 26, 48, 49, 12, 13, 14, 86, 87, 24, 25, 29, 151, 85, 75, 76, 84, 157, 158, 159, 160, 98, 141, 53, 155, 17, 40, 131, 46, 93, 20, 51, 52, 21, 0, 153, 154, 2, 146, 69, 70, 50, 71, 72, 19, 47, 66, 67, 143, 127, 132, 133, 148, 149, 150, 45, 124, 82, 138, 147], "values": [0.74658203125, 0.6884765625, 0.65771484375, 0.65673828125, 0.65673828125, 0.6328125, 0.6328125, 0.62158203125, 0.578125, 0.578125, 0.578125, 0.572265625, 0.572265625, 0.55224609375, 0.5244140625, 0.5185546875, 0.46240234375, 0.46240234375, 0.46240234375, 0.455078125, 0.435302734375, 0.435302734375, 0.435302734375, 0.4169921875, 0.354248046875, 0.317138671875, 0.316162109375, 0.30908203125, 0.30908203125, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.2763671875, 0.272216796875, 0.263427734375, 0.259521484375, 0.259521484375, 0.2548828125, 0.25439453125, 0.25244140625, 0.25146484375, 0.25146484375, 0.25146484375, 0.2484130859375, 0.2484130859375, 0.2373046875, 0.2373046875, 0.2305908203125, 0.2286376953125, 0.226806640625, 0.2225341796875, 0.211669921875, 0.2115478515625, 0.2095947265625, 0.2095947265625, 0.2095947265625, 0.2095947265625, 0.2003173828125, 0.193603515625, 0.1923828125, 0.1832275390625, 0.18310546875, 0.18017578125, 0.1668701171875, 0.166015625, 0.1602783203125, 0.1561279296875, 0.1558837890625, 0.1558837890625, 0.1522216796875, 0.139404296875, 0.1334228515625, 0.1334228515625, 0.11846923828125, 0.1175537109375, 0.10595703125, 0.10595703125, 0.09716796875, 0.09161376953125, 0.09161376953125, 0.0911865234375, 0.08770751953125, 0.07879638671875, 0.07879638671875, 0.0694580078125, 0.061279296875, 0.061279296875, 0.061279296875, 0.05987548828125, 0.05987548828125, 0.05987548828125, 0.058349609375, 0.052490234375, 0.041259765625, 0.035186767578125, 0.034576416015625]}, "3": {"indices": [66, 447, 420, 449, 450, 200, 204, 268, 442, 298, 325, 326, 64, 149, 444, 448, 89, 4083, 46, 50, 65, 67, 775, 1014, 311, 84, 4073, 68, 76, 85, 86, 312, 313, 3871, 526, 709, 1142, 1015, 2134, 2160, 8, 870, 3236, 451, 456, 4146, 4200, 391, 2702, 4380, 1499, 3874, 2887, 3100, 3268, 3269, 3270, 3271, 524, 397, 4228, 851, 852, 853, 2406, 2407, 4777, 4778, 4779, 148, 4067, 4068, 4383, 55, 56, 58, 2685, 1506, 1507, 209, 107, 1546, 1190, 88, 100, 1554, 1555, 90, 525, 682, 782, 804, 708, 4193, 4859, 4087, 4088, 2948, 57, 701], "values": [0.99755859375, 0.99755859375, 0.947265625, 0.947265625, 0.947265625, 0.8203125, 0.8203125, 0.8203125, 0.8203125, 0.81884765625, 0.81884765625, 0.81884765625, 0.80419921875, 0.80419921875, 0.80419921875, 0.80419921875, 0.77392578125, 0.7421875, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.71533203125, 0.70947265625, 0.701171875, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.689453125, 0.66748046875, 0.66748046875, 0.66748046875, 0.642578125, 0.63671875, 0.63671875, 0.619140625, 0.6123046875, 0.6025390625, 0.59521484375, 0.59521484375, 0.59130859375, 0.59130859375, 0.58837890625, 0.587890625, 0.587890625, 0.58740234375, 0.58447265625, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.58203125, 0.58154296875, 0.57763671875, 0.5771484375, 0.5771484375, 0.5771484375, 0.5693359375, 0.5693359375, 0.56884765625, 0.56884765625, 0.56884765625, 0.56787109375, 0.56787109375, 0.56787109375, 0.56787109375, 0.560546875, 0.560546875, 0.560546875, 0.56005859375, 0.55908203125, 0.55908203125, 0.55810546875, 0.556640625, 0.55029296875, 0.54931640625, 0.54833984375, 0.54833984375, 0.54443359375, 0.54443359375, 0.5419921875, 0.5419921875, 0.54052734375, 0.54052734375, 0.54052734375, 0.53564453125, 0.53564453125, 0.53515625, 0.5341796875, 0.5341796875, 0.52587890625, 0.525390625, 0.52490234375]}, "4": {"indices": [225, 234, 212, 120, 121, 124, 126, 127, 56, 57, 91, 181, 232, 92, 163, 179, 178, 142, 209, 17, 158, 239, 240, 135, 233, 129, 249, 250, 206, 122, 109, 107, 116, 117, 118, 119, 61, 177, 44, 53, 11, 110, 111, 132, 146, 147, 60, 5, 152, 153, 213, 7, 34, 70, 72, 157, 36, 238, 45, 237, 176, 180, 52, 159, 160, 1, 2, 236, 100, 220, 230, 94, 104, 108, 113, 128, 130, 141, 148, 187, 54, 4, 16, 46, 67, 74, 78, 81, 82, 85, 86, 203, 14, 221, 247, 164, 93, 99, 101, 115], "values": [0.689453125, 0.689453125, 0.6572265625, 0.6435546875, 0.6435546875, 0.6435546875, 0.6435546875, 0.6435546875, 0.6015625, 0.6015625, 0.6015625, 0.6015625, 0.583984375, 0.52734375, 0.52734375, 0.52734375, 0.513671875, 0.50927734375, 0.50146484375, 0.47412109375, 0.47314453125, 0.46435546875, 0.46435546875, 0.458251953125, 0.44775390625, 0.441650390625, 0.439697265625, 0.439697265625, 0.43408203125, 0.424560546875, 0.418701171875, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.41552734375, 0.414306640625, 0.411376953125, 0.401611328125, 0.400146484375, 0.396728515625, 0.396728515625, 0.396728515625, 0.396728515625, 0.396728515625, 0.39111328125, 0.38916015625, 0.38916015625, 0.38916015625, 0.38818359375, 0.3876953125, 0.3876953125, 0.385986328125, 0.385986328125, 0.383544921875, 0.38330078125, 0.37939453125, 0.376708984375, 0.375732421875, 0.375244140625, 0.37255859375, 0.359619140625, 0.356201171875, 0.356201171875, 0.354736328125, 0.354736328125, 0.350341796875, 0.338134765625, 0.337646484375, 0.320556640625, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31103515625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.306640625, 0.305419921875, 0.304443359375, 0.302734375, 0.30224609375, 0.30126953125, 0.29736328125, 0.29736328125, 0.29736328125]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4271, 4335, 4021, 4022, 4023, 4024, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 2336, 2359, 4306, 1074, 1111, 1153, 1171, 441, 4222, 4624, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 2368, 2766, 2797, 2834, 2843, 4977, 4852, 4341, 4342, 4343, 4504, 2868, 2928, 2936, 4757, 4775, 1370, 3417, 3687, 3695, 3699, 3700, 4177, 4500, 4577, 4340, 4616, 3223, 3751, 3838, 3839, 3858, 3861, 933, 4250, 4076, 4079, 1113, 1088, 3585, 4790, 4080, 1075, 1078, 1093, 3697, 3288], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.9658203125, 0.8173828125, 0.8173828125, 0.78076171875, 0.669921875, 0.6689453125, 0.66845703125, 0.66845703125, 0.66845703125, 0.66845703125, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.65087890625, 0.6357421875, 0.62744140625, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.6162109375, 0.615234375, 0.615234375, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.607421875, 0.60107421875, 0.6005859375, 0.6005859375, 0.6005859375, 0.599609375, 0.58544921875, 0.58544921875, 0.58544921875, 0.57958984375, 0.57958984375, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5771484375, 0.57373046875, 0.57275390625, 0.55859375, 0.5556640625, 0.5556640625, 0.5498046875, 0.5498046875, 0.5498046875, 0.5498046875, 0.54931640625, 0.546875, 0.537109375, 0.537109375, 0.53466796875, 0.5341796875, 0.53369140625, 0.5322265625, 0.52734375, 0.52685546875, 0.52685546875, 0.52685546875, 0.52587890625, 0.52099609375]}, "6": {"indices": [371, 327, 230, 231, 233, 234, 235, 914, 372, 1138, 232, 3147, 3323, 1101, 202, 1588, 1589, 2584, 38, 49, 50, 56, 236, 414, 415, 416, 3561, 3562, 3566, 3567, 1083, 1127, 1147, 62, 63, 174, 1173, 330, 203, 913, 921, 922, 9, 376, 3945, 1797, 2432, 3584, 2878, 3156, 3157, 3158, 3160, 3168, 3169, 3220, 3221, 3318, 3263, 2854, 618, 1014, 1854, 3308, 3309, 4400, 915, 162, 2801, 1792, 1793, 1798, 1813, 1814, 2847, 34, 614, 865, 868, 3970, 3971, 2576, 2579, 2580, 2581, 3579, 1796, 4181, 4182, 4184, 4185, 1167, 4578, 3564, 4629, 568, 190, 187, 191, 204], "values": [0.70849609375, 0.6748046875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.63134765625, 0.5908203125, 0.52685546875, 0.51806640625, 0.496826171875, 0.4814453125, 0.474365234375, 0.455322265625, 0.453125, 0.453125, 0.436767578125, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.4296875, 0.429443359375, 0.429443359375, 0.429443359375, 0.422119140625, 0.422119140625, 0.422119140625, 0.422119140625, 0.420166015625, 0.420166015625, 0.420166015625, 0.4140625, 0.4140625, 0.41357421875, 0.41259765625, 0.410400390625, 0.405517578125, 0.40185546875, 0.39990234375, 0.39990234375, 0.3955078125, 0.39501953125, 0.39501953125, 0.394775390625, 0.391845703125, 0.3916015625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.38330078125, 0.383056640625, 0.379638671875, 0.379638671875, 0.379638671875, 0.377197265625, 0.377197265625, 0.377197265625, 0.375, 0.3740234375, 0.3740234375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.371826171875, 0.37109375, 0.37109375, 0.37109375, 0.37109375, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.368408203125, 0.368408203125, 0.3681640625, 0.367431640625, 0.36669921875, 0.36669921875, 0.36669921875, 0.36669921875, 0.365478515625, 0.3623046875, 0.361083984375, 0.35888671875, 0.35595703125, 0.3544921875, 0.354248046875, 0.354248046875, 0.351318359375]}, "7": {"indices": [54, 27, 31, 34, 143, 32, 33, 66, 53, 56, 61, 62, 63, 35, 19, 55, 49, 227, 124, 26, 50, 29, 51, 28, 47, 48, 57, 60, 119, 10, 118, 192, 88, 132, 133, 204, 83, 45, 46, 52, 157, 158, 159, 160, 97, 9, 59, 1, 74, 75, 76, 77, 175, 111, 213, 30, 109, 219, 220, 221, 178, 179, 180, 181, 182, 183, 170, 172, 173, 72, 18, 128, 230, 44, 130, 138, 114, 58, 217, 194, 121, 113, 73, 186, 200, 123, 125, 214, 25, 64, 120, 117, 13, 232, 116, 225, 0, 185, 193, 15], "values": [0.8212890625, 0.67578125, 0.6259765625, 0.6259765625, 0.5693359375, 0.560546875, 0.5458984375, 0.54150390625, 0.52685546875, 0.5234375, 0.5234375, 0.5234375, 0.485107421875, 0.477783203125, 0.4697265625, 0.4638671875, 0.439208984375, 0.434814453125, 0.430419921875, 0.42138671875, 0.404541015625, 0.399169921875, 0.39794921875, 0.396240234375, 0.3876953125, 0.3876953125, 0.366455078125, 0.358154296875, 0.350830078125, 0.348876953125, 0.346435546875, 0.34423828125, 0.343017578125, 0.340576171875, 0.340576171875, 0.337890625, 0.33740234375, 0.328369140625, 0.325927734375, 0.32568359375, 0.3251953125, 0.3251953125, 0.3251953125, 0.3251953125, 0.320556640625, 0.316650390625, 0.31005859375, 0.3017578125, 0.2998046875, 0.2998046875, 0.2998046875, 0.2998046875, 0.294677734375, 0.29150390625, 0.2900390625, 0.281982421875, 0.278564453125, 0.2744140625, 0.2744140625, 0.2744140625, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.26806640625, 0.26806640625, 0.26806640625, 0.266845703125, 0.260498046875, 0.250732421875, 0.2490234375, 0.247802734375, 0.237060546875, 0.237060546875, 0.2362060546875, 0.2332763671875, 0.231689453125, 0.22900390625, 0.225830078125, 0.2249755859375, 0.219482421875, 0.21044921875, 0.2093505859375, 0.2080078125, 0.2080078125, 0.20703125, 0.205810546875, 0.2052001953125, 0.193603515625, 0.1920166015625, 0.1888427734375, 0.1866455078125, 0.1846923828125, 0.1842041015625, 0.181884765625, 0.17822265625, 0.1766357421875, 0.17626953125]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 1377, 640, 1600, 1601, 1602, 656, 441, 442, 443, 161, 162, 650, 554, 555, 159, 1973, 274, 277, 454, 1972, 1810, 501, 451, 157, 183, 156, 1491, 1492, 1959, 174, 175, 2222, 473, 474, 497, 498, 499, 1195, 1874, 177, 178, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 923, 680, 418, 506, 507, 508, 565, 566, 567, 603, 1078, 1079, 1203, 1204, 372, 1328, 1408, 2004, 649, 1687, 1081, 1138, 1232, 1240, 494, 495, 496, 1324, 1325, 1326, 1327, 1406, 1755, 1841, 1844, 1936], "values": [0.908203125, 0.908203125, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.8642578125, 0.82373046875, 0.7802734375, 0.76123046875, 0.75341796875, 0.6728515625, 0.6728515625, 0.6728515625, 0.662109375, 0.64697265625, 0.64697265625, 0.64697265625, 0.62646484375, 0.62646484375, 0.623046875, 0.609375, 0.609375, 0.58447265625, 0.5703125, 0.55810546875, 0.55810546875, 0.55810546875, 0.54736328125, 0.53173828125, 0.525390625, 0.52392578125, 0.5205078125, 0.5205078125, 0.51806640625, 0.51806640625, 0.51806640625, 0.51708984375, 0.51416015625, 0.51416015625, 0.513671875, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.5009765625, 0.49169921875, 0.49072265625, 0.49072265625, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.48583984375, 0.462646484375, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.45458984375, 0.4521484375, 0.451416015625, 0.451416015625, 0.44970703125, 0.448974609375, 0.4404296875, 0.4404296875, 0.4404296875, 0.4404296875, 0.438720703125, 0.438720703125, 0.438720703125, 0.434326171875, 0.434326171875, 0.434326171875, 0.434326171875, 0.43359375, 0.43359375, 0.43359375, 0.43359375, 0.43359375]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2840, 2839, 2919, 283, 2473, 2474, 752, 3631, 2475, 2941, 2944, 1027, 212, 213, 220, 221, 687, 286, 174, 278, 751, 422, 4165, 2957, 873, 4434, 4435, 2448, 755, 756, 4507, 1790, 2481, 2916, 827, 835, 4204, 828, 834, 1075, 1076, 1077, 1339, 2018, 4488, 772, 4383, 4349, 4350, 4376, 2476, 2803, 2961, 4533, 866, 867, 4348, 4351, 2431, 796, 1337, 1944, 2772, 1457, 1031, 2195, 2308, 4676, 749, 904, 630, 36, 2976, 2977, 684, 686, 1979, 3810, 3811, 258, 262, 2557, 2777, 3389, 3182, 3345, 624, 753, 754, 905, 3036, 3037, 277, 2929, 2225], "values": [0.99755859375, 0.8896484375, 0.84912109375, 0.79736328125, 0.79736328125, 0.67578125, 0.66357421875, 0.658203125, 0.619140625, 0.619140625, 0.619140625, 0.603515625, 0.60009765625, 0.59326171875, 0.59326171875, 0.59326171875, 0.587890625, 0.57861328125, 0.57861328125, 0.57861328125, 0.57861328125, 0.57861328125, 0.57421875, 0.5498046875, 0.5498046875, 0.5478515625, 0.53466796875, 0.5341796875, 0.5234375, 0.5224609375, 0.50244140625, 0.50244140625, 0.5009765625, 0.5, 0.5, 0.5, 0.49169921875, 0.49169921875, 0.49169921875, 0.489990234375, 0.489990234375, 0.48974609375, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.4814453125, 0.4765625, 0.46923828125, 0.468994140625, 0.454833984375, 0.454833984375, 0.454833984375, 0.45361328125, 0.45361328125, 0.45361328125, 0.44775390625, 0.428466796875, 0.428466796875, 0.42626953125, 0.42626953125, 0.423828125, 0.41552734375, 0.4140625, 0.413818359375, 0.412353515625, 0.40966796875, 0.40869140625, 0.40869140625, 0.40869140625, 0.40869140625, 0.405029296875, 0.405029296875, 0.39892578125, 0.393798828125, 0.3935546875, 0.3935546875, 0.388916015625, 0.388916015625, 0.388427734375, 0.386962890625, 0.386962890625, 0.38623046875, 0.3837890625, 0.3818359375, 0.38037109375, 0.380126953125, 0.378662109375, 0.378662109375, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37060546875, 0.37060546875, 0.369140625, 0.369140625, 0.367431640625]}, "10": {"indices": [262, 267, 279, 462, 1346, 1665, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 1039, 1521, 1553, 1565, 273, 277, 278, 691, 1286, 1653, 1654, 1666, 1668, 1656, 648, 754, 794, 1333, 1340, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 340, 501, 730, 1697, 1699, 1702, 502, 1524, 1548, 795, 884, 1192, 570, 1671, 1673, 679, 685, 792, 1472, 1691, 1344, 506, 520, 1424, 1669, 1751, 334, 524, 1335, 1679, 1938, 1975, 209, 218, 1672, 1445, 1677, 123, 1621, 482, 1279, 1283, 1623, 1646, 1664], "values": [0.998046875, 0.998046875, 0.998046875, 0.9013671875, 0.9013671875, 0.9013671875, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.88330078125, 0.876953125, 0.876953125, 0.876953125, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.86376953125, 0.85986328125, 0.85986328125, 0.85986328125, 0.8583984375, 0.8583984375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.84814453125, 0.84716796875, 0.84716796875, 0.8427734375, 0.8427734375, 0.8427734375, 0.84033203125, 0.83984375, 0.83984375, 0.83935546875, 0.83935546875, 0.83935546875, 0.8359375, 0.83544921875, 0.83544921875, 0.828125, 0.828125, 0.828125, 0.8271484375, 0.8271484375, 0.82568359375, 0.8251953125, 0.8251953125, 0.82373046875, 0.82177734375, 0.82177734375, 0.8203125, 0.8193359375, 0.81884765625, 0.818359375, 0.818359375, 0.818359375, 0.8173828125, 0.8173828125, 0.81689453125, 0.81640625, 0.81591796875, 0.8154296875, 0.81494140625, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875]}, "11": {"indices": [457, 1613, 478, 1651, 1685, 553, 557, 558, 577, 587, 761, 1060, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 945, 946, 953, 1189, 1533, 1012, 1338, 545, 484, 493, 494, 495, 499, 500, 507, 514, 521, 550, 552, 1040, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1571, 1602, 1745, 1779, 1378, 829, 1205, 511, 480, 1580, 1615, 490, 519, 965, 571, 1734, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 562, 162, 383, 35, 43, 141, 101, 102, 124, 125, 126, 1718, 139, 140, 147, 1271, 1272, 526, 1579, 1720, 1725, 1730, 1731, 1752], "values": [0.998046875, 0.96044921875, 0.9404296875, 0.9404296875, 0.935546875, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.92822265625, 0.92822265625, 0.92822265625, 0.921875, 0.921875, 0.921875, 0.921875, 0.90673828125, 0.90576171875, 0.90576171875, 0.90185546875, 0.90185546875, 0.8974609375, 0.89208984375, 0.88818359375, 0.88720703125, 0.87890625, 0.869140625, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.84228515625, 0.841796875, 0.8359375, 0.8349609375, 0.83056640625, 0.83056640625, 0.83056640625, 0.82666015625, 0.82666015625, 0.82666015625, 0.82373046875, 0.82275390625, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81298828125, 0.8125, 0.8125, 0.80908203125, 0.80908203125, 0.8056640625, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.80322265625, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 214, 304, 189, 173, 284, 334, 365, 366, 80, 37, 47, 74, 77, 20, 132, 227, 237, 150, 380, 243, 196, 187, 110, 130, 200, 68, 276, 277, 278, 281, 180, 362, 72, 181, 364, 190, 151, 213, 203, 116, 117, 118, 282, 86, 381, 382, 24, 46, 179, 48, 359, 354, 355, 144, 66, 251, 177, 43, 148, 34, 35, 36, 61, 60, 218, 391, 392, 393, 394, 395, 396, 215, 254, 6, 63, 157, 158, 75, 76, 232, 160, 161, 178, 326, 336, 59, 129, 121, 123, 124], "values": [0.83984375, 0.83984375, 0.83984375, 0.66162109375, 0.66162109375, 0.66162109375, 0.64697265625, 0.64697265625, 0.513671875, 0.492431640625, 0.489990234375, 0.464111328125, 0.458984375, 0.454345703125, 0.441162109375, 0.42822265625, 0.42822265625, 0.42822265625, 0.419921875, 0.402587890625, 0.40234375, 0.402099609375, 0.402099609375, 0.39990234375, 0.396728515625, 0.386474609375, 0.368408203125, 0.354248046875, 0.350341796875, 0.3466796875, 0.3447265625, 0.343994140625, 0.337158203125, 0.3359375, 0.3359375, 0.333984375, 0.330810546875, 0.330810546875, 0.330810546875, 0.330810546875, 0.330078125, 0.323486328125, 0.318359375, 0.31201171875, 0.308349609375, 0.307373046875, 0.302734375, 0.302734375, 0.302001953125, 0.301025390625, 0.301025390625, 0.295654296875, 0.291748046875, 0.28857421875, 0.286865234375, 0.286865234375, 0.284912109375, 0.284912109375, 0.28369140625, 0.28173828125, 0.276123046875, 0.27587890625, 0.27587890625, 0.274169921875, 0.272705078125, 0.26904296875, 0.262451171875, 0.261474609375, 0.26123046875, 0.258544921875, 0.258544921875, 0.258544921875, 0.25830078125, 0.257080078125, 0.2568359375, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.25634765625, 0.254638671875, 0.252197265625, 0.251220703125, 0.24951171875, 0.24951171875, 0.2469482421875, 0.2469482421875, 0.2464599609375, 0.241455078125, 0.241455078125, 0.241455078125, 0.24072265625, 0.2398681640625, 0.2392578125, 0.23828125, 0.2357177734375, 0.2357177734375, 0.2357177734375]}, "13": {"indices": [275, 274, 276, 537, 513, 273, 272, 277, 575, 576, 573, 574, 498, 578, 529, 530, 2836, 2832, 2833, 1864, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 2478, 229, 230, 607, 293, 298, 442, 533, 1381, 526, 1067, 1070, 1990, 1991, 1992, 2128, 2129, 2882, 2884, 2885, 2525, 2458, 531, 270, 2457, 296, 279, 2875, 3040, 716, 717, 2839, 1026, 348, 349, 350, 363, 365, 370, 371, 254, 1251, 2843, 291, 2373, 2259, 2838, 1164, 515, 458, 2568, 2569, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 2406, 2407, 1906], "values": [0.625, 0.56689453125, 0.55078125, 0.548828125, 0.5439453125, 0.537109375, 0.5234375, 0.51220703125, 0.49609375, 0.49609375, 0.4921875, 0.4921875, 0.49169921875, 0.487548828125, 0.48291015625, 0.48291015625, 0.476318359375, 0.46630859375, 0.46630859375, 0.4658203125, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.44921875, 0.4453125, 0.4453125, 0.44091796875, 0.43603515625, 0.431396484375, 0.42578125, 0.421875, 0.416015625, 0.4111328125, 0.4111328125, 0.4111328125, 0.40966796875, 0.40966796875, 0.40966796875, 0.40625, 0.40625, 0.40576171875, 0.40576171875, 0.40576171875, 0.40380859375, 0.401611328125, 0.400390625, 0.3994140625, 0.3994140625, 0.394287109375, 0.392333984375, 0.39111328125, 0.3876953125, 0.387451171875, 0.387451171875, 0.38720703125, 0.385498046875, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.38427734375, 0.38427734375, 0.3837890625, 0.381591796875, 0.381103515625, 0.378662109375, 0.378173828125, 0.3759765625, 0.375, 0.3720703125, 0.371337890625, 0.371337890625, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.36865234375, 0.36865234375, 0.36767578125]}, "14": {"indices": [291, 2397, 2328, 293, 484, 485, 2404, 271, 273, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 495, 2568, 2569, 272, 2912, 557, 292, 2339, 2886, 1063, 1064, 1065, 303, 298, 2472, 748, 506, 750, 304, 901, 1598, 2525, 305, 2523, 284, 285, 286, 287, 288, 458, 575, 576, 2008, 2009, 2010, 2402, 2403, 300, 229, 230, 957, 789, 296, 302, 489, 539, 2366, 2163, 275, 1117, 297, 477, 478, 269, 420, 504, 1612, 2122, 703, 314, 2111, 2112, 474, 294, 295, 280, 281, 1088, 2687, 2688, 1069, 448, 2416, 743, 744, 745, 746, 680, 747, 749, 636, 770], "values": [0.625, 0.587890625, 0.5859375, 0.58056640625, 0.580078125, 0.580078125, 0.5751953125, 0.5478515625, 0.541015625, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.51318359375, 0.51318359375, 0.51318359375, 0.50732421875, 0.5068359375, 0.5068359375, 0.50390625, 0.50341796875, 0.49609375, 0.4853515625, 0.478515625, 0.4775390625, 0.4658203125, 0.4658203125, 0.4658203125, 0.46533203125, 0.465087890625, 0.456787109375, 0.453369140625, 0.451171875, 0.450439453125, 0.447509765625, 0.4453125, 0.4453125, 0.442138671875, 0.437255859375, 0.437255859375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.429931640625, 0.420166015625, 0.420166015625, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.419189453125, 0.41845703125, 0.41845703125, 0.417724609375, 0.417236328125, 0.4140625, 0.4140625, 0.41162109375, 0.404296875, 0.40234375, 0.39990234375, 0.3994140625, 0.396484375, 0.394287109375, 0.393310546875, 0.393310546875, 0.391845703125, 0.391357421875, 0.39111328125, 0.388916015625, 0.388427734375, 0.380859375, 0.3779296875, 0.377197265625, 0.377197265625, 0.3720703125, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.3681640625, 0.364990234375, 0.364990234375, 0.3642578125, 0.36328125, 0.359619140625, 0.359375, 0.359375, 0.359375, 0.359375, 0.353515625, 0.35302734375, 0.35302734375, 0.3505859375, 0.34765625]}, "15": {"indices": [570, 2770, 483, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 255, 260, 261, 1599, 1600, 1378, 1380, 2406, 2407, 259, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 2712, 789, 475, 486, 1117, 1294, 1295, 1333, 1041, 1052, 1124, 773, 923, 742, 366, 927, 928, 361, 422, 191, 192, 926, 2866, 1181, 1182, 1183, 1838, 1857, 2564, 2773, 2778, 2796, 2816, 431, 2065, 2520, 1029, 442, 2157, 2199, 2215, 2216, 2217, 1813, 1208, 1403, 2369, 2777, 461, 524, 525, 566, 572, 1381, 1661, 3032, 1071, 1072, 1462, 1463, 1464, 1465, 1466, 1907, 3029, 1602, 2625, 2609, 420, 1350, 1353], "values": [0.70166015625, 0.640625, 0.60107421875, 0.5263671875, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.478271484375, 0.458984375, 0.458984375, 0.458984375, 0.447998046875, 0.447998046875, 0.4453125, 0.4453125, 0.44140625, 0.44140625, 0.42431640625, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.416015625, 0.41259765625, 0.4091796875, 0.4091796875, 0.4091796875, 0.407470703125, 0.407470703125, 0.407470703125, 0.40625, 0.40625, 0.405029296875, 0.404541015625, 0.400390625, 0.399658203125, 0.3984375, 0.3974609375, 0.3974609375, 0.39599609375, 0.39599609375, 0.3955078125, 0.3955078125, 0.390380859375, 0.388916015625, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.386962890625, 0.386474609375, 0.386474609375, 0.386474609375, 0.386474609375, 0.3857421875, 0.383544921875, 0.38330078125, 0.38232421875, 0.382080078125, 0.382080078125, 0.380859375, 0.380859375, 0.380859375, 0.380859375, 0.378173828125, 0.375, 0.374267578125, 0.373046875, 0.372802734375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.36572265625, 0.365478515625, 0.363525390625, 0.359130859375, 0.359130859375, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.358642578125, 0.358154296875, 0.356201171875, 0.354736328125, 0.353271484375, 0.3525390625, 0.3525390625]}, "16": {"indices": [380, 3432, 202, 3343, 3347, 1879, 1880, 4445, 3155, 852, 3900, 3338, 3340, 2884, 2885, 2890, 2892, 1883, 1885, 1888, 1889, 1860, 3337, 3972, 3973, 4669, 3975, 3977, 3978, 3334, 3979, 3970, 1923, 1925, 3899, 4609, 4169, 990, 1062, 3909, 4662, 3464, 111, 112, 113, 114, 364, 4171, 4172, 793, 991, 891, 2658, 4867, 4887, 3966, 795, 796, 800, 1003, 2389, 4854, 3158, 3976, 2480, 2484, 4551, 4595, 4660, 4663, 4531, 1827, 994, 2994, 359, 1972, 1882, 1890, 1891, 4865, 1478, 2886, 33, 43, 2020, 3235, 881, 4425, 1016, 67, 1903, 2605, 1881, 986, 987, 116, 196, 2353, 2354, 2355], "values": [0.8515625, 0.68017578125, 0.64306640625, 0.623046875, 0.623046875, 0.6064453125, 0.6064453125, 0.60400390625, 0.59423828125, 0.5927734375, 0.5732421875, 0.56396484375, 0.56396484375, 0.5458984375, 0.5458984375, 0.5458984375, 0.5458984375, 0.54052734375, 0.54052734375, 0.54052734375, 0.54052734375, 0.53662109375, 0.53662109375, 0.5361328125, 0.5361328125, 0.529296875, 0.52734375, 0.52734375, 0.52734375, 0.5263671875, 0.5224609375, 0.521484375, 0.51953125, 0.51953125, 0.517578125, 0.5166015625, 0.51513671875, 0.5126953125, 0.51171875, 0.51171875, 0.51025390625, 0.5087890625, 0.5068359375, 0.5068359375, 0.5068359375, 0.5068359375, 0.5068359375, 0.505859375, 0.505859375, 0.50439453125, 0.50439453125, 0.50341796875, 0.5029296875, 0.50146484375, 0.499755859375, 0.4970703125, 0.495361328125, 0.495361328125, 0.495361328125, 0.495361328125, 0.494140625, 0.49267578125, 0.490234375, 0.489990234375, 0.48974609375, 0.48974609375, 0.48876953125, 0.48876953125, 0.48876953125, 0.48876953125, 0.48779296875, 0.487060546875, 0.486572265625, 0.485595703125, 0.485107421875, 0.482177734375, 0.4775390625, 0.4775390625, 0.4775390625, 0.4775390625, 0.47412109375, 0.47412109375, 0.47216796875, 0.47216796875, 0.47216796875, 0.4716796875, 0.469970703125, 0.46875, 0.462890625, 0.461181640625, 0.4609375, 0.460693359375, 0.45947265625, 0.458984375, 0.458984375, 0.45458984375, 0.45458984375, 0.45458984375, 0.45458984375, 0.45458984375]}, "17": {"indices": [29, 24, 8, 10, 6, 7, 19, 20, 25, 18, 11, 12, 31, 32, 22, 21, 27, 23, 28, 26, 3, 9, 34, 35, 4, 5, 1, 2, 30, 0, 13, 14, 15, 16, 17, 33], "values": [0.51904296875, 0.43994140625, 0.42724609375, 0.42724609375, 0.41064453125, 0.41064453125, 0.41064453125, 0.41064453125, 0.409912109375, 0.40771484375, 0.402099609375, 0.402099609375, 0.3779296875, 0.370849609375, 0.34814453125, 0.337890625, 0.330322265625, 0.3291015625, 0.271484375, 0.2646484375, 0.25048828125, 0.2373046875, 0.23583984375, 0.23583984375, 0.235107421875, 0.1968994140625, 0.182373046875, 0.182373046875, 0.154541015625, 0.11785888671875, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 26, 6, 7, 19, 20, 24, 8, 10, 30, 31, 21, 23, 9, 28, 27, 25, 29, 1, 2, 11, 12, 4, 34, 35, 5, 3, 0, 32, 33, 13, 14, 15, 16, 17], "values": [0.64501953125, 0.6005859375, 0.58251953125, 0.572265625, 0.572265625, 0.572265625, 0.572265625, 0.56884765625, 0.5595703125, 0.5595703125, 0.52294921875, 0.50537109375, 0.4970703125, 0.4951171875, 0.492919921875, 0.490234375, 0.478515625, 0.4306640625, 0.41845703125, 0.4169921875, 0.4169921875, 0.409912109375, 0.409912109375, 0.399169921875, 0.37255859375, 0.37255859375, 0.346435546875, 0.343994140625, 0.269287109375, 0.255615234375, 0.110107421875, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 53, 54, 165, 163, 88, 89, 113, 59, 61, 107, 212, 208, 197, 9, 121, 203, 5, 45, 57, 213, 64, 217, 8, 177, 65, 66, 72, 144, 145, 146, 182, 60, 210, 211, 174, 71, 200, 76, 48, 16, 18, 192, 228, 56, 82, 122, 25, 115, 196, 83, 4, 6, 7, 205, 128, 0, 112, 204, 207, 17, 191, 1, 245, 215, 67, 126, 143, 201, 181, 209, 84, 214, 199, 198, 130, 239, 24, 183, 114, 116, 91, 246, 185, 105, 235, 236, 124, 135, 193, 12, 179, 26, 188, 41, 249, 186], "values": [0.7890625, 0.77197265625, 0.77197265625, 0.55078125, 0.52685546875, 0.52685546875, 0.5166015625, 0.489501953125, 0.471435546875, 0.471435546875, 0.447509765625, 0.435302734375, 0.39013671875, 0.38671875, 0.37451171875, 0.360107421875, 0.35009765625, 0.34228515625, 0.34130859375, 0.334716796875, 0.33154296875, 0.3291015625, 0.3125, 0.299072265625, 0.296875, 0.28076171875, 0.27880859375, 0.272216796875, 0.252197265625, 0.252197265625, 0.251953125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.24609375, 0.2449951171875, 0.23828125, 0.23828125, 0.2353515625, 0.2174072265625, 0.208984375, 0.207763671875, 0.1929931640625, 0.1923828125, 0.1923828125, 0.19189453125, 0.1864013671875, 0.17529296875, 0.17529296875, 0.1749267578125, 0.174072265625, 0.1712646484375, 0.1712646484375, 0.170654296875, 0.1678466796875, 0.1678466796875, 0.1678466796875, 0.16552734375, 0.1639404296875, 0.1622314453125, 0.1607666015625, 0.15380859375, 0.1522216796875, 0.1466064453125, 0.145263671875, 0.14453125, 0.1427001953125, 0.14208984375, 0.1416015625, 0.1409912109375, 0.1409912109375, 0.1383056640625, 0.1363525390625, 0.1343994140625, 0.131103515625, 0.130126953125, 0.127197265625, 0.124267578125, 0.12249755859375, 0.117919921875, 0.11700439453125, 0.1097412109375, 0.108154296875, 0.108154296875, 0.10748291015625, 0.1055908203125, 0.10333251953125, 0.09893798828125, 0.09844970703125, 0.09552001953125, 0.09332275390625, 0.0882568359375, 0.08074951171875, 0.07720947265625, 0.07659912109375, 0.07452392578125, 0.07305908203125, 0.069091796875, 0.06878662109375, 0.06591796875]}}

for i in range(20):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])

v1 =  {"0": {"indices": [395, 686, 396, 434, 199, 21, 89, 180, 181, 783, 785, 379, 397, 758, 778, 779, 182, 627, 787, 788, 789, 203, 204, 791, 432, 811, 207, 209, 535, 626, 668, 408, 409, 634, 71, 110, 111, 410, 618, 620, 621, 622, 796, 793, 534, 86, 87, 153, 433, 105, 172, 465, 469, 486, 487, 488, 545, 200, 166, 167, 174, 205, 429, 40, 866, 795, 214, 215, 768, 769, 16, 631, 633, 767, 420, 436, 438, 444, 64, 161, 399, 404, 405, 406, 407, 558, 289, 489, 529, 530, 561, 88, 674, 613, 612, 152, 256, 364, 441, 163], "values": [0.6376953125, 0.59521484375, 0.5703125, 0.56787109375, 0.56298828125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5400390625, 0.5400390625, 0.5380859375, 0.5380859375, 0.521484375, 0.521484375, 0.521484375, 0.51806640625, 0.50244140625, 0.498779296875, 0.498779296875, 0.498779296875, 0.4951171875, 0.4951171875, 0.479736328125, 0.46337890625, 0.452392578125, 0.449462890625, 0.449462890625, 0.435546875, 0.4287109375, 0.4287109375, 0.42431640625, 0.42431640625, 0.421630859375, 0.4150390625, 0.4150390625, 0.4150390625, 0.397705078125, 0.3916015625, 0.3916015625, 0.3916015625, 0.3916015625, 0.390869140625, 0.38818359375, 0.380126953125, 0.376708984375, 0.376708984375, 0.376220703125, 0.36474609375, 0.3623046875, 0.3623046875, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.349365234375, 0.347900390625, 0.343017578125, 0.343017578125, 0.33935546875, 0.336181640625, 0.33203125, 0.329345703125, 0.319580078125, 0.3193359375, 0.318115234375, 0.318115234375, 0.313720703125, 0.313720703125, 0.310546875, 0.306884765625, 0.306884765625, 0.305419921875, 0.3046875, 0.3046875, 0.3046875, 0.3046875, 0.299560546875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.291748046875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28759765625, 0.28564453125, 0.283203125, 0.28076171875, 0.2802734375, 0.280029296875, 0.278564453125, 0.27783203125, 0.277587890625]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 59, 88, 89, 90, 91, 92, 96, 97, 66, 67, 21, 85, 139, 10, 45, 9, 47, 60, 48, 61, 94, 95, 62, 63, 40, 0, 137, 77, 78, 140, 53, 98, 138, 157, 158, 159, 160, 76, 74, 58, 80, 81, 15, 142, 135, 32, 131, 153, 154, 93, 57, 107, 112, 114, 106, 108, 109, 110, 111, 115, 27, 141, 127, 132, 133, 35, 36, 54, 28, 17, 24, 25, 124, 134, 82, 155, 125, 2, 46, 26, 51, 52, 22, 23, 34, 79, 73, 113, 143, 31, 33, 156, 147, 149], "values": [0.61376953125, 0.59765625, 0.59765625, 0.53369140625, 0.486572265625, 0.486572265625, 0.4541015625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.440673828125, 0.440673828125, 0.41015625, 0.388671875, 0.382080078125, 0.3759765625, 0.3505859375, 0.349609375, 0.333984375, 0.331787109375, 0.33154296875, 0.3310546875, 0.31689453125, 0.31689453125, 0.298583984375, 0.298583984375, 0.291748046875, 0.291259765625, 0.291015625, 0.28271484375, 0.28271484375, 0.282470703125, 0.27685546875, 0.258544921875, 0.253173828125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2452392578125, 0.240478515625, 0.2384033203125, 0.23828125, 0.23828125, 0.2330322265625, 0.2298583984375, 0.2275390625, 0.2108154296875, 0.2108154296875, 0.204833984375, 0.204833984375, 0.1888427734375, 0.1864013671875, 0.18505859375, 0.18505859375, 0.18505859375, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.17626953125, 0.175048828125, 0.1632080078125, 0.1632080078125, 0.1632080078125, 0.159912109375, 0.159912109375, 0.159912109375, 0.154052734375, 0.1519775390625, 0.1451416015625, 0.1451416015625, 0.1407470703125, 0.1402587890625, 0.138671875, 0.137451171875, 0.1328125, 0.13134765625, 0.1298828125, 0.1251220703125, 0.1240234375, 0.1240234375, 0.1138916015625, 0.1138916015625, 0.10711669921875, 0.10614013671875, 0.10205078125, 0.0994873046875, 0.0968017578125, 0.0933837890625, 0.0933837890625, 0.0924072265625, 0.08441162109375, 0.07696533203125]}, "2": {"indices": [5, 32, 43, 4, 44, 3, 18, 55, 37, 38, 39, 31, 33, 8, 56, 6, 7, 41, 42, 30, 35, 36, 54, 34, 15, 9, 156, 22, 23, 88, 89, 90, 91, 92, 96, 97, 74, 16, 10, 94, 95, 26, 48, 49, 12, 13, 14, 86, 87, 24, 25, 29, 151, 85, 75, 76, 84, 157, 158, 159, 160, 98, 141, 53, 155, 17, 40, 131, 46, 93, 20, 51, 52, 21, 0, 153, 154, 2, 146, 69, 70, 50, 71, 72, 19, 47, 66, 67, 143, 127, 132, 133, 148, 149, 150, 45, 124, 82, 138, 147], "values": [0.74658203125, 0.6884765625, 0.65771484375, 0.65673828125, 0.65673828125, 0.6328125, 0.6328125, 0.62158203125, 0.578125, 0.578125, 0.578125, 0.572265625, 0.572265625, 0.55224609375, 0.5244140625, 0.5185546875, 0.46240234375, 0.46240234375, 0.46240234375, 0.455078125, 0.435302734375, 0.435302734375, 0.435302734375, 0.4169921875, 0.354248046875, 0.317138671875, 0.316162109375, 0.30908203125, 0.30908203125, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.283447265625, 0.2763671875, 0.272216796875, 0.263427734375, 0.259521484375, 0.259521484375, 0.2548828125, 0.25439453125, 0.25244140625, 0.25146484375, 0.25146484375, 0.25146484375, 0.2484130859375, 0.2484130859375, 0.2373046875, 0.2373046875, 0.2305908203125, 0.2286376953125, 0.226806640625, 0.2225341796875, 0.211669921875, 0.2115478515625, 0.2095947265625, 0.2095947265625, 0.2095947265625, 0.2095947265625, 0.2003173828125, 0.193603515625, 0.1923828125, 0.1832275390625, 0.18310546875, 0.18017578125, 0.1668701171875, 0.166015625, 0.1602783203125, 0.1561279296875, 0.1558837890625, 0.1558837890625, 0.1522216796875, 0.139404296875, 0.1334228515625, 0.1334228515625, 0.11846923828125, 0.1175537109375, 0.10595703125, 0.10595703125, 0.09716796875, 0.09161376953125, 0.09161376953125, 0.0911865234375, 0.08770751953125, 0.07879638671875, 0.07879638671875, 0.0694580078125, 0.061279296875, 0.061279296875, 0.061279296875, 0.05987548828125, 0.05987548828125, 0.05987548828125, 0.058349609375, 0.052490234375, 0.041259765625, 0.035186767578125, 0.034576416015625]}, "3": {"indices": [66, 447, 420, 449, 450, 200, 204, 268, 442, 298, 325, 326, 64, 149, 444, 448, 89, 4083, 46, 50, 65, 67, 775, 1014, 311, 84, 4073, 68, 76, 85, 86, 312, 313, 3871, 526, 709, 1142, 1015, 2134, 2160, 8, 870, 3236, 451, 456, 4146, 4200, 391, 2702, 4380, 1499, 3874, 2887, 3100, 3268, 3269, 3270, 3271, 524, 397, 4228, 851, 852, 853, 2406, 2407, 4777, 4778, 4779, 148, 4067, 4068, 4383, 55, 56, 58, 2685, 1506, 1507, 209, 107, 1546, 1190, 88, 100, 1554, 1555, 90, 525, 682, 782, 804, 708, 4193, 4859, 4087, 4088, 2948, 57, 701], "values": [0.99755859375, 0.99755859375, 0.947265625, 0.947265625, 0.947265625, 0.8203125, 0.8203125, 0.8203125, 0.8203125, 0.81884765625, 0.81884765625, 0.81884765625, 0.80419921875, 0.80419921875, 0.80419921875, 0.80419921875, 0.77392578125, 0.7421875, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.71533203125, 0.70947265625, 0.701171875, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.689453125, 0.66748046875, 0.66748046875, 0.66748046875, 0.642578125, 0.63671875, 0.63671875, 0.619140625, 0.6123046875, 0.6025390625, 0.59521484375, 0.59521484375, 0.59130859375, 0.59130859375, 0.58837890625, 0.587890625, 0.587890625, 0.58740234375, 0.58447265625, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.58203125, 0.58154296875, 0.57763671875, 0.5771484375, 0.5771484375, 0.5771484375, 0.5693359375, 0.5693359375, 0.56884765625, 0.56884765625, 0.56884765625, 0.56787109375, 0.56787109375, 0.56787109375, 0.56787109375, 0.560546875, 0.560546875, 0.560546875, 0.56005859375, 0.55908203125, 0.55908203125, 0.55810546875, 0.556640625, 0.55029296875, 0.54931640625, 0.54833984375, 0.54833984375, 0.54443359375, 0.54443359375, 0.5419921875, 0.5419921875, 0.54052734375, 0.54052734375, 0.54052734375, 0.53564453125, 0.53564453125, 0.53515625, 0.5341796875, 0.5341796875, 0.52587890625, 0.525390625, 0.52490234375]}, "4": {"indices": [225, 234, 212, 120, 121, 124, 126, 127, 56, 57, 91, 181, 232, 92, 163, 179, 178, 142, 209, 17, 158, 239, 240, 135, 233, 129, 249, 250, 206, 122, 109, 107, 116, 117, 118, 119, 61, 177, 44, 53, 11, 110, 111, 132, 146, 147, 60, 5, 152, 153, 213, 7, 34, 70, 72, 157, 36, 238, 45, 237, 176, 180, 52, 159, 160, 1, 2, 236, 100, 220, 230, 94, 104, 108, 113, 128, 130, 141, 148, 187, 54, 4, 16, 46, 67, 74, 78, 81, 82, 85, 86, 203, 14, 221, 247, 164, 93, 99, 101, 115], "values": [0.689453125, 0.689453125, 0.6572265625, 0.6435546875, 0.6435546875, 0.6435546875, 0.6435546875, 0.6435546875, 0.6015625, 0.6015625, 0.6015625, 0.6015625, 0.583984375, 0.52734375, 0.52734375, 0.52734375, 0.513671875, 0.50927734375, 0.50146484375, 0.47412109375, 0.47314453125, 0.46435546875, 0.46435546875, 0.458251953125, 0.44775390625, 0.441650390625, 0.439697265625, 0.439697265625, 0.43408203125, 0.424560546875, 0.418701171875, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.41552734375, 0.414306640625, 0.411376953125, 0.401611328125, 0.400146484375, 0.396728515625, 0.396728515625, 0.396728515625, 0.396728515625, 0.396728515625, 0.39111328125, 0.38916015625, 0.38916015625, 0.38916015625, 0.38818359375, 0.3876953125, 0.3876953125, 0.385986328125, 0.385986328125, 0.383544921875, 0.38330078125, 0.37939453125, 0.376708984375, 0.375732421875, 0.375244140625, 0.37255859375, 0.359619140625, 0.356201171875, 0.356201171875, 0.354736328125, 0.354736328125, 0.350341796875, 0.338134765625, 0.337646484375, 0.320556640625, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31982421875, 0.31103515625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.308837890625, 0.306640625, 0.305419921875, 0.304443359375, 0.302734375, 0.30224609375, 0.30126953125, 0.29736328125, 0.29736328125, 0.29736328125]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4271, 4335, 4021, 4022, 4023, 4024, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 2336, 2359, 4306, 1074, 1111, 1153, 1171, 441, 4222, 4624, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 2368, 2766, 2797, 2834, 2843, 4977, 4852, 4341, 4342, 4343, 4504, 2868, 2928, 2936, 4757, 4775, 1370, 3417, 3687, 3695, 3699, 3700, 4177, 4500, 4577, 4340, 4616, 3223, 3751, 3838, 3839, 3858, 3861, 933, 4250, 4076, 4079, 1113, 1088, 3585, 4790, 4080, 1075, 1078, 1093, 3697, 3288], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.9658203125, 0.8173828125, 0.8173828125, 0.78076171875, 0.669921875, 0.6689453125, 0.66845703125, 0.66845703125, 0.66845703125, 0.66845703125, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.65087890625, 0.6357421875, 0.62744140625, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.6162109375, 0.615234375, 0.615234375, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.607421875, 0.60107421875, 0.6005859375, 0.6005859375, 0.6005859375, 0.599609375, 0.58544921875, 0.58544921875, 0.58544921875, 0.57958984375, 0.57958984375, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5771484375, 0.57373046875, 0.57275390625, 0.55859375, 0.5556640625, 0.5556640625, 0.5498046875, 0.5498046875, 0.5498046875, 0.5498046875, 0.54931640625, 0.546875, 0.537109375, 0.537109375, 0.53466796875, 0.5341796875, 0.53369140625, 0.5322265625, 0.52734375, 0.52685546875, 0.52685546875, 0.52685546875, 0.52587890625, 0.52099609375]}, "6": {"indices": [371, 327, 230, 231, 233, 234, 235, 914, 372, 1138, 232, 3147, 3323, 1101, 202, 1588, 1589, 2584, 38, 49, 50, 56, 236, 414, 415, 416, 3561, 3562, 3566, 3567, 1083, 1127, 1147, 62, 63, 174, 1173, 330, 203, 913, 921, 922, 9, 376, 3945, 1797, 2432, 3584, 2878, 3156, 3157, 3158, 3160, 3168, 3169, 3220, 3221, 3318, 3263, 2854, 618, 1014, 1854, 3308, 3309, 4400, 915, 162, 2801, 1792, 1793, 1798, 1813, 1814, 2847, 34, 614, 865, 868, 3970, 3971, 2576, 2579, 2580, 2581, 3579, 1796, 4181, 4182, 4184, 4185, 1167, 4578, 3564, 4629, 568, 190, 187, 191, 204], "values": [0.70849609375, 0.6748046875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.63134765625, 0.5908203125, 0.52685546875, 0.51806640625, 0.496826171875, 0.4814453125, 0.474365234375, 0.455322265625, 0.453125, 0.453125, 0.436767578125, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.4296875, 0.429443359375, 0.429443359375, 0.429443359375, 0.422119140625, 0.422119140625, 0.422119140625, 0.422119140625, 0.420166015625, 0.420166015625, 0.420166015625, 0.4140625, 0.4140625, 0.41357421875, 0.41259765625, 0.410400390625, 0.405517578125, 0.40185546875, 0.39990234375, 0.39990234375, 0.3955078125, 0.39501953125, 0.39501953125, 0.394775390625, 0.391845703125, 0.3916015625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.38330078125, 0.383056640625, 0.379638671875, 0.379638671875, 0.379638671875, 0.377197265625, 0.377197265625, 0.377197265625, 0.375, 0.3740234375, 0.3740234375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.371826171875, 0.37109375, 0.37109375, 0.37109375, 0.37109375, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.368408203125, 0.368408203125, 0.3681640625, 0.367431640625, 0.36669921875, 0.36669921875, 0.36669921875, 0.36669921875, 0.365478515625, 0.3623046875, 0.361083984375, 0.35888671875, 0.35595703125, 0.3544921875, 0.354248046875, 0.354248046875, 0.351318359375]}, "7": {"indices": [54, 27, 31, 34, 143, 32, 33, 66, 53, 56, 61, 62, 63, 35, 19, 55, 49, 227, 124, 26, 50, 29, 51, 28, 47, 48, 57, 60, 119, 10, 118, 192, 88, 132, 133, 204, 83, 45, 46, 52, 157, 158, 159, 160, 97, 9, 59, 1, 74, 75, 76, 77, 175, 111, 213, 30, 109, 219, 220, 221, 178, 179, 180, 181, 182, 183, 170, 172, 173, 72, 18, 128, 230, 44, 130, 138, 114, 58, 217, 194, 121, 113, 73, 186, 200, 123, 125, 214, 25, 64, 120, 117, 13, 232, 116, 225, 0, 185, 193, 15], "values": [0.8212890625, 0.67578125, 0.6259765625, 0.6259765625, 0.5693359375, 0.560546875, 0.5458984375, 0.54150390625, 0.52685546875, 0.5234375, 0.5234375, 0.5234375, 0.485107421875, 0.477783203125, 0.4697265625, 0.4638671875, 0.439208984375, 0.434814453125, 0.430419921875, 0.42138671875, 0.404541015625, 0.399169921875, 0.39794921875, 0.396240234375, 0.3876953125, 0.3876953125, 0.366455078125, 0.358154296875, 0.350830078125, 0.348876953125, 0.346435546875, 0.34423828125, 0.343017578125, 0.340576171875, 0.340576171875, 0.337890625, 0.33740234375, 0.328369140625, 0.325927734375, 0.32568359375, 0.3251953125, 0.3251953125, 0.3251953125, 0.3251953125, 0.320556640625, 0.316650390625, 0.31005859375, 0.3017578125, 0.2998046875, 0.2998046875, 0.2998046875, 0.2998046875, 0.294677734375, 0.29150390625, 0.2900390625, 0.281982421875, 0.278564453125, 0.2744140625, 0.2744140625, 0.2744140625, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.26806640625, 0.26806640625, 0.26806640625, 0.266845703125, 0.260498046875, 0.250732421875, 0.2490234375, 0.247802734375, 0.237060546875, 0.237060546875, 0.2362060546875, 0.2332763671875, 0.231689453125, 0.22900390625, 0.225830078125, 0.2249755859375, 0.219482421875, 0.21044921875, 0.2093505859375, 0.2080078125, 0.2080078125, 0.20703125, 0.205810546875, 0.2052001953125, 0.193603515625, 0.1920166015625, 0.1888427734375, 0.1866455078125, 0.1846923828125, 0.1842041015625, 0.181884765625, 0.17822265625, 0.1766357421875, 0.17626953125]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 1377, 640, 1600, 1601, 1602, 656, 441, 442, 443, 161, 162, 650, 554, 555, 159, 1973, 274, 277, 454, 1972, 1810, 501, 451, 157, 183, 156, 1491, 1492, 1959, 174, 175, 2222, 473, 474, 497, 498, 499, 1195, 1874, 177, 178, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 923, 680, 418, 506, 507, 508, 565, 566, 567, 603, 1078, 1079, 1203, 1204, 372, 1328, 1408, 2004, 649, 1687, 1081, 1138, 1232, 1240, 494, 495, 496, 1324, 1325, 1326, 1327, 1406, 1755, 1841, 1844, 1936], "values": [0.908203125, 0.908203125, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.8642578125, 0.82373046875, 0.7802734375, 0.76123046875, 0.75341796875, 0.6728515625, 0.6728515625, 0.6728515625, 0.662109375, 0.64697265625, 0.64697265625, 0.64697265625, 0.62646484375, 0.62646484375, 0.623046875, 0.609375, 0.609375, 0.58447265625, 0.5703125, 0.55810546875, 0.55810546875, 0.55810546875, 0.54736328125, 0.53173828125, 0.525390625, 0.52392578125, 0.5205078125, 0.5205078125, 0.51806640625, 0.51806640625, 0.51806640625, 0.51708984375, 0.51416015625, 0.51416015625, 0.513671875, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.5009765625, 0.49169921875, 0.49072265625, 0.49072265625, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.48583984375, 0.462646484375, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.45458984375, 0.4521484375, 0.451416015625, 0.451416015625, 0.44970703125, 0.448974609375, 0.4404296875, 0.4404296875, 0.4404296875, 0.4404296875, 0.438720703125, 0.438720703125, 0.438720703125, 0.434326171875, 0.434326171875, 0.434326171875, 0.434326171875, 0.43359375, 0.43359375, 0.43359375, 0.43359375, 0.43359375]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2840, 2839, 2919, 283, 2473, 2474, 752, 3631, 2475, 2941, 2944, 1027, 212, 213, 220, 221, 687, 286, 174, 278, 751, 422, 4165, 2957, 873, 4434, 4435, 2448, 755, 756, 4507, 1790, 2481, 2916, 827, 835, 4204, 828, 834, 1075, 1076, 1077, 1339, 2018, 4488, 772, 4383, 4349, 4350, 4376, 2476, 2803, 2961, 4533, 866, 867, 4348, 4351, 2431, 796, 1337, 1944, 2772, 1457, 1031, 2195, 2308, 4676, 749, 904, 630, 36, 2976, 2977, 684, 686, 1979, 3810, 3811, 258, 262, 2557, 2777, 3389, 3182, 3345, 624, 753, 754, 905, 3036, 3037, 277, 2929, 2225], "values": [0.99755859375, 0.8896484375, 0.84912109375, 0.79736328125, 0.79736328125, 0.67578125, 0.66357421875, 0.658203125, 0.619140625, 0.619140625, 0.619140625, 0.603515625, 0.60009765625, 0.59326171875, 0.59326171875, 0.59326171875, 0.587890625, 0.57861328125, 0.57861328125, 0.57861328125, 0.57861328125, 0.57861328125, 0.57421875, 0.5498046875, 0.5498046875, 0.5478515625, 0.53466796875, 0.5341796875, 0.5234375, 0.5224609375, 0.50244140625, 0.50244140625, 0.5009765625, 0.5, 0.5, 0.5, 0.49169921875, 0.49169921875, 0.49169921875, 0.489990234375, 0.489990234375, 0.48974609375, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.488525390625, 0.4814453125, 0.4765625, 0.46923828125, 0.468994140625, 0.454833984375, 0.454833984375, 0.454833984375, 0.45361328125, 0.45361328125, 0.45361328125, 0.44775390625, 0.428466796875, 0.428466796875, 0.42626953125, 0.42626953125, 0.423828125, 0.41552734375, 0.4140625, 0.413818359375, 0.412353515625, 0.40966796875, 0.40869140625, 0.40869140625, 0.40869140625, 0.40869140625, 0.405029296875, 0.405029296875, 0.39892578125, 0.393798828125, 0.3935546875, 0.3935546875, 0.388916015625, 0.388916015625, 0.388427734375, 0.386962890625, 0.386962890625, 0.38623046875, 0.3837890625, 0.3818359375, 0.38037109375, 0.380126953125, 0.378662109375, 0.378662109375, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37060546875, 0.37060546875, 0.369140625, 0.369140625, 0.367431640625]}, "10": {"indices": [262, 267, 279, 462, 1346, 1665, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 1039, 1521, 1553, 1565, 273, 277, 278, 691, 1286, 1653, 1654, 1666, 1668, 1656, 648, 754, 794, 1333, 1340, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 340, 501, 730, 1697, 1699, 1702, 502, 1524, 1548, 795, 884, 1192, 570, 1671, 1673, 679, 685, 792, 1472, 1691, 1344, 506, 520, 1424, 1669, 1751, 334, 524, 1335, 1679, 1938, 1975, 209, 218, 1672, 1445, 1677, 123, 1621, 482, 1279, 1283, 1623, 1646, 1664], "values": [0.998046875, 0.998046875, 0.998046875, 0.9013671875, 0.9013671875, 0.9013671875, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.8974609375, 0.88330078125, 0.876953125, 0.876953125, 0.876953125, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.86376953125, 0.85986328125, 0.85986328125, 0.85986328125, 0.8583984375, 0.8583984375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.84814453125, 0.84716796875, 0.84716796875, 0.8427734375, 0.8427734375, 0.8427734375, 0.84033203125, 0.83984375, 0.83984375, 0.83935546875, 0.83935546875, 0.83935546875, 0.8359375, 0.83544921875, 0.83544921875, 0.828125, 0.828125, 0.828125, 0.8271484375, 0.8271484375, 0.82568359375, 0.8251953125, 0.8251953125, 0.82373046875, 0.82177734375, 0.82177734375, 0.8203125, 0.8193359375, 0.81884765625, 0.818359375, 0.818359375, 0.818359375, 0.8173828125, 0.8173828125, 0.81689453125, 0.81640625, 0.81591796875, 0.8154296875, 0.81494140625, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875, 0.81201171875]}, "11": {"indices": [457, 1613, 478, 1651, 1685, 553, 557, 558, 577, 587, 761, 1060, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 945, 946, 953, 1189, 1533, 1012, 1338, 545, 484, 493, 494, 495, 499, 500, 507, 514, 521, 550, 552, 1040, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1571, 1602, 1745, 1779, 1378, 829, 1205, 511, 480, 1580, 1615, 490, 519, 965, 571, 1734, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 562, 162, 383, 35, 43, 141, 101, 102, 124, 125, 126, 1718, 139, 140, 147, 1271, 1272, 526, 1579, 1720, 1725, 1730, 1731, 1752], "values": [0.998046875, 0.96044921875, 0.9404296875, 0.9404296875, 0.935546875, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.92822265625, 0.92822265625, 0.92822265625, 0.921875, 0.921875, 0.921875, 0.921875, 0.90673828125, 0.90576171875, 0.90576171875, 0.90185546875, 0.90185546875, 0.8974609375, 0.89208984375, 0.88818359375, 0.88720703125, 0.87890625, 0.869140625, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.84228515625, 0.841796875, 0.8359375, 0.8349609375, 0.83056640625, 0.83056640625, 0.83056640625, 0.82666015625, 0.82666015625, 0.82666015625, 0.82373046875, 0.82275390625, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81298828125, 0.8125, 0.8125, 0.80908203125, 0.80908203125, 0.8056640625, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.80322265625, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 214, 304, 189, 173, 284, 334, 365, 366, 80, 37, 47, 74, 77, 20, 132, 227, 237, 150, 380, 243, 196, 187, 110, 130, 200, 68, 276, 277, 278, 281, 180, 362, 72, 181, 364, 190, 151, 213, 203, 116, 117, 118, 282, 86, 381, 382, 24, 46, 179, 48, 359, 354, 355, 144, 66, 251, 177, 43, 148, 34, 35, 36, 61, 60, 218, 391, 392, 393, 394, 395, 396, 215, 254, 6, 63, 157, 158, 75, 76, 232, 160, 161, 178, 326, 336, 59, 129, 121, 123, 124], "values": [0.83984375, 0.83984375, 0.83984375, 0.66162109375, 0.66162109375, 0.66162109375, 0.64697265625, 0.64697265625, 0.513671875, 0.492431640625, 0.489990234375, 0.464111328125, 0.458984375, 0.454345703125, 0.441162109375, 0.42822265625, 0.42822265625, 0.42822265625, 0.419921875, 0.402587890625, 0.40234375, 0.402099609375, 0.402099609375, 0.39990234375, 0.396728515625, 0.386474609375, 0.368408203125, 0.354248046875, 0.350341796875, 0.3466796875, 0.3447265625, 0.343994140625, 0.337158203125, 0.3359375, 0.3359375, 0.333984375, 0.330810546875, 0.330810546875, 0.330810546875, 0.330810546875, 0.330078125, 0.323486328125, 0.318359375, 0.31201171875, 0.308349609375, 0.307373046875, 0.302734375, 0.302734375, 0.302001953125, 0.301025390625, 0.301025390625, 0.295654296875, 0.291748046875, 0.28857421875, 0.286865234375, 0.286865234375, 0.284912109375, 0.284912109375, 0.28369140625, 0.28173828125, 0.276123046875, 0.27587890625, 0.27587890625, 0.274169921875, 0.272705078125, 0.26904296875, 0.262451171875, 0.261474609375, 0.26123046875, 0.258544921875, 0.258544921875, 0.258544921875, 0.25830078125, 0.257080078125, 0.2568359375, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.25634765625, 0.254638671875, 0.252197265625, 0.251220703125, 0.24951171875, 0.24951171875, 0.2469482421875, 0.2469482421875, 0.2464599609375, 0.241455078125, 0.241455078125, 0.241455078125, 0.24072265625, 0.2398681640625, 0.2392578125, 0.23828125, 0.2357177734375, 0.2357177734375, 0.2357177734375]}, "13": {"indices": [275, 274, 276, 537, 513, 273, 272, 277, 575, 576, 573, 574, 498, 578, 529, 530, 2836, 2832, 2833, 1864, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 2478, 229, 230, 607, 293, 298, 442, 533, 1381, 526, 1067, 1070, 1990, 1991, 1992, 2128, 2129, 2882, 2884, 2885, 2525, 2458, 531, 270, 2457, 296, 279, 2875, 3040, 716, 717, 2839, 1026, 348, 349, 350, 363, 365, 370, 371, 254, 1251, 2843, 291, 2373, 2259, 2838, 1164, 515, 458, 2568, 2569, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 2406, 2407, 1906], "values": [0.625, 0.56689453125, 0.55078125, 0.548828125, 0.5439453125, 0.537109375, 0.5234375, 0.51220703125, 0.49609375, 0.49609375, 0.4921875, 0.4921875, 0.49169921875, 0.487548828125, 0.48291015625, 0.48291015625, 0.476318359375, 0.46630859375, 0.46630859375, 0.4658203125, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.44921875, 0.4453125, 0.4453125, 0.44091796875, 0.43603515625, 0.431396484375, 0.42578125, 0.421875, 0.416015625, 0.4111328125, 0.4111328125, 0.4111328125, 0.40966796875, 0.40966796875, 0.40966796875, 0.40625, 0.40625, 0.40576171875, 0.40576171875, 0.40576171875, 0.40380859375, 0.401611328125, 0.400390625, 0.3994140625, 0.3994140625, 0.394287109375, 0.392333984375, 0.39111328125, 0.3876953125, 0.387451171875, 0.387451171875, 0.38720703125, 0.385498046875, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.38427734375, 0.38427734375, 0.3837890625, 0.381591796875, 0.381103515625, 0.378662109375, 0.378173828125, 0.3759765625, 0.375, 0.3720703125, 0.371337890625, 0.371337890625, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.36865234375, 0.36865234375, 0.36767578125]}, "14": {"indices": [291, 2397, 2328, 293, 484, 485, 2404, 271, 273, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 495, 2568, 2569, 272, 2912, 557, 292, 2339, 2886, 1063, 1064, 1065, 303, 298, 2472, 748, 506, 750, 304, 901, 1598, 2525, 305, 2523, 284, 285, 286, 287, 288, 458, 575, 576, 2008, 2009, 2010, 2402, 2403, 300, 229, 230, 957, 789, 296, 302, 489, 539, 2366, 2163, 275, 1117, 297, 477, 478, 269, 420, 504, 1612, 2122, 703, 314, 2111, 2112, 474, 294, 295, 280, 281, 1088, 2687, 2688, 1069, 448, 2416, 743, 744, 745, 746, 680, 747, 749, 636, 770], "values": [0.625, 0.587890625, 0.5859375, 0.58056640625, 0.580078125, 0.580078125, 0.5751953125, 0.5478515625, 0.541015625, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.51318359375, 0.51318359375, 0.51318359375, 0.50732421875, 0.5068359375, 0.5068359375, 0.50390625, 0.50341796875, 0.49609375, 0.4853515625, 0.478515625, 0.4775390625, 0.4658203125, 0.4658203125, 0.4658203125, 0.46533203125, 0.465087890625, 0.456787109375, 0.453369140625, 0.451171875, 0.450439453125, 0.447509765625, 0.4453125, 0.4453125, 0.442138671875, 0.437255859375, 0.437255859375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.429931640625, 0.420166015625, 0.420166015625, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.419189453125, 0.41845703125, 0.41845703125, 0.417724609375, 0.417236328125, 0.4140625, 0.4140625, 0.41162109375, 0.404296875, 0.40234375, 0.39990234375, 0.3994140625, 0.396484375, 0.394287109375, 0.393310546875, 0.393310546875, 0.391845703125, 0.391357421875, 0.39111328125, 0.388916015625, 0.388427734375, 0.380859375, 0.3779296875, 0.377197265625, 0.377197265625, 0.3720703125, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.3681640625, 0.364990234375, 0.364990234375, 0.3642578125, 0.36328125, 0.359619140625, 0.359375, 0.359375, 0.359375, 0.359375, 0.353515625, 0.35302734375, 0.35302734375, 0.3505859375, 0.34765625]}, "15": {"indices": [570, 2770, 483, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 255, 260, 261, 1599, 1600, 1378, 1380, 2406, 2407, 259, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 2712, 789, 475, 486, 1117, 1294, 1295, 1333, 1041, 1052, 1124, 773, 923, 742, 366, 927, 928, 361, 422, 191, 192, 926, 2866, 1181, 1182, 1183, 1838, 1857, 2564, 2773, 2778, 2796, 2816, 431, 2065, 2520, 1029, 442, 2157, 2199, 2215, 2216, 2217, 1813, 1208, 1403, 2369, 2777, 461, 524, 525, 566, 572, 1381, 1661, 3032, 1071, 1072, 1462, 1463, 1464, 1465, 1466, 1907, 3029, 1602, 2625, 2609, 420, 1350, 1353], "values": [0.70166015625, 0.640625, 0.60107421875, 0.5263671875, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.478271484375, 0.458984375, 0.458984375, 0.458984375, 0.447998046875, 0.447998046875, 0.4453125, 0.4453125, 0.44140625, 0.44140625, 0.42431640625, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.416015625, 0.41259765625, 0.4091796875, 0.4091796875, 0.4091796875, 0.407470703125, 0.407470703125, 0.407470703125, 0.40625, 0.40625, 0.405029296875, 0.404541015625, 0.400390625, 0.399658203125, 0.3984375, 0.3974609375, 0.3974609375, 0.39599609375, 0.39599609375, 0.3955078125, 0.3955078125, 0.390380859375, 0.388916015625, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.386962890625, 0.386474609375, 0.386474609375, 0.386474609375, 0.386474609375, 0.3857421875, 0.383544921875, 0.38330078125, 0.38232421875, 0.382080078125, 0.382080078125, 0.380859375, 0.380859375, 0.380859375, 0.380859375, 0.378173828125, 0.375, 0.374267578125, 0.373046875, 0.372802734375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.36572265625, 0.365478515625, 0.363525390625, 0.359130859375, 0.359130859375, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.358642578125, 0.358154296875, 0.356201171875, 0.354736328125, 0.353271484375, 0.3525390625, 0.3525390625]}, "16": {"indices": [380, 3432, 202, 3343, 3347, 1879, 1880, 4445, 3155, 852, 3900, 3338, 3340, 2884, 2885, 2890, 2892, 1883, 1885, 1888, 1889, 1860, 3337, 3972, 3973, 4669, 3975, 3977, 3978, 3334, 3979, 3970, 1923, 1925, 3899, 4609, 4169, 990, 1062, 3909, 4662, 3464, 111, 112, 113, 114, 364, 4171, 4172, 793, 991, 891, 2658, 4867, 4887, 3966, 795, 796, 800, 1003, 2389, 4854, 3158, 3976, 2480, 2484, 4551, 4595, 4660, 4663, 4531, 1827, 994, 2994, 359, 1972, 1882, 1890, 1891, 4865, 1478, 2886, 33, 43, 2020, 3235, 881, 4425, 1016, 67, 1903, 2605, 1881, 986, 987, 116, 196, 2353, 2354, 2355], "values": [0.8515625, 0.68017578125, 0.64306640625, 0.623046875, 0.623046875, 0.6064453125, 0.6064453125, 0.60400390625, 0.59423828125, 0.5927734375, 0.5732421875, 0.56396484375, 0.56396484375, 0.5458984375, 0.5458984375, 0.5458984375, 0.5458984375, 0.54052734375, 0.54052734375, 0.54052734375, 0.54052734375, 0.53662109375, 0.53662109375, 0.5361328125, 0.5361328125, 0.529296875, 0.52734375, 0.52734375, 0.52734375, 0.5263671875, 0.5224609375, 0.521484375, 0.51953125, 0.51953125, 0.517578125, 0.5166015625, 0.51513671875, 0.5126953125, 0.51171875, 0.51171875, 0.51025390625, 0.5087890625, 0.5068359375, 0.5068359375, 0.5068359375, 0.5068359375, 0.5068359375, 0.505859375, 0.505859375, 0.50439453125, 0.50439453125, 0.50341796875, 0.5029296875, 0.50146484375, 0.499755859375, 0.4970703125, 0.495361328125, 0.495361328125, 0.495361328125, 0.495361328125, 0.494140625, 0.49267578125, 0.490234375, 0.489990234375, 0.48974609375, 0.48974609375, 0.48876953125, 0.48876953125, 0.48876953125, 0.48876953125, 0.48779296875, 0.487060546875, 0.486572265625, 0.485595703125, 0.485107421875, 0.482177734375, 0.4775390625, 0.4775390625, 0.4775390625, 0.4775390625, 0.47412109375, 0.47412109375, 0.47216796875, 0.47216796875, 0.47216796875, 0.4716796875, 0.469970703125, 0.46875, 0.462890625, 0.461181640625, 0.4609375, 0.460693359375, 0.45947265625, 0.458984375, 0.458984375, 0.45458984375, 0.45458984375, 0.45458984375, 0.45458984375, 0.45458984375]}, "17": {"indices": [29, 24, 8, 10, 6, 7, 19, 20, 25, 18, 11, 12, 31, 32, 22, 21, 27, 23, 28, 26, 3, 9, 34, 35, 4, 5, 1, 2, 30, 0, 13, 14, 15, 16, 17, 33], "values": [0.51904296875, 0.43994140625, 0.42724609375, 0.42724609375, 0.41064453125, 0.41064453125, 0.41064453125, 0.41064453125, 0.409912109375, 0.40771484375, 0.402099609375, 0.402099609375, 0.3779296875, 0.370849609375, 0.34814453125, 0.337890625, 0.330322265625, 0.3291015625, 0.271484375, 0.2646484375, 0.25048828125, 0.2373046875, 0.23583984375, 0.23583984375, 0.235107421875, 0.1968994140625, 0.182373046875, 0.182373046875, 0.154541015625, 0.11785888671875, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 26, 6, 7, 19, 20, 24, 8, 10, 30, 31, 21, 23, 9, 28, 27, 25, 29, 1, 2, 11, 12, 4, 34, 35, 5, 3, 0, 32, 33, 13, 14, 15, 16, 17], "values": [0.64501953125, 0.6005859375, 0.58251953125, 0.572265625, 0.572265625, 0.572265625, 0.572265625, 0.56884765625, 0.5595703125, 0.5595703125, 0.52294921875, 0.50537109375, 0.4970703125, 0.4951171875, 0.492919921875, 0.490234375, 0.478515625, 0.4306640625, 0.41845703125, 0.4169921875, 0.4169921875, 0.409912109375, 0.409912109375, 0.399169921875, 0.37255859375, 0.37255859375, 0.346435546875, 0.343994140625, 0.269287109375, 0.255615234375, 0.110107421875, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 53, 54, 165, 163, 88, 89, 113, 59, 61, 107, 212, 208, 197, 9, 121, 203, 5, 45, 57, 213, 64, 217, 8, 177, 65, 66, 72, 144, 145, 146, 182, 60, 210, 211, 174, 71, 200, 76, 48, 16, 18, 192, 228, 56, 82, 122, 25, 115, 196, 83, 4, 6, 7, 205, 128, 0, 112, 204, 207, 17, 191, 1, 245, 215, 67, 126, 143, 201, 181, 209, 84, 214, 199, 198, 130, 239, 24, 183, 114, 116, 91, 246, 185, 105, 235, 236, 124, 135, 193, 12, 179, 26, 188, 41, 249, 186], "values": [0.7890625, 0.77197265625, 0.77197265625, 0.55078125, 0.52685546875, 0.52685546875, 0.5166015625, 0.489501953125, 0.471435546875, 0.471435546875, 0.447509765625, 0.435302734375, 0.39013671875, 0.38671875, 0.37451171875, 0.360107421875, 0.35009765625, 0.34228515625, 0.34130859375, 0.334716796875, 0.33154296875, 0.3291015625, 0.3125, 0.299072265625, 0.296875, 0.28076171875, 0.27880859375, 0.272216796875, 0.252197265625, 0.252197265625, 0.251953125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.24609375, 0.2449951171875, 0.23828125, 0.23828125, 0.2353515625, 0.2174072265625, 0.208984375, 0.207763671875, 0.1929931640625, 0.1923828125, 0.1923828125, 0.19189453125, 0.1864013671875, 0.17529296875, 0.17529296875, 0.1749267578125, 0.174072265625, 0.1712646484375, 0.1712646484375, 0.170654296875, 0.1678466796875, 0.1678466796875, 0.1678466796875, 0.16552734375, 0.1639404296875, 0.1622314453125, 0.1607666015625, 0.15380859375, 0.1522216796875, 0.1466064453125, 0.145263671875, 0.14453125, 0.1427001953125, 0.14208984375, 0.1416015625, 0.1409912109375, 0.1409912109375, 0.1383056640625, 0.1363525390625, 0.1343994140625, 0.131103515625, 0.130126953125, 0.127197265625, 0.124267578125, 0.12249755859375, 0.117919921875, 0.11700439453125, 0.1097412109375, 0.108154296875, 0.108154296875, 0.10748291015625, 0.1055908203125, 0.10333251953125, 0.09893798828125, 0.09844970703125, 0.09552001953125, 0.09332275390625, 0.0882568359375, 0.08074951171875, 0.07720947265625, 0.07659912109375, 0.07452392578125, 0.07305908203125, 0.069091796875, 0.06878662109375, 0.06591796875]}}
v2 =  {"0": {"indices": [395, 686, 396, 434, 199, 21, 89, 180, 181, 783, 785, 379, 397, 758, 778, 779, 182, 627, 787, 788, 789, 203, 204, 791, 432, 811, 207, 209, 535, 626, 668, 408, 409, 634, 71, 110, 111, 410, 618, 620, 621, 622, 796, 793, 534, 86, 87, 153, 105, 172, 465, 469, 486, 487, 488, 545, 200, 166, 167, 174, 205, 433, 429, 40, 214, 215, 866, 795, 768, 769, 16, 631, 633, 767, 420, 436, 438, 444, 64, 161, 399, 404, 405, 406, 407, 558, 289, 489, 529, 530, 561, 88, 674, 613, 612, 152, 256, 364, 441, 163], "values": [0.6376953125, 0.595703125, 0.5703125, 0.56787109375, 0.56298828125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5400390625, 0.5400390625, 0.5380859375, 0.5380859375, 0.521484375, 0.521484375, 0.521484375, 0.51806640625, 0.50244140625, 0.498779296875, 0.498779296875, 0.498779296875, 0.4951171875, 0.4951171875, 0.479736328125, 0.46337890625, 0.452392578125, 0.449462890625, 0.449462890625, 0.435546875, 0.4287109375, 0.4287109375, 0.42431640625, 0.42431640625, 0.421630859375, 0.4150390625, 0.4150390625, 0.4150390625, 0.397705078125, 0.3916015625, 0.3916015625, 0.3916015625, 0.3916015625, 0.390869140625, 0.38818359375, 0.386962890625, 0.376708984375, 0.376708984375, 0.376220703125, 0.3623046875, 0.3623046875, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.35302734375, 0.349365234375, 0.347900390625, 0.343017578125, 0.343017578125, 0.340576171875, 0.336181640625, 0.3359375, 0.33203125, 0.329345703125, 0.3212890625, 0.3212890625, 0.319580078125, 0.3193359375, 0.313720703125, 0.313720703125, 0.310546875, 0.306884765625, 0.306884765625, 0.305419921875, 0.3046875, 0.3046875, 0.3046875, 0.3046875, 0.299560546875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.296875, 0.291748046875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28857421875, 0.28759765625, 0.28564453125, 0.283203125, 0.28076171875, 0.2802734375, 0.280029296875, 0.278564453125, 0.27783203125, 0.277587890625]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 59, 88, 89, 90, 91, 92, 96, 97, 66, 67, 21, 85, 139, 10, 45, 9, 47, 60, 48, 61, 94, 95, 62, 63, 40, 0, 137, 77, 78, 140, 53, 74, 98, 138, 157, 158, 159, 160, 76, 58, 15, 142, 135, 80, 81, 32, 131, 153, 154, 93, 57, 107, 112, 114, 106, 108, 109, 110, 111, 115, 27, 141, 127, 132, 133, 24, 25, 35, 36, 54, 28, 17, 124, 134, 82, 155, 125, 2, 46, 26, 51, 52, 22, 23, 34, 79, 73, 113, 143, 31, 33, 156, 147, 149], "values": [0.61376953125, 0.59765625, 0.59765625, 0.53369140625, 0.486572265625, 0.486572265625, 0.4541015625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.445556640625, 0.440673828125, 0.440673828125, 0.41015625, 0.388671875, 0.382080078125, 0.3759765625, 0.3505859375, 0.349609375, 0.333984375, 0.331787109375, 0.33154296875, 0.3310546875, 0.31689453125, 0.31689453125, 0.298583984375, 0.298583984375, 0.291748046875, 0.291259765625, 0.291015625, 0.28271484375, 0.28271484375, 0.282470703125, 0.27685546875, 0.265380859375, 0.258544921875, 0.253173828125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.2452392578125, 0.2384033203125, 0.2330322265625, 0.2298583984375, 0.2275390625, 0.227294921875, 0.227294921875, 0.2108154296875, 0.2108154296875, 0.204833984375, 0.204833984375, 0.1888427734375, 0.1864013671875, 0.18505859375, 0.18505859375, 0.18505859375, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.18212890625, 0.17626953125, 0.175048828125, 0.1632080078125, 0.1632080078125, 0.1632080078125, 0.16064453125, 0.16064453125, 0.159912109375, 0.159912109375, 0.159912109375, 0.154052734375, 0.1519775390625, 0.1407470703125, 0.1402587890625, 0.138671875, 0.137451171875, 0.1328125, 0.13134765625, 0.1298828125, 0.1251220703125, 0.1240234375, 0.1240234375, 0.1138916015625, 0.1138916015625, 0.10711669921875, 0.10614013671875, 0.10205078125, 0.0994873046875, 0.0968017578125, 0.0933837890625, 0.0933837890625, 0.0924072265625, 0.08441162109375, 0.07696533203125]}, "2": {"indices": [5, 44, 32, 43, 3, 18, 4, 55, 37, 38, 39, 31, 33, 8, 6, 7, 41, 42, 56, 30, 35, 36, 54, 34, 15, 9, 156, 22, 23, 26, 49, 48, 16, 10, 74, 12, 13, 14, 76, 24, 25, 88, 89, 90, 91, 92, 96, 97, 98, 75, 86, 87, 84, 141, 94, 95, 157, 158, 159, 160, 85, 155, 29, 17, 40, 151, 131, 46, 53, 20, 51, 52, 93, 2, 21, 69, 70, 0, 47, 153, 154, 71, 72, 127, 132, 133, 148, 149, 150, 124, 19, 50, 66, 67, 77, 78, 143, 45, 59, 138], "values": [0.7177734375, 0.658203125, 0.642578125, 0.64208984375, 0.62451171875, 0.62451171875, 0.60546875, 0.60302734375, 0.56201171875, 0.56201171875, 0.56201171875, 0.5439453125, 0.5439453125, 0.51806640625, 0.51318359375, 0.496337890625, 0.496337890625, 0.496337890625, 0.45556640625, 0.45263671875, 0.420166015625, 0.420166015625, 0.420166015625, 0.39111328125, 0.361083984375, 0.30419921875, 0.29833984375, 0.297119140625, 0.297119140625, 0.291259765625, 0.279541015625, 0.273681640625, 0.2626953125, 0.26123046875, 0.258544921875, 0.256591796875, 0.256591796875, 0.256591796875, 0.2529296875, 0.246337890625, 0.246337890625, 0.245361328125, 0.245361328125, 0.245361328125, 0.245361328125, 0.245361328125, 0.245361328125, 0.245361328125, 0.233642578125, 0.220703125, 0.211181640625, 0.211181640625, 0.2099609375, 0.20703125, 0.2030029296875, 0.2030029296875, 0.197265625, 0.197265625, 0.197265625, 0.197265625, 0.1961669921875, 0.185791015625, 0.1827392578125, 0.1810302734375, 0.1810302734375, 0.1796875, 0.17626953125, 0.1737060546875, 0.167236328125, 0.157958984375, 0.1533203125, 0.1533203125, 0.1444091796875, 0.1375732421875, 0.1292724609375, 0.1280517578125, 0.1280517578125, 0.127197265625, 0.10821533203125, 0.1031494140625, 0.1031494140625, 0.0992431640625, 0.0992431640625, 0.09881591796875, 0.09881591796875, 0.09881591796875, 0.0966796875, 0.0966796875, 0.0966796875, 0.08807373046875, 0.084228515625, 0.07977294921875, 0.071533203125, 0.071533203125, 0.069580078125, 0.069580078125, 0.06793212890625, 0.0675048828125, 0.055267333984375, 0.052642822265625]}, "3": {"indices": [66, 447, 420, 449, 450, 200, 204, 268, 442, 298, 325, 326, 64, 149, 444, 448, 89, 311, 4083, 46, 50, 65, 67, 775, 1014, 84, 4073, 68, 76, 85, 86, 312, 313, 3871, 526, 709, 1142, 1015, 2134, 2160, 8, 870, 3236, 451, 456, 4146, 4200, 391, 2702, 4380, 1499, 4228, 3874, 2887, 3100, 3268, 3269, 3270, 3271, 524, 397, 851, 852, 853, 2406, 2407, 4777, 4778, 4779, 148, 4067, 4068, 4383, 55, 56, 58, 2685, 1506, 1507, 209, 107, 1546, 1190, 88, 100, 1554, 1555, 90, 525, 682, 782, 804, 708, 4193, 4859, 2948, 57, 4350, 4355, 4360], "values": [0.99755859375, 0.99755859375, 0.947265625, 0.947265625, 0.947265625, 0.8203125, 0.8203125, 0.8203125, 0.8203125, 0.81884765625, 0.81884765625, 0.81884765625, 0.80419921875, 0.80419921875, 0.80419921875, 0.80419921875, 0.77392578125, 0.7431640625, 0.7421875, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.73486328125, 0.70947265625, 0.701171875, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.69189453125, 0.689453125, 0.66748046875, 0.66748046875, 0.66748046875, 0.642578125, 0.63671875, 0.63671875, 0.62548828125, 0.6123046875, 0.6025390625, 0.59521484375, 0.59521484375, 0.59130859375, 0.59130859375, 0.58837890625, 0.587890625, 0.587890625, 0.58740234375, 0.58642578125, 0.58447265625, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.583984375, 0.58203125, 0.58154296875, 0.5732421875, 0.5732421875, 0.5732421875, 0.5693359375, 0.5693359375, 0.56884765625, 0.56884765625, 0.56884765625, 0.56787109375, 0.56787109375, 0.56787109375, 0.56787109375, 0.560546875, 0.560546875, 0.560546875, 0.56005859375, 0.55908203125, 0.55908203125, 0.55810546875, 0.556640625, 0.55029296875, 0.54931640625, 0.54833984375, 0.54833984375, 0.54443359375, 0.54443359375, 0.5419921875, 0.5419921875, 0.54052734375, 0.54052734375, 0.54052734375, 0.53564453125, 0.53564453125, 0.53515625, 0.52587890625, 0.525390625, 0.52490234375, 0.52490234375, 0.52490234375]}, "4": {"indices": [212, 225, 234, 56, 57, 91, 181, 120, 121, 124, 126, 127, 232, 92, 209, 163, 179, 142, 177, 135, 178, 129, 239, 240, 17, 176, 61, 206, 159, 160, 70, 72, 249, 250, 107, 116, 117, 118, 119, 44, 110, 111, 132, 146, 147, 45, 233, 100, 158, 122, 7, 34, 109, 157, 180, 152, 153, 60, 36, 213, 11, 5, 53, 94, 104, 108, 113, 128, 130, 141, 148, 52, 247, 238, 65, 15, 14, 237, 54, 1, 2, 187, 221, 189, 190, 69, 29, 31, 12, 13, 51, 243, 9, 35, 246, 220, 46, 82, 85, 86], "values": [0.64208984375, 0.63525390625, 0.63525390625, 0.60107421875, 0.60107421875, 0.60107421875, 0.60107421875, 0.5751953125, 0.5751953125, 0.5751953125, 0.5751953125, 0.5751953125, 0.564453125, 0.53857421875, 0.52880859375, 0.52099609375, 0.52099609375, 0.483154296875, 0.4765625, 0.475341796875, 0.46630859375, 0.448974609375, 0.427490234375, 0.427490234375, 0.425537109375, 0.42431640625, 0.41748046875, 0.41064453125, 0.407958984375, 0.407958984375, 0.407470703125, 0.407470703125, 0.4052734375, 0.4052734375, 0.40478515625, 0.40478515625, 0.40478515625, 0.40478515625, 0.40478515625, 0.404052734375, 0.401123046875, 0.401123046875, 0.401123046875, 0.401123046875, 0.401123046875, 0.399658203125, 0.3994140625, 0.397705078125, 0.396484375, 0.39599609375, 0.395263671875, 0.395263671875, 0.37744140625, 0.376953125, 0.376220703125, 0.36865234375, 0.36865234375, 0.3671875, 0.3642578125, 0.3623046875, 0.3515625, 0.35107421875, 0.34619140625, 0.34521484375, 0.34521484375, 0.34521484375, 0.34521484375, 0.34521484375, 0.34521484375, 0.34521484375, 0.34521484375, 0.343994140625, 0.336181640625, 0.3310546875, 0.32958984375, 0.323974609375, 0.31689453125, 0.314208984375, 0.302490234375, 0.30078125, 0.30078125, 0.300048828125, 0.29931640625, 0.2978515625, 0.2978515625, 0.29736328125, 0.29345703125, 0.29345703125, 0.287841796875, 0.287841796875, 0.28662109375, 0.283447265625, 0.282958984375, 0.277587890625, 0.277587890625, 0.27734375, 0.27197265625, 0.27197265625, 0.27197265625, 0.27197265625]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4271, 4335, 4021, 4022, 4023, 4024, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 2336, 2359, 4306, 1074, 1111, 1153, 1171, 441, 4222, 4624, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 2368, 2766, 2797, 2834, 2843, 4977, 4852, 4341, 4342, 4343, 4504, 2868, 2928, 2936, 4757, 4775, 1370, 3417, 3687, 3695, 3699, 3700, 4177, 4500, 4577, 4340, 4616, 3223, 3751, 3838, 3839, 3858, 3861, 933, 4250, 4076, 4079, 1113, 1088, 3585, 4790, 4080, 1075, 1078, 1093, 3697, 3288], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.9658203125, 0.8173828125, 0.8173828125, 0.78076171875, 0.669921875, 0.6689453125, 0.66845703125, 0.66845703125, 0.66845703125, 0.66845703125, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.666015625, 0.65087890625, 0.6357421875, 0.62744140625, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.6162109375, 0.615234375, 0.615234375, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.6103515625, 0.607421875, 0.60107421875, 0.6005859375, 0.6005859375, 0.6005859375, 0.599609375, 0.58544921875, 0.58544921875, 0.58544921875, 0.57958984375, 0.57958984375, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5791015625, 0.5771484375, 0.57373046875, 0.57275390625, 0.55859375, 0.5556640625, 0.5556640625, 0.5498046875, 0.5498046875, 0.5498046875, 0.5498046875, 0.54931640625, 0.546875, 0.537109375, 0.537109375, 0.53466796875, 0.5341796875, 0.53369140625, 0.5322265625, 0.52734375, 0.52685546875, 0.52685546875, 0.52685546875, 0.52587890625, 0.52099609375]}, "6": {"indices": [377, 333, 236, 237, 239, 240, 241, 920, 378, 1144, 238, 3153, 3329, 1107, 208, 1594, 1595, 2590, 44, 55, 56, 62, 242, 420, 421, 422, 3567, 3568, 3572, 3573, 1089, 1133, 1153, 68, 69, 180, 1179, 336, 209, 919, 927, 928, 15, 382, 3951, 1803, 2438, 3590, 2884, 3162, 3163, 3164, 3166, 3174, 3175, 3226, 3227, 3324, 3269, 2860, 624, 1020, 1860, 3314, 3315, 4406, 921, 168, 2807, 1798, 1799, 1804, 1819, 1820, 2853, 40, 620, 871, 874, 3976, 3977, 2582, 2585, 2586, 2587, 3585, 1802, 4187, 4188, 4190, 4191, 1173, 4584, 3570, 574, 4635, 196, 193, 197, 210], "values": [0.70849609375, 0.6748046875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.63134765625, 0.5908203125, 0.52685546875, 0.51806640625, 0.496826171875, 0.4814453125, 0.474365234375, 0.455322265625, 0.453125, 0.453125, 0.436767578125, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.4296875, 0.429443359375, 0.429443359375, 0.429443359375, 0.422119140625, 0.422119140625, 0.422119140625, 0.422119140625, 0.420166015625, 0.420166015625, 0.420166015625, 0.4140625, 0.4140625, 0.41357421875, 0.41259765625, 0.410400390625, 0.405517578125, 0.40185546875, 0.39990234375, 0.39990234375, 0.3955078125, 0.39501953125, 0.39501953125, 0.394775390625, 0.391845703125, 0.3916015625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.385009765625, 0.38330078125, 0.383056640625, 0.379638671875, 0.379638671875, 0.379638671875, 0.377197265625, 0.377197265625, 0.377197265625, 0.375, 0.3740234375, 0.3740234375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.37255859375, 0.371826171875, 0.37109375, 0.37109375, 0.37109375, 0.37109375, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.368408203125, 0.368408203125, 0.3681640625, 0.367431640625, 0.36669921875, 0.36669921875, 0.36669921875, 0.36669921875, 0.365478515625, 0.3623046875, 0.361083984375, 0.360595703125, 0.35888671875, 0.3544921875, 0.354248046875, 0.354248046875, 0.351318359375]}, "7": {"indices": [54, 27, 31, 34, 143, 32, 33, 66, 53, 56, 61, 62, 63, 35, 19, 55, 49, 227, 124, 26, 50, 51, 28, 47, 48, 29, 57, 60, 119, 10, 118, 192, 88, 132, 133, 204, 83, 45, 46, 52, 157, 158, 159, 160, 97, 9, 59, 1, 74, 75, 76, 77, 175, 111, 213, 30, 109, 219, 220, 221, 178, 179, 180, 181, 182, 183, 170, 172, 173, 72, 18, 128, 230, 44, 130, 138, 114, 58, 217, 194, 121, 113, 73, 186, 200, 123, 125, 214, 25, 64, 120, 117, 13, 232, 116, 225, 0, 185, 15, 16], "values": [0.8212890625, 0.67578125, 0.6259765625, 0.6259765625, 0.5693359375, 0.560546875, 0.5458984375, 0.54150390625, 0.53173828125, 0.5234375, 0.5234375, 0.5234375, 0.485107421875, 0.477783203125, 0.47265625, 0.4638671875, 0.439208984375, 0.434814453125, 0.430419921875, 0.42138671875, 0.404541015625, 0.39794921875, 0.396240234375, 0.3876953125, 0.3876953125, 0.3759765625, 0.366455078125, 0.358154296875, 0.350830078125, 0.348876953125, 0.346435546875, 0.34423828125, 0.343017578125, 0.340576171875, 0.340576171875, 0.337890625, 0.33740234375, 0.328369140625, 0.325927734375, 0.32568359375, 0.3251953125, 0.3251953125, 0.3251953125, 0.3251953125, 0.320556640625, 0.31494140625, 0.31005859375, 0.3017578125, 0.2998046875, 0.2998046875, 0.2998046875, 0.2998046875, 0.294677734375, 0.29150390625, 0.2900390625, 0.281982421875, 0.278564453125, 0.2744140625, 0.2744140625, 0.2744140625, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.272216796875, 0.26806640625, 0.26806640625, 0.26806640625, 0.266845703125, 0.260498046875, 0.250732421875, 0.2490234375, 0.247802734375, 0.237060546875, 0.237060546875, 0.2362060546875, 0.2332763671875, 0.231689453125, 0.22900390625, 0.225830078125, 0.2249755859375, 0.219482421875, 0.21044921875, 0.2093505859375, 0.2080078125, 0.2080078125, 0.20703125, 0.205810546875, 0.2017822265625, 0.193603515625, 0.1920166015625, 0.1888427734375, 0.1866455078125, 0.1846923828125, 0.1842041015625, 0.181884765625, 0.17822265625, 0.17626953125, 0.17626953125]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 640, 1377, 1600, 1601, 1602, 656, 441, 442, 443, 161, 162, 650, 554, 555, 159, 1973, 274, 277, 454, 1972, 1810, 501, 451, 157, 183, 156, 1491, 1492, 1959, 174, 175, 2222, 473, 474, 497, 498, 499, 1195, 1874, 177, 178, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 923, 680, 418, 506, 507, 508, 565, 566, 567, 603, 1078, 1079, 1203, 1204, 372, 1328, 1408, 2004, 649, 1687, 1081, 1138, 1232, 1240, 494, 495, 496, 1324, 1325, 1326, 1327, 1406, 1755, 1841, 1844, 1936], "values": [0.908203125, 0.908203125, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.87890625, 0.8642578125, 0.82373046875, 0.7802734375, 0.76171875, 0.76123046875, 0.6728515625, 0.6728515625, 0.6728515625, 0.662109375, 0.64697265625, 0.64697265625, 0.64697265625, 0.62646484375, 0.62646484375, 0.623046875, 0.609375, 0.609375, 0.58447265625, 0.5703125, 0.55810546875, 0.55810546875, 0.55810546875, 0.54736328125, 0.53173828125, 0.525390625, 0.52392578125, 0.5205078125, 0.5205078125, 0.51806640625, 0.51806640625, 0.51806640625, 0.51708984375, 0.51416015625, 0.51416015625, 0.513671875, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.50146484375, 0.5009765625, 0.49169921875, 0.49072265625, 0.49072265625, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.489990234375, 0.48583984375, 0.462646484375, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.455078125, 0.45458984375, 0.4521484375, 0.451416015625, 0.451416015625, 0.44970703125, 0.448974609375, 0.4404296875, 0.4404296875, 0.4404296875, 0.4404296875, 0.438720703125, 0.438720703125, 0.438720703125, 0.434326171875, 0.434326171875, 0.434326171875, 0.434326171875, 0.43359375, 0.43359375, 0.43359375, 0.43359375, 0.43359375]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2840, 2839, 2919, 283, 2473, 2474, 3631, 752, 2475, 2941, 2944, 212, 213, 220, 221, 687, 1027, 286, 751, 422, 4165, 174, 278, 4204, 2957, 1790, 2916, 873, 4434, 4435, 827, 835, 755, 756, 2018, 4507, 828, 834, 1075, 1076, 1077, 1339, 2448, 2481, 4488, 4383, 772, 4349, 4350, 4376, 4533, 2476, 2803, 2961, 4348, 4351, 866, 867, 1944, 1031, 2195, 2308, 4676, 796, 2431, 1337, 749, 904, 2225, 36, 2772, 262, 630, 3389, 624, 753, 754, 905, 1457, 258, 1979, 2777, 4495, 872, 1902, 2976, 2977, 3630, 684, 686, 189, 3810, 3811, 277, 2557], "values": [0.998046875, 0.88818359375, 0.845703125, 0.7978515625, 0.7978515625, 0.67724609375, 0.6591796875, 0.65283203125, 0.62744140625, 0.62744140625, 0.62744140625, 0.61474609375, 0.607421875, 0.59814453125, 0.59814453125, 0.59814453125, 0.5859375, 0.5859375, 0.5859375, 0.5859375, 0.5859375, 0.58544921875, 0.56201171875, 0.55517578125, 0.552734375, 0.537109375, 0.53076171875, 0.53076171875, 0.51953125, 0.51416015625, 0.51025390625, 0.51025390625, 0.50927734375, 0.5029296875, 0.5029296875, 0.501953125, 0.501953125, 0.5, 0.5, 0.495849609375, 0.49560546875, 0.49267578125, 0.49267578125, 0.49267578125, 0.49267578125, 0.49267578125, 0.49267578125, 0.48876953125, 0.48388671875, 0.4814453125, 0.469970703125, 0.46826171875, 0.44970703125, 0.44970703125, 0.44970703125, 0.443603515625, 0.440673828125, 0.440673828125, 0.440673828125, 0.440673828125, 0.440673828125, 0.43212890625, 0.43212890625, 0.430908203125, 0.42333984375, 0.42333984375, 0.42333984375, 0.42333984375, 0.4169921875, 0.41357421875, 0.4130859375, 0.412109375, 0.412109375, 0.412109375, 0.41064453125, 0.409423828125, 0.40478515625, 0.402587890625, 0.39599609375, 0.395263671875, 0.395263671875, 0.395263671875, 0.395263671875, 0.39501953125, 0.389404296875, 0.386962890625, 0.3837890625, 0.3837890625, 0.38232421875, 0.38232421875, 0.38134765625, 0.38134765625, 0.378662109375, 0.37841796875, 0.37841796875, 0.3779296875, 0.376953125, 0.376953125, 0.376708984375, 0.375732421875]}, "10": {"indices": [262, 267, 279, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 462, 1346, 1665, 1039, 1653, 1654, 1666, 691, 1286, 1668, 1656, 648, 754, 794, 273, 277, 278, 1521, 1553, 1565, 501, 730, 1333, 1340, 1697, 1699, 1702, 570, 1192, 1445, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 340, 1669, 1751, 1344, 334, 1677, 1671, 1673, 1472, 123, 679, 685, 792, 1691, 795, 884, 1623, 1646, 1664, 1424, 1524, 1548, 1672, 1221, 1252, 1279, 1283, 524, 502, 212, 503, 631, 651, 751, 763, 802, 891], "values": [0.99755859375, 0.99755859375, 0.99755859375, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.89013671875, 0.87646484375, 0.87646484375, 0.87646484375, 0.87548828125, 0.86572265625, 0.86572265625, 0.86572265625, 0.8603515625, 0.8603515625, 0.8603515625, 0.85986328125, 0.85205078125, 0.85205078125, 0.85205078125, 0.84619140625, 0.84619140625, 0.84619140625, 0.8408203125, 0.8408203125, 0.8408203125, 0.84033203125, 0.84033203125, 0.8369140625, 0.8369140625, 0.83447265625, 0.83447265625, 0.83447265625, 0.833984375, 0.83154296875, 0.83154296875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82373046875, 0.82177734375, 0.81494140625, 0.81494140625, 0.8125, 0.8115234375, 0.81103515625, 0.810546875, 0.810546875, 0.81005859375, 0.8076171875, 0.8076171875, 0.8076171875, 0.8076171875, 0.80712890625, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.80078125, 0.80078125, 0.80078125, 0.79931640625, 0.798828125, 0.798828125, 0.798828125, 0.798828125, 0.79736328125, 0.79345703125, 0.79296875, 0.7919921875, 0.7919921875, 0.7919921875, 0.7919921875, 0.7919921875, 0.7919921875, 0.7919921875]}, "11": {"indices": [457, 1613, 478, 1651, 1685, 553, 557, 558, 577, 587, 761, 1060, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 945, 946, 953, 1189, 1533, 1012, 1338, 545, 484, 493, 494, 495, 499, 500, 507, 514, 521, 550, 552, 1040, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1571, 1602, 1745, 1779, 1378, 829, 511, 1205, 480, 1580, 1615, 490, 519, 965, 571, 1734, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 562, 162, 383, 35, 43, 141, 101, 102, 124, 125, 126, 1718, 139, 140, 147, 1271, 1272, 526, 1579, 1720, 1725, 1730, 1731, 1752], "values": [0.998046875, 0.96044921875, 0.9404296875, 0.9404296875, 0.935546875, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.9345703125, 0.92822265625, 0.92822265625, 0.92822265625, 0.921875, 0.921875, 0.921875, 0.921875, 0.90673828125, 0.90576171875, 0.90576171875, 0.90185546875, 0.90185546875, 0.8974609375, 0.89208984375, 0.88818359375, 0.88720703125, 0.87890625, 0.869140625, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.8466796875, 0.84228515625, 0.841796875, 0.8349609375, 0.8349609375, 0.83056640625, 0.83056640625, 0.83056640625, 0.82666015625, 0.82666015625, 0.82666015625, 0.82373046875, 0.82275390625, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81982421875, 0.81298828125, 0.8125, 0.8125, 0.80908203125, 0.80908203125, 0.8056640625, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.8046875, 0.80322265625, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80224609375, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125, 0.80126953125]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 173, 304, 214, 189, 284, 334, 365, 366, 80, 37, 47, 74, 77, 20, 132, 227, 237, 150, 380, 243, 196, 364, 187, 110, 130, 200, 68, 276, 277, 278, 281, 180, 362, 72, 181, 190, 151, 213, 203, 116, 117, 118, 177, 282, 381, 382, 24, 46, 179, 48, 86, 34, 35, 36, 359, 354, 355, 144, 66, 251, 63, 43, 148, 61, 60, 218, 391, 392, 393, 394, 395, 396, 215, 254, 6, 157, 158, 232, 160, 161, 178, 326, 336, 59, 129, 121, 123, 124, 9, 333], "values": [0.83984375, 0.83984375, 0.83984375, 0.66162109375, 0.66162109375, 0.66162109375, 0.64697265625, 0.64697265625, 0.513671875, 0.492431640625, 0.4677734375, 0.464111328125, 0.459228515625, 0.458984375, 0.441162109375, 0.42822265625, 0.42822265625, 0.42822265625, 0.419921875, 0.402587890625, 0.40234375, 0.402099609375, 0.402099609375, 0.39990234375, 0.396728515625, 0.386474609375, 0.368408203125, 0.354248046875, 0.350341796875, 0.3466796875, 0.3447265625, 0.34423828125, 0.343994140625, 0.337158203125, 0.3359375, 0.3359375, 0.333984375, 0.330810546875, 0.330810546875, 0.330810546875, 0.330810546875, 0.330078125, 0.323486328125, 0.318359375, 0.31201171875, 0.307373046875, 0.302734375, 0.302734375, 0.302001953125, 0.301025390625, 0.301025390625, 0.295654296875, 0.294677734375, 0.291748046875, 0.286865234375, 0.286865234375, 0.284912109375, 0.284912109375, 0.28369140625, 0.28173828125, 0.28076171875, 0.2802734375, 0.2802734375, 0.2802734375, 0.276123046875, 0.27587890625, 0.27587890625, 0.274169921875, 0.272705078125, 0.26904296875, 0.267578125, 0.261474609375, 0.26123046875, 0.25830078125, 0.257080078125, 0.2568359375, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.25634765625, 0.254638671875, 0.252197265625, 0.24951171875, 0.24951171875, 0.2464599609375, 0.241455078125, 0.241455078125, 0.241455078125, 0.24072265625, 0.2398681640625, 0.2392578125, 0.23828125, 0.2357177734375, 0.2357177734375, 0.2357177734375, 0.2333984375, 0.2303466796875]}, "13": {"indices": [275, 274, 537, 276, 513, 273, 272, 277, 575, 576, 573, 574, 498, 578, 529, 530, 2836, 2832, 2833, 1864, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 2478, 229, 230, 607, 293, 298, 442, 533, 1381, 526, 1067, 1070, 1990, 1991, 1992, 2128, 2129, 2882, 2884, 2885, 2525, 2458, 531, 270, 2457, 296, 279, 2875, 716, 717, 3040, 2839, 1026, 348, 349, 350, 363, 365, 370, 371, 254, 1251, 2843, 291, 2373, 2259, 2838, 1164, 515, 458, 2568, 2569, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 2406, 2407, 1906], "values": [0.625, 0.56689453125, 0.55322265625, 0.55078125, 0.5439453125, 0.537109375, 0.5234375, 0.51220703125, 0.49609375, 0.49609375, 0.4921875, 0.4921875, 0.49169921875, 0.487548828125, 0.48291015625, 0.48291015625, 0.476318359375, 0.46630859375, 0.46630859375, 0.4658203125, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.451904296875, 0.44921875, 0.4453125, 0.4453125, 0.44091796875, 0.43603515625, 0.431396484375, 0.42578125, 0.421875, 0.416015625, 0.4111328125, 0.4111328125, 0.4111328125, 0.40966796875, 0.40966796875, 0.40966796875, 0.40625, 0.40625, 0.40576171875, 0.40576171875, 0.40576171875, 0.40380859375, 0.401611328125, 0.400390625, 0.3994140625, 0.3994140625, 0.394287109375, 0.392333984375, 0.39111328125, 0.38916015625, 0.38916015625, 0.3876953125, 0.38720703125, 0.385498046875, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.384521484375, 0.38427734375, 0.38427734375, 0.3837890625, 0.381591796875, 0.381103515625, 0.378662109375, 0.378173828125, 0.3759765625, 0.375, 0.3720703125, 0.371337890625, 0.371337890625, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.3701171875, 0.36865234375, 0.36865234375, 0.36767578125]}, "14": {"indices": [291, 2397, 2328, 293, 271, 484, 485, 2404, 273, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 495, 272, 2912, 2568, 2569, 557, 292, 2339, 2886, 1063, 1064, 1065, 303, 298, 2472, 748, 506, 750, 304, 901, 1598, 2525, 305, 2523, 284, 285, 286, 287, 288, 458, 575, 576, 2008, 2009, 2010, 2402, 2403, 300, 229, 230, 957, 789, 296, 302, 489, 539, 2366, 2163, 275, 1117, 297, 477, 478, 269, 420, 504, 1612, 2122, 703, 314, 2111, 2112, 474, 294, 295, 280, 281, 1088, 2687, 2688, 1069, 448, 2416, 743, 744, 745, 746, 680, 747, 749, 636, 770], "values": [0.625, 0.587890625, 0.5859375, 0.58056640625, 0.580078125, 0.580078125, 0.580078125, 0.580078125, 0.541015625, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.52734375, 0.51318359375, 0.51318359375, 0.51318359375, 0.50732421875, 0.50390625, 0.50341796875, 0.50146484375, 0.50146484375, 0.49609375, 0.4853515625, 0.478515625, 0.4775390625, 0.4658203125, 0.4658203125, 0.4658203125, 0.46533203125, 0.465087890625, 0.456787109375, 0.453369140625, 0.451171875, 0.450439453125, 0.447509765625, 0.4453125, 0.4453125, 0.442138671875, 0.437255859375, 0.437255859375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.4365234375, 0.429931640625, 0.420166015625, 0.420166015625, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.41943359375, 0.419189453125, 0.41845703125, 0.41845703125, 0.417724609375, 0.417236328125, 0.4140625, 0.4140625, 0.41162109375, 0.404296875, 0.40234375, 0.39990234375, 0.3994140625, 0.396484375, 0.394287109375, 0.393310546875, 0.393310546875, 0.391845703125, 0.391357421875, 0.39111328125, 0.388916015625, 0.388427734375, 0.380859375, 0.3779296875, 0.377197265625, 0.377197265625, 0.3720703125, 0.36962890625, 0.36962890625, 0.368408203125, 0.368408203125, 0.3681640625, 0.364990234375, 0.364990234375, 0.3642578125, 0.36328125, 0.359619140625, 0.359375, 0.359375, 0.359375, 0.359375, 0.353515625, 0.35302734375, 0.35302734375, 0.3505859375, 0.34765625]}, "15": {"indices": [570, 2770, 483, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 255, 260, 261, 1599, 1600, 1378, 1380, 2406, 2407, 259, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 2712, 789, 475, 486, 1117, 1294, 1295, 1333, 1041, 1052, 1124, 773, 923, 742, 366, 927, 928, 361, 422, 191, 192, 926, 2866, 2564, 1181, 1182, 1183, 1838, 1857, 2773, 2778, 2796, 2816, 431, 2065, 2520, 1029, 442, 2157, 2199, 2215, 2216, 2217, 1813, 1208, 1403, 2369, 2777, 461, 524, 525, 566, 572, 1381, 1661, 3032, 1071, 1072, 1462, 1463, 1464, 1465, 1466, 1907, 3029, 1602, 2625, 2609, 420, 1350, 1353], "values": [0.70166015625, 0.640625, 0.60107421875, 0.5263671875, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.483642578125, 0.478271484375, 0.458984375, 0.458984375, 0.458984375, 0.447998046875, 0.447998046875, 0.4453125, 0.4453125, 0.44140625, 0.44140625, 0.42431640625, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.419677734375, 0.416015625, 0.41259765625, 0.4091796875, 0.4091796875, 0.4091796875, 0.407470703125, 0.407470703125, 0.407470703125, 0.40625, 0.40625, 0.405029296875, 0.404541015625, 0.400390625, 0.399658203125, 0.3984375, 0.3974609375, 0.3974609375, 0.39599609375, 0.39599609375, 0.3955078125, 0.3955078125, 0.390380859375, 0.388916015625, 0.388671875, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.388427734375, 0.386474609375, 0.386474609375, 0.386474609375, 0.386474609375, 0.3857421875, 0.383544921875, 0.38330078125, 0.38232421875, 0.382080078125, 0.382080078125, 0.380859375, 0.380859375, 0.380859375, 0.380859375, 0.378173828125, 0.375, 0.374267578125, 0.373046875, 0.372802734375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.370849609375, 0.36572265625, 0.365478515625, 0.363525390625, 0.359130859375, 0.359130859375, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.35888671875, 0.358642578125, 0.358154296875, 0.356201171875, 0.354736328125, 0.353271484375, 0.3525390625, 0.3525390625]}, "16": {"indices": [380, 3432, 202, 3343, 3347, 1879, 1880, 4445, 3972, 3973, 2658, 2884, 2885, 2890, 2892, 852, 3338, 3340, 3155, 3909, 4662, 4887, 3970, 3464, 4609, 1062, 1882, 1890, 1891, 3235, 1895, 359, 1883, 1885, 1888, 1889, 111, 112, 113, 114, 364, 3979, 2994, 3334, 3337, 3900, 2016, 793, 991, 891, 990, 3899, 4669, 1827, 1832, 4865, 3975, 3977, 3978, 3332, 1881, 2389, 4169, 795, 796, 800, 1860, 2480, 2484, 1972, 4854, 4551, 4595, 4660, 4663, 1016, 3966, 1903, 4867, 4171, 4172, 881, 4425, 2425, 2426, 3778, 3976, 3158, 3647, 33, 43, 2020, 1003, 2886, 2593, 4580, 67, 3156, 201, 3725], "values": [0.8583984375, 0.68115234375, 0.63330078125, 0.60009765625, 0.60009765625, 0.58056640625, 0.58056640625, 0.56884765625, 0.5576171875, 0.5576171875, 0.5517578125, 0.55029296875, 0.55029296875, 0.55029296875, 0.55029296875, 0.5458984375, 0.53076171875, 0.53076171875, 0.52880859375, 0.52587890625, 0.521484375, 0.51953125, 0.5166015625, 0.513671875, 0.513671875, 0.51171875, 0.51123046875, 0.51123046875, 0.51123046875, 0.51123046875, 0.50927734375, 0.50830078125, 0.50048828125, 0.50048828125, 0.50048828125, 0.50048828125, 0.5, 0.5, 0.5, 0.5, 0.5, 0.49658203125, 0.495361328125, 0.4951171875, 0.494140625, 0.4921875, 0.4912109375, 0.49072265625, 0.49072265625, 0.48876953125, 0.48828125, 0.48828125, 0.486083984375, 0.48291015625, 0.482177734375, 0.48193359375, 0.4814453125, 0.4814453125, 0.4814453125, 0.481201171875, 0.47998046875, 0.478271484375, 0.47802734375, 0.4775390625, 0.4775390625, 0.4775390625, 0.474365234375, 0.47412109375, 0.47412109375, 0.47216796875, 0.4716796875, 0.46875, 0.46875, 0.46875, 0.46875, 0.467041015625, 0.466064453125, 0.462158203125, 0.460693359375, 0.4580078125, 0.4580078125, 0.457275390625, 0.457275390625, 0.45263671875, 0.45263671875, 0.452392578125, 0.4521484375, 0.45068359375, 0.4453125, 0.444580078125, 0.444580078125, 0.444580078125, 0.4443359375, 0.442626953125, 0.44189453125, 0.44140625, 0.440673828125, 0.440185546875, 0.43896484375, 0.43701171875]}, "17": {"indices": [29, 24, 18, 8, 10, 6, 7, 19, 20, 22, 25, 31, 11, 12, 32, 21, 23, 27, 28, 26, 3, 34, 35, 4, 9, 1, 2, 5, 30, 0, 33, 13, 14, 15, 16, 17], "values": [0.494873046875, 0.485107421875, 0.47216796875, 0.454833984375, 0.454833984375, 0.429443359375, 0.429443359375, 0.429443359375, 0.429443359375, 0.42724609375, 0.412109375, 0.40625, 0.38427734375, 0.38427734375, 0.3720703125, 0.3564453125, 0.3193359375, 0.31689453125, 0.2958984375, 0.291259765625, 0.27294921875, 0.271484375, 0.271484375, 0.2489013671875, 0.2401123046875, 0.203857421875, 0.203857421875, 0.17822265625, 0.1605224609375, 0.1273193359375, 0.0479736328125, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 22, 26, 6, 7, 19, 20, 24, 8, 10, 30, 31, 21, 23, 9, 28, 27, 25, 29, 1, 2, 11, 12, 4, 34, 35, 5, 3, 0, 32, 33, 13, 14, 15, 16, 17], "values": [0.64404296875, 0.6005859375, 0.58251953125, 0.572265625, 0.572265625, 0.572265625, 0.572265625, 0.56884765625, 0.5595703125, 0.5595703125, 0.52294921875, 0.50537109375, 0.4970703125, 0.4951171875, 0.492919921875, 0.490234375, 0.478515625, 0.4306640625, 0.41845703125, 0.4169921875, 0.4169921875, 0.409912109375, 0.409912109375, 0.399169921875, 0.37255859375, 0.37255859375, 0.346435546875, 0.345458984375, 0.265869140625, 0.255615234375, 0.110107421875, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 53, 54, 165, 163, 88, 89, 113, 59, 61, 107, 212, 208, 197, 121, 9, 203, 5, 45, 57, 213, 64, 217, 8, 177, 65, 66, 72, 144, 145, 146, 182, 60, 174, 210, 211, 71, 200, 76, 48, 16, 18, 192, 228, 56, 82, 122, 25, 115, 196, 83, 4, 6, 7, 205, 128, 0, 112, 204, 207, 17, 191, 1, 245, 215, 67, 126, 143, 201, 181, 209, 84, 214, 199, 198, 130, 239, 24, 183, 114, 116, 91, 246, 185, 105, 235, 236, 124, 216, 135, 193, 73, 12, 179, 26, 188, 249], "values": [0.7890625, 0.77197265625, 0.77197265625, 0.55078125, 0.52685546875, 0.52685546875, 0.5166015625, 0.489501953125, 0.471435546875, 0.471435546875, 0.447509765625, 0.435302734375, 0.39013671875, 0.38671875, 0.37451171875, 0.360107421875, 0.35009765625, 0.34130859375, 0.3369140625, 0.334716796875, 0.33154296875, 0.3291015625, 0.3125, 0.299072265625, 0.296875, 0.28076171875, 0.27880859375, 0.272216796875, 0.252197265625, 0.252197265625, 0.251953125, 0.2462158203125, 0.2462158203125, 0.2462158203125, 0.24609375, 0.2449951171875, 0.2353515625, 0.229248046875, 0.229248046875, 0.2174072265625, 0.208984375, 0.207763671875, 0.1929931640625, 0.1923828125, 0.1923828125, 0.19189453125, 0.1864013671875, 0.17529296875, 0.17529296875, 0.1749267578125, 0.174072265625, 0.1712646484375, 0.1712646484375, 0.170654296875, 0.1678466796875, 0.1678466796875, 0.1678466796875, 0.16552734375, 0.1639404296875, 0.1622314453125, 0.1607666015625, 0.15380859375, 0.1522216796875, 0.1466064453125, 0.145263671875, 0.14453125, 0.1427001953125, 0.14208984375, 0.1416015625, 0.1409912109375, 0.1409912109375, 0.1383056640625, 0.1363525390625, 0.1343994140625, 0.131103515625, 0.130126953125, 0.127197265625, 0.124267578125, 0.12249755859375, 0.117919921875, 0.11700439453125, 0.1097412109375, 0.108154296875, 0.108154296875, 0.10748291015625, 0.1055908203125, 0.10333251953125, 0.09893798828125, 0.09844970703125, 0.09552001953125, 0.09332275390625, 0.09228515625, 0.0882568359375, 0.08074951171875, 0.07763671875, 0.07720947265625, 0.07659912109375, 0.07452392578125, 0.07305908203125, 0.06878662109375]}}

for i in range(20):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])


v1 =  {"0": {"indices": [], "values": []}, "1": {"indices": [75, 71, 72, 84, 86, 87, 88, 89, 90, 91, 92, 96, 97, 59, 66, 67, 10, 21, 139, 9, 85, 48, 61, 60, 45, 53, 94, 95, 47, 62, 63, 137, 0, 77, 78, 140, 40, 138, 74, 98, 157, 158, 159, 160, 76, 58, 80, 81, 15, 142, 153, 154, 32, 93, 135, 106, 108, 109, 110, 111, 115, 107, 112, 114, 57, 17, 28, 141, 27, 131, 82, 155, 127, 132, 133, 24, 25, 79, 125, 134, 124, 35, 36, 54, 2, 22, 23, 73, 46, 51, 52, 143, 146, 31, 33, 156, 147, 34, 19, 149], "values": [0.60595703125, 0.56787109375, 0.56787109375, 0.5126953125, 0.497314453125, 0.497314453125, 0.47412109375, 0.47412109375, 0.47412109375, 0.47412109375, 0.47412109375, 0.47412109375, 0.47412109375, 0.44287109375, 0.43310546875, 0.43310546875, 0.406005859375, 0.405517578125, 0.383056640625, 0.38134765625, 0.37158203125, 0.357421875, 0.34326171875, 0.3359375, 0.335205078125, 0.31787109375, 0.316162109375, 0.316162109375, 0.3134765625, 0.31298828125, 0.31298828125, 0.297119140625, 0.289306640625, 0.279296875, 0.279296875, 0.277099609375, 0.267578125, 0.258544921875, 0.254150390625, 0.2529296875, 0.251953125, 0.251953125, 0.251953125, 0.251953125, 0.2484130859375, 0.24169921875, 0.2353515625, 0.2353515625, 0.2257080078125, 0.218505859375, 0.211669921875, 0.211669921875, 0.2086181640625, 0.2073974609375, 0.1976318359375, 0.1973876953125, 0.1973876953125, 0.1973876953125, 0.1973876953125, 0.1973876953125, 0.1973876953125, 0.19287109375, 0.19287109375, 0.19287109375, 0.188720703125, 0.1796875, 0.177001953125, 0.1751708984375, 0.1632080078125, 0.1595458984375, 0.1578369140625, 0.151611328125, 0.1505126953125, 0.1505126953125, 0.1505126953125, 0.144775390625, 0.144775390625, 0.1385498046875, 0.1336669921875, 0.128662109375, 0.1280517578125, 0.1273193359375, 0.1273193359375, 0.1273193359375, 0.11724853515625, 0.1143798828125, 0.1143798828125, 0.11328125, 0.1063232421875, 0.1004638671875, 0.1004638671875, 0.09478759765625, 0.08697509765625, 0.0841064453125, 0.0841064453125, 0.079833984375, 0.0762939453125, 0.0738525390625, 0.072021484375, 0.070556640625]}, "2": {"indices": [5, 32, 44, 43, 4, 3, 18, 55, 37, 38, 39, 31, 33, 8, 56, 6, 30, 7, 41, 42, 35, 36, 54, 34, 15, 9, 74, 156, 22, 23, 12, 13, 14, 49, 48, 88, 89, 90, 91, 92, 96, 97, 29, 26, 24, 25, 16, 10, 84, 86, 87, 75, 17, 94, 95, 157, 158, 159, 160, 151, 76, 155, 85, 40, 141, 93, 131, 46, 98, 53, 146, 51, 52, 21, 153, 154, 2, 0, 20, 19, 69, 70, 148, 149, 150, 140, 71, 72, 138, 50, 143, 137, 127, 132, 133, 47, 80, 81, 147, 45], "values": [0.75634765625, 0.6962890625, 0.6572265625, 0.65185546875, 0.6318359375, 0.6298828125, 0.6298828125, 0.61474609375, 0.587890625, 0.587890625, 0.587890625, 0.5771484375, 0.5771484375, 0.5400390625, 0.52001953125, 0.50439453125, 0.456298828125, 0.44384765625, 0.44384765625, 0.44384765625, 0.425537109375, 0.425537109375, 0.425537109375, 0.404296875, 0.379150390625, 0.34814453125, 0.311767578125, 0.29833984375, 0.284423828125, 0.284423828125, 0.2841796875, 0.2841796875, 0.2841796875, 0.275634765625, 0.2666015625, 0.264404296875, 0.264404296875, 0.264404296875, 0.264404296875, 0.264404296875, 0.264404296875, 0.264404296875, 0.259033203125, 0.257568359375, 0.25634765625, 0.25634765625, 0.252685546875, 0.250244140625, 0.236572265625, 0.2305908203125, 0.2305908203125, 0.2283935546875, 0.216552734375, 0.2078857421875, 0.2078857421875, 0.2069091796875, 0.2069091796875, 0.2069091796875, 0.2069091796875, 0.20361328125, 0.198974609375, 0.18994140625, 0.1884765625, 0.1856689453125, 0.1800537109375, 0.176025390625, 0.169677734375, 0.169189453125, 0.1619873046875, 0.150390625, 0.1427001953125, 0.136474609375, 0.136474609375, 0.1317138671875, 0.129638671875, 0.129638671875, 0.1259765625, 0.1240234375, 0.1236572265625, 0.11920166015625, 0.11566162109375, 0.11566162109375, 0.10845947265625, 0.10845947265625, 0.10845947265625, 0.106689453125, 0.10260009765625, 0.10260009765625, 0.0992431640625, 0.0982666015625, 0.09735107421875, 0.09063720703125, 0.08319091796875, 0.08319091796875, 0.08319091796875, 0.0816650390625, 0.07452392578125, 0.07452392578125, 0.0731201171875, 0.06549072265625]}, "3": {"indices": [66, 447, 420, 449, 450, 64, 149, 444, 448, 200, 204, 268, 442, 298, 325, 326, 89, 311, 4083, 46, 50, 65, 67, 775, 1014, 84, 3871, 4073, 68, 76, 85, 86, 312, 313, 526, 709, 1142, 4067, 4068, 4383, 2134, 2160, 8, 524, 3100, 3268, 3269, 3270, 3271, 1015, 2887, 1499, 3236, 2406, 2407, 851, 852, 853, 4380, 2702, 451, 456, 391, 3874, 107, 90, 525, 397, 4326, 4352, 4777, 4778, 4779, 88, 100, 55, 56, 58, 1506, 1507, 4228, 148, 209, 870, 4146, 4200, 4193, 682, 782, 804, 708, 57, 2685, 4087, 4088, 4859, 91, 2948, 428, 429], "values": [0.998046875, 0.998046875, 0.966796875, 0.966796875, 0.966796875, 0.84375, 0.84375, 0.84375, 0.84375, 0.8310546875, 0.8310546875, 0.8310546875, 0.8310546875, 0.83056640625, 0.83056640625, 0.83056640625, 0.81298828125, 0.7431640625, 0.73046875, 0.716796875, 0.716796875, 0.716796875, 0.716796875, 0.716796875, 0.716796875, 0.70263671875, 0.701171875, 0.70068359375, 0.67822265625, 0.67822265625, 0.67822265625, 0.67822265625, 0.67822265625, 0.67822265625, 0.654296875, 0.654296875, 0.654296875, 0.6474609375, 0.6474609375, 0.6474609375, 0.64306640625, 0.64306640625, 0.6337890625, 0.6328125, 0.62451171875, 0.62451171875, 0.62451171875, 0.62451171875, 0.62451171875, 0.623046875, 0.6083984375, 0.6064453125, 0.60302734375, 0.60205078125, 0.60205078125, 0.599609375, 0.599609375, 0.599609375, 0.59619140625, 0.58984375, 0.5888671875, 0.5888671875, 0.58203125, 0.5810546875, 0.57958984375, 0.57373046875, 0.57373046875, 0.5732421875, 0.568359375, 0.568359375, 0.56591796875, 0.56591796875, 0.56591796875, 0.56396484375, 0.56396484375, 0.56103515625, 0.56103515625, 0.56103515625, 0.55908203125, 0.55908203125, 0.55810546875, 0.5556640625, 0.5556640625, 0.55224609375, 0.5517578125, 0.5517578125, 0.55126953125, 0.54736328125, 0.54736328125, 0.54736328125, 0.54443359375, 0.5439453125, 0.5400390625, 0.5400390625, 0.5400390625, 0.53369140625, 0.53125, 0.52978515625, 0.52734375, 0.52734375]}, "4": {"indices": [225, 234, 212, 120, 121, 124, 126, 127, 56, 57, 91, 181, 232, 92, 163, 179, 178, 209, 135, 142, 129, 233, 158, 17, 44, 239, 240, 107, 116, 117, 118, 119, 122, 109, 206, 177, 249, 250, 61, 110, 111, 132, 146, 147, 180, 152, 153, 60, 94, 104, 108, 113, 128, 130, 141, 148, 176, 157, 70, 72, 100, 7, 34, 45, 11, 53, 5, 36, 52, 238, 213, 230, 236, 237, 1, 2, 159, 160, 14, 220, 246, 54, 221, 247, 99, 101, 115, 93, 69, 187, 207, 79, 19, 16, 46, 67, 74, 78, 82, 86], "values": [0.693359375, 0.693359375, 0.68408203125, 0.634765625, 0.634765625, 0.634765625, 0.634765625, 0.634765625, 0.6220703125, 0.6220703125, 0.6220703125, 0.6220703125, 0.61328125, 0.5615234375, 0.5234375, 0.5234375, 0.51171875, 0.50537109375, 0.49658203125, 0.4921875, 0.48583984375, 0.484130859375, 0.478515625, 0.475341796875, 0.4677734375, 0.4560546875, 0.4560546875, 0.4453125, 0.4453125, 0.4453125, 0.4453125, 0.4453125, 0.4404296875, 0.43994140625, 0.43310546875, 0.431884765625, 0.42431640625, 0.42431640625, 0.423828125, 0.423828125, 0.423828125, 0.423828125, 0.423828125, 0.423828125, 0.41552734375, 0.4140625, 0.4140625, 0.412109375, 0.410400390625, 0.410400390625, 0.410400390625, 0.410400390625, 0.410400390625, 0.410400390625, 0.410400390625, 0.410400390625, 0.40869140625, 0.40673828125, 0.406005859375, 0.406005859375, 0.40576171875, 0.40478515625, 0.40478515625, 0.40234375, 0.398681640625, 0.398193359375, 0.397705078125, 0.390869140625, 0.386962890625, 0.38330078125, 0.38037109375, 0.3798828125, 0.374267578125, 0.373046875, 0.364501953125, 0.364501953125, 0.354248046875, 0.354248046875, 0.348388671875, 0.341796875, 0.336669921875, 0.33349609375, 0.332763671875, 0.330810546875, 0.328369140625, 0.328369140625, 0.328369140625, 0.32763671875, 0.31591796875, 0.315185546875, 0.311279296875, 0.307861328125, 0.306884765625, 0.306396484375, 0.306396484375, 0.306396484375, 0.306396484375, 0.306396484375, 0.306396484375, 0.306396484375]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4022, 4271, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 4021, 4023, 4024, 2336, 1074, 1111, 1153, 1171, 4335, 4852, 4504, 4222, 4624, 4306, 4341, 4342, 4343, 4616, 441, 2368, 2359, 1370, 2868, 2928, 2936, 3417, 3687, 3695, 3699, 3700, 4177, 2766, 2797, 2834, 2843, 4977, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 4577, 4340, 4757, 4775, 4500, 3585, 933, 4850, 3223, 3751, 3570, 3573, 3591, 4716, 4735, 4758, 4759, 4773, 4790, 2920, 4250, 3692, 3696, 3858, 3861], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.9560546875, 0.78466796875, 0.78466796875, 0.7802734375, 0.6875, 0.66943359375, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.66357421875, 0.63330078125, 0.63330078125, 0.63330078125, 0.61279296875, 0.6123046875, 0.6123046875, 0.6123046875, 0.6123046875, 0.60986328125, 0.609375, 0.6083984375, 0.60009765625, 0.60009765625, 0.5986328125, 0.5908203125, 0.5908203125, 0.5908203125, 0.587890625, 0.58740234375, 0.5849609375, 0.58447265625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.58056640625, 0.572265625, 0.572265625, 0.572265625, 0.572265625, 0.57080078125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.5703125, 0.56787109375, 0.56396484375, 0.56005859375, 0.56005859375, 0.552734375, 0.54345703125, 0.5419921875, 0.5390625, 0.53515625, 0.53515625, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.52880859375, 0.5283203125, 0.52734375, 0.51953125, 0.51904296875, 0.51904296875, 0.513671875, 0.513671875]}, "6": {"indices": [385, 341, 244, 245, 247, 248, 249, 928, 386, 1152, 246, 3337, 3161, 1115, 216, 1602, 1603, 428, 429, 430, 217, 3575, 3576, 3580, 3581, 250, 52, 63, 64, 70, 5, 3598, 1097, 1141, 1161, 344, 3959, 2892, 3170, 3171, 3172, 3174, 3182, 3183, 3234, 3235, 3332, 1811, 188, 390, 927, 76, 77, 1187, 23, 935, 936, 929, 2868, 2598, 2446, 176, 703, 716, 2861, 2815, 4414, 1028, 1868, 218, 48, 628, 879, 882, 4195, 4196, 4198, 4199, 632, 3322, 3323, 2590, 2593, 2594, 2595, 3593, 1810, 3578, 1181, 84, 582, 343, 587, 961, 962, 4592, 693, 694, 696, 697], "values": [0.71630859375, 0.6923828125, 0.6630859375, 0.6630859375, 0.6630859375, 0.6630859375, 0.6630859375, 0.64453125, 0.62158203125, 0.5380859375, 0.52490234375, 0.4990234375, 0.49267578125, 0.487060546875, 0.470703125, 0.46875, 0.46875, 0.4482421875, 0.4482421875, 0.4482421875, 0.445068359375, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.43408203125, 0.433837890625, 0.433837890625, 0.433837890625, 0.433837890625, 0.431640625, 0.42236328125, 0.41650390625, 0.41650390625, 0.41650390625, 0.411865234375, 0.41162109375, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.407470703125, 0.406982421875, 0.405029296875, 0.402587890625, 0.400146484375, 0.400146484375, 0.400146484375, 0.396728515625, 0.3955078125, 0.3955078125, 0.39453125, 0.39306640625, 0.39208984375, 0.387451171875, 0.384765625, 0.384033203125, 0.384033203125, 0.383056640625, 0.3828125, 0.3818359375, 0.381103515625, 0.381103515625, 0.380615234375, 0.379638671875, 0.379638671875, 0.379638671875, 0.379638671875, 0.37890625, 0.37890625, 0.37890625, 0.37890625, 0.37353515625, 0.37109375, 0.37109375, 0.36767578125, 0.36767578125, 0.36767578125, 0.36767578125, 0.365966796875, 0.36474609375, 0.364013671875, 0.363525390625, 0.3603515625, 0.35791015625, 0.352294921875, 0.352294921875, 0.352294921875, 0.352294921875, 0.352294921875, 0.351806640625, 0.351806640625, 0.351806640625, 0.351806640625]}, "7": {"indices": [54, 27, 31, 34, 32, 143, 53, 33, 66, 56, 61, 62, 19, 35, 63, 55, 49, 124, 227, 50, 47, 48, 26, 28, 29, 118, 51, 57, 119, 88, 60, 132, 133, 10, 46, 74, 75, 76, 77, 204, 83, 109, 157, 158, 159, 160, 52, 97, 192, 9, 45, 59, 175, 111, 1, 213, 72, 178, 179, 180, 181, 182, 183, 30, 73, 219, 220, 221, 230, 18, 170, 172, 173, 44, 113, 123, 125, 128, 114, 117, 214, 121, 217, 58, 120, 64, 194, 200, 116, 78, 130, 138, 25, 186, 232, 185, 13, 233, 0, 212], "values": [0.8427734375, 0.6708984375, 0.6103515625, 0.6103515625, 0.59033203125, 0.5703125, 0.53857421875, 0.5361328125, 0.5302734375, 0.5078125, 0.5078125, 0.5078125, 0.47802734375, 0.474853515625, 0.46923828125, 0.462890625, 0.440185546875, 0.43994140625, 0.432373046875, 0.423828125, 0.40576171875, 0.40576171875, 0.401611328125, 0.400146484375, 0.389892578125, 0.386474609375, 0.378173828125, 0.363525390625, 0.36328125, 0.35986328125, 0.355712890625, 0.353271484375, 0.353271484375, 0.344970703125, 0.337646484375, 0.33349609375, 0.33349609375, 0.33349609375, 0.33349609375, 0.331298828125, 0.328857421875, 0.328369140625, 0.322998046875, 0.322998046875, 0.322998046875, 0.322998046875, 0.3212890625, 0.318115234375, 0.3173828125, 0.315185546875, 0.30810546875, 0.299072265625, 0.29150390625, 0.28955078125, 0.289306640625, 0.286865234375, 0.28271484375, 0.279052734375, 0.279052734375, 0.279052734375, 0.279052734375, 0.279052734375, 0.279052734375, 0.274169921875, 0.27001953125, 0.26318359375, 0.26318359375, 0.26318359375, 0.2587890625, 0.257080078125, 0.25390625, 0.25390625, 0.25390625, 0.2491455078125, 0.2464599609375, 0.245361328125, 0.245361328125, 0.2427978515625, 0.236083984375, 0.23095703125, 0.2249755859375, 0.2220458984375, 0.2216796875, 0.220947265625, 0.2200927734375, 0.21484375, 0.2086181640625, 0.199462890625, 0.197265625, 0.1962890625, 0.19384765625, 0.19384765625, 0.1885986328125, 0.1883544921875, 0.18603515625, 0.185302734375, 0.1827392578125, 0.182373046875, 0.177001953125, 0.1727294921875]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 640, 1377, 1600, 1601, 1602, 656, 650, 441, 442, 443, 161, 162, 554, 555, 159, 274, 277, 1973, 454, 1972, 451, 1959, 1810, 1195, 473, 474, 497, 498, 499, 174, 175, 501, 156, 1491, 1492, 923, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 157, 183, 2222, 177, 178, 1874, 1408, 2004, 1324, 1325, 1326, 1327, 680, 372, 2712, 1328, 1687, 418, 506, 507, 508, 565, 566, 567, 603, 649, 1078, 1079, 1203, 1204, 21, 1081, 1138, 1232, 1240, 494, 495, 496, 2013, 2022, 2041], "values": [0.916015625, 0.916015625, 0.8759765625, 0.8759765625, 0.8759765625, 0.8759765625, 0.8759765625, 0.8759765625, 0.8759765625, 0.87158203125, 0.81201171875, 0.77783203125, 0.7587890625, 0.74853515625, 0.66845703125, 0.66845703125, 0.66845703125, 0.65966796875, 0.63916015625, 0.6376953125, 0.6376953125, 0.6376953125, 0.625, 0.625, 0.61181640625, 0.61181640625, 0.59130859375, 0.58447265625, 0.58447265625, 0.5791015625, 0.5556640625, 0.54443359375, 0.52734375, 0.51904296875, 0.51806640625, 0.51611328125, 0.5, 0.5, 0.5, 0.5, 0.5, 0.499755859375, 0.499755859375, 0.49853515625, 0.4921875, 0.4921875, 0.4921875, 0.48876953125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.48828125, 0.488037109375, 0.488037109375, 0.4873046875, 0.482666015625, 0.482666015625, 0.470947265625, 0.452392578125, 0.452392578125, 0.45068359375, 0.45068359375, 0.45068359375, 0.45068359375, 0.44677734375, 0.44580078125, 0.4453125, 0.445068359375, 0.4443359375, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.444091796875, 0.44140625, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.431640625, 0.431640625, 0.431640625, 0.4306640625, 0.4306640625, 0.4306640625]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2919, 2839, 2840, 3631, 2475, 2941, 2944, 752, 283, 2473, 2474, 212, 213, 220, 221, 687, 751, 4204, 286, 1027, 422, 174, 278, 873, 827, 835, 4165, 4383, 828, 834, 1075, 1076, 1077, 1339, 4488, 630, 2957, 1790, 2448, 2916, 4533, 4434, 4435, 4349, 4350, 4376, 755, 756, 2018, 4507, 866, 867, 4348, 4351, 796, 2481, 772, 277, 1337, 2431, 2476, 2803, 2961, 262, 624, 753, 754, 905, 749, 904, 189, 1979, 1031, 2195, 2308, 4676, 1944, 260, 263, 264, 3389, 3630, 36, 1457, 2225, 872, 1902, 2772, 4753, 4374, 258, 628, 3036, 3037, 173], "values": [0.99755859375, 0.89697265625, 0.86181640625, 0.8076171875, 0.8076171875, 0.6708984375, 0.66796875, 0.66796875, 0.65185546875, 0.64306640625, 0.64306640625, 0.64306640625, 0.62939453125, 0.62646484375, 0.62646484375, 0.62646484375, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.6064453125, 0.58154296875, 0.57373046875, 0.5732421875, 0.56689453125, 0.56640625, 0.56640625, 0.5634765625, 0.55712890625, 0.55712890625, 0.54736328125, 0.54052734375, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.52978515625, 0.529296875, 0.529296875, 0.51220703125, 0.51220703125, 0.51220703125, 0.51171875, 0.5107421875, 0.5107421875, 0.50927734375, 0.50927734375, 0.50927734375, 0.5078125, 0.5078125, 0.50634765625, 0.4951171875, 0.49072265625, 0.49072265625, 0.490478515625, 0.490478515625, 0.486572265625, 0.477294921875, 0.474853515625, 0.4716796875, 0.46435546875, 0.46337890625, 0.461181640625, 0.461181640625, 0.461181640625, 0.458984375, 0.453369140625, 0.453369140625, 0.453369140625, 0.453369140625, 0.449462890625, 0.449462890625, 0.443603515625, 0.439453125, 0.43798828125, 0.43798828125, 0.43798828125, 0.43798828125, 0.429443359375, 0.419921875, 0.419921875, 0.419921875, 0.417236328125, 0.4140625, 0.4111328125, 0.409423828125, 0.404296875, 0.403564453125, 0.403564453125, 0.4013671875, 0.401123046875, 0.3984375, 0.397216796875, 0.396484375, 0.3955078125, 0.3955078125, 0.395263671875]}, "10": {"indices": [262, 267, 279, 462, 1346, 1665, 1039, 691, 1286, 1668, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 1656, 273, 277, 278, 1653, 1654, 1666, 648, 754, 794, 1521, 1553, 1565, 1192, 1333, 1340, 1697, 1699, 1702, 501, 730, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 679, 685, 792, 1671, 1673, 502, 795, 884, 1445, 570, 1472, 1524, 1548, 1691, 1669, 1751, 1335, 506, 520, 1672, 1424, 524, 1621, 340, 1677, 209, 218, 482, 1344, 123, 334, 210, 1221, 1252, 1279, 1283, 212, 513, 763], "values": [0.998046875, 0.998046875, 0.998046875, 0.8994140625, 0.8994140625, 0.8994140625, 0.890625, 0.8828125, 0.8828125, 0.8828125, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.88232421875, 0.86669921875, 0.8662109375, 0.8662109375, 0.8662109375, 0.86572265625, 0.86572265625, 0.86572265625, 0.86083984375, 0.86083984375, 0.86083984375, 0.86083984375, 0.86083984375, 0.86083984375, 0.849609375, 0.84716796875, 0.84716796875, 0.84716796875, 0.84716796875, 0.84716796875, 0.84619140625, 0.84619140625, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.841796875, 0.84130859375, 0.84130859375, 0.84130859375, 0.837890625, 0.837890625, 0.8349609375, 0.8349609375, 0.8349609375, 0.8349609375, 0.833984375, 0.8310546875, 0.82470703125, 0.82470703125, 0.8232421875, 0.82275390625, 0.82275390625, 0.8212890625, 0.8193359375, 0.8193359375, 0.818359375, 0.81689453125, 0.8134765625, 0.8134765625, 0.8095703125, 0.80322265625, 0.80078125, 0.80078125, 0.80078125, 0.7998046875, 0.79931640625, 0.79833984375, 0.796875, 0.79638671875, 0.79638671875, 0.79638671875, 0.79638671875, 0.79345703125, 0.79345703125, 0.7919921875]}, "11": {"indices": [457, 1613, 553, 557, 558, 577, 587, 761, 1060, 478, 1651, 1685, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 545, 945, 946, 953, 1533, 1338, 1012, 1189, 514, 1040, 1571, 1745, 1779, 480, 1580, 1615, 484, 493, 494, 495, 499, 500, 507, 521, 550, 552, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1602, 829, 490, 519, 965, 1378, 1205, 511, 571, 1734, 1271, 1272, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 141, 35, 43, 383, 1718, 32, 101, 102, 124, 125, 126, 562, 139, 140, 147, 162, 526, 1579, 1720, 1730, 1731, 1752], "values": [0.998046875, 0.97119140625, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.94775390625, 0.94775390625, 0.94287109375, 0.9287109375, 0.9287109375, 0.9287109375, 0.92578125, 0.92578125, 0.92578125, 0.92578125, 0.92236328125, 0.9072265625, 0.9072265625, 0.9033203125, 0.89990234375, 0.89990234375, 0.89501953125, 0.8935546875, 0.88720703125, 0.88623046875, 0.884765625, 0.8671875, 0.8671875, 0.8671875, 0.8671875, 0.8671875, 0.85595703125, 0.85595703125, 0.85595703125, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.85400390625, 0.84521484375, 0.8447265625, 0.8447265625, 0.8447265625, 0.8349609375, 0.82958984375, 0.826171875, 0.82373046875, 0.822265625, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.818359375, 0.81689453125, 0.81396484375, 0.81396484375, 0.81396484375, 0.81396484375, 0.81298828125, 0.81298828125, 0.81298828125, 0.81298828125, 0.81298828125, 0.81298828125, 0.81201171875, 0.80615234375, 0.80615234375, 0.80615234375, 0.802734375, 0.802734375, 0.802734375, 0.802734375, 0.802734375, 0.802734375, 0.802734375]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 189, 173, 187, 304, 214, 334, 365, 366, 80, 74, 77, 284, 237, 20, 227, 132, 203, 37, 180, 243, 364, 150, 276, 277, 278, 281, 362, 130, 200, 47, 110, 196, 118, 72, 391, 392, 393, 394, 395, 396, 151, 213, 381, 382, 215, 380, 181, 282, 66, 116, 117, 354, 355, 6, 34, 35, 36, 326, 190, 68, 60, 251, 359, 43, 254, 157, 158, 63, 144, 48, 179, 100, 156, 121, 123, 124, 106, 112, 147, 153, 191, 61, 58, 32, 160, 161, 178, 316, 102, 115], "values": [0.82763671875, 0.82763671875, 0.82763671875, 0.6611328125, 0.6611328125, 0.6611328125, 0.65478515625, 0.65478515625, 0.50390625, 0.490478515625, 0.470947265625, 0.447998046875, 0.44091796875, 0.439453125, 0.4306640625, 0.42724609375, 0.42724609375, 0.42724609375, 0.409912109375, 0.40966796875, 0.40966796875, 0.40283203125, 0.380859375, 0.374755859375, 0.37353515625, 0.369873046875, 0.36474609375, 0.352783203125, 0.346923828125, 0.342529296875, 0.33984375, 0.333740234375, 0.33154296875, 0.33154296875, 0.33154296875, 0.33154296875, 0.330078125, 0.329345703125, 0.329345703125, 0.328857421875, 0.327880859375, 0.323974609375, 0.3232421875, 0.319091796875, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.31640625, 0.312744140625, 0.312744140625, 0.309814453125, 0.309814453125, 0.3095703125, 0.308837890625, 0.30859375, 0.3076171875, 0.306640625, 0.30517578125, 0.30517578125, 0.299072265625, 0.299072265625, 0.29736328125, 0.29736328125, 0.29736328125, 0.29736328125, 0.29248046875, 0.29150390625, 0.2890625, 0.287109375, 0.286376953125, 0.282958984375, 0.280517578125, 0.2802734375, 0.27880859375, 0.27880859375, 0.27490234375, 0.2724609375, 0.26953125, 0.266357421875, 0.264404296875, 0.264404296875, 0.263427734375, 0.263427734375, 0.263427734375, 0.26171875, 0.26171875, 0.26171875, 0.26171875, 0.26171875, 0.25927734375, 0.25732421875, 0.256591796875, 0.256591796875, 0.256591796875, 0.256591796875, 0.25390625, 0.25244140625, 0.2469482421875]}, "13": {"indices": [275, 274, 273, 272, 276, 537, 513, 277, 578, 573, 574, 529, 530, 2836, 2478, 575, 576, 498, 2832, 2833, 1864, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 229, 230, 607, 2875, 293, 531, 1381, 2373, 442, 298, 602, 296, 2525, 279, 526, 1067, 1070, 533, 2458, 348, 349, 350, 363, 365, 370, 371, 1990, 1991, 1992, 291, 1164, 716, 717, 2882, 2884, 2885, 2838, 270, 2259, 1775, 1776, 254, 3040, 528, 2843, 1026, 2611, 233, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 2568, 2569, 2406, 2407, 1642, 1643], "values": [0.63818359375, 0.5859375, 0.55517578125, 0.55419921875, 0.5517578125, 0.5458984375, 0.54443359375, 0.54150390625, 0.5263671875, 0.50439453125, 0.50439453125, 0.501953125, 0.501953125, 0.47802734375, 0.475830078125, 0.471923828125, 0.471923828125, 0.469970703125, 0.463134765625, 0.463134765625, 0.460693359375, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.447998046875, 0.447998046875, 0.447509765625, 0.445556640625, 0.435302734375, 0.4306640625, 0.429931640625, 0.424560546875, 0.4169921875, 0.416748046875, 0.41650390625, 0.413330078125, 0.407470703125, 0.4072265625, 0.406982421875, 0.406982421875, 0.406982421875, 0.406494140625, 0.400146484375, 0.398681640625, 0.398681640625, 0.398681640625, 0.398681640625, 0.398681640625, 0.398681640625, 0.398681640625, 0.3974609375, 0.3974609375, 0.3974609375, 0.397216796875, 0.396240234375, 0.395263671875, 0.395263671875, 0.393310546875, 0.393310546875, 0.393310546875, 0.39111328125, 0.389892578125, 0.38623046875, 0.3857421875, 0.3857421875, 0.384521484375, 0.382080078125, 0.3818359375, 0.3818359375, 0.380859375, 0.380126953125, 0.379150390625, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.37841796875, 0.376953125, 0.376953125, 0.374755859375, 0.374755859375, 0.37451171875, 0.37451171875]}, "14": {"indices": [291, 2328, 484, 485, 293, 2397, 2404, 273, 271, 272, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 2912, 495, 2886, 292, 2568, 2569, 557, 2339, 2472, 1598, 506, 2521, 1063, 1064, 1065, 298, 748, 284, 285, 286, 287, 288, 304, 303, 575, 576, 2523, 750, 458, 305, 901, 2525, 296, 300, 2402, 2403, 2163, 302, 294, 295, 489, 743, 744, 745, 746, 420, 269, 747, 749, 539, 280, 281, 477, 478, 957, 2008, 2009, 2010, 314, 2111, 2112, 789, 297, 2366, 504, 275, 1117, 229, 230, 290, 2122, 770, 2687, 2688, 474, 703, 279, 2416, 317, 636, 2372], "values": [0.63427734375, 0.61474609375, 0.61083984375, 0.61083984375, 0.60107421875, 0.5947265625, 0.59228515625, 0.58984375, 0.57080078125, 0.5615234375, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.53955078125, 0.53955078125, 0.53955078125, 0.5244140625, 0.50146484375, 0.497802734375, 0.49560546875, 0.494140625, 0.494140625, 0.493896484375, 0.487548828125, 0.486572265625, 0.4853515625, 0.482177734375, 0.480224609375, 0.4794921875, 0.4794921875, 0.4794921875, 0.475341796875, 0.47119140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.458251953125, 0.457763671875, 0.454833984375, 0.454833984375, 0.452880859375, 0.45166015625, 0.4501953125, 0.449951171875, 0.44677734375, 0.446533203125, 0.442626953125, 0.4365234375, 0.43359375, 0.43359375, 0.433349609375, 0.423828125, 0.423095703125, 0.423095703125, 0.4189453125, 0.41748046875, 0.41748046875, 0.41748046875, 0.41748046875, 0.4169921875, 0.415283203125, 0.4150390625, 0.4150390625, 0.4130859375, 0.412109375, 0.412109375, 0.411865234375, 0.411865234375, 0.409912109375, 0.406982421875, 0.406982421875, 0.406982421875, 0.406005859375, 0.406005859375, 0.406005859375, 0.40283203125, 0.398681640625, 0.394775390625, 0.393798828125, 0.391357421875, 0.3896484375, 0.388427734375, 0.388427734375, 0.38720703125, 0.3857421875, 0.383056640625, 0.3828125, 0.3828125, 0.38232421875, 0.377197265625, 0.37451171875, 0.370849609375, 0.3701171875, 0.3671875, 0.3671875]}, "15": {"indices": [570, 483, 2770, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 2406, 2407, 1378, 1380, 255, 260, 261, 1599, 1600, 475, 486, 1041, 1052, 1117, 773, 789, 923, 742, 2199, 2215, 2216, 2217, 191, 192, 1124, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 2369, 1208, 1294, 1295, 1333, 259, 927, 928, 2157, 361, 422, 2712, 2065, 1813, 461, 524, 525, 366, 926, 566, 572, 1181, 1182, 1183, 2564, 2625, 1029, 1838, 1857, 1265, 1266, 1281, 2866, 2520, 2332, 1661, 431, 442, 1071, 1072, 1704, 1763, 1764, 1765, 1766, 1767, 2777, 1545, 1602, 1907, 1462, 1463, 1464, 1465, 1466, 1403], "values": [0.6875, 0.61865234375, 0.6103515625, 0.53369140625, 0.509765625, 0.509765625, 0.509765625, 0.509765625, 0.509765625, 0.509765625, 0.509765625, 0.494873046875, 0.47802734375, 0.47802734375, 0.46826171875, 0.46826171875, 0.451904296875, 0.451904296875, 0.451904296875, 0.447998046875, 0.447998046875, 0.443115234375, 0.443115234375, 0.43994140625, 0.43994140625, 0.429931640625, 0.427490234375, 0.425048828125, 0.4228515625, 0.419921875, 0.41796875, 0.41796875, 0.41796875, 0.41796875, 0.414306640625, 0.414306640625, 0.4130859375, 0.41162109375, 0.41162109375, 0.41162109375, 0.41162109375, 0.41162109375, 0.41162109375, 0.41162109375, 0.40966796875, 0.4091796875, 0.40869140625, 0.40869140625, 0.40869140625, 0.408447265625, 0.40625, 0.40625, 0.40625, 0.40576171875, 0.40576171875, 0.40576171875, 0.405029296875, 0.40283203125, 0.402587890625, 0.402587890625, 0.402587890625, 0.401123046875, 0.40087890625, 0.39990234375, 0.39990234375, 0.39892578125, 0.39892578125, 0.39892578125, 0.396240234375, 0.396240234375, 0.39453125, 0.39404296875, 0.39404296875, 0.39306640625, 0.39306640625, 0.39306640625, 0.389892578125, 0.3896484375, 0.386962890625, 0.3857421875, 0.381591796875, 0.380126953125, 0.377685546875, 0.377685546875, 0.376220703125, 0.376220703125, 0.376220703125, 0.376220703125, 0.376220703125, 0.376220703125, 0.3740234375, 0.367431640625, 0.36572265625, 0.363525390625, 0.362060546875, 0.362060546875, 0.362060546875, 0.362060546875, 0.362060546875, 0.360107421875]}, "16": {"indices": [380, 3432, 202, 3343, 3347, 4445, 2658, 2884, 2885, 2890, 2892, 1879, 1880, 4887, 3464, 1883, 1885, 1888, 1889, 3909, 852, 3337, 3970, 3155, 793, 991, 3975, 3977, 3978, 4662, 4669, 1895, 3972, 3973, 3899, 3338, 3340, 3979, 3900, 1882, 1890, 1891, 891, 33, 43, 2020, 4169, 4551, 4595, 4660, 4663, 2480, 2484, 111, 112, 113, 114, 364, 1827, 3334, 1881, 1832, 3966, 4865, 4609, 990, 3235, 359, 2886, 3145, 1016, 1972, 3976, 1860, 4171, 4172, 1903, 795, 796, 800, 3778, 2389, 1062, 1141, 1142, 2016, 4425, 945, 785, 786, 4531, 3149, 1923, 1925, 881, 994, 2536, 4854, 1317, 2593], "values": [0.8896484375, 0.6552734375, 0.64697265625, 0.62890625, 0.62890625, 0.5927734375, 0.58154296875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.572265625, 0.572265625, 0.56298828125, 0.55126953125, 0.55078125, 0.55078125, 0.55078125, 0.55078125, 0.55029296875, 0.54638671875, 0.5458984375, 0.5458984375, 0.54296875, 0.5419921875, 0.5419921875, 0.54150390625, 0.54150390625, 0.54150390625, 0.5400390625, 0.53955078125, 0.53662109375, 0.5322265625, 0.5322265625, 0.5302734375, 0.52685546875, 0.52685546875, 0.52685546875, 0.52490234375, 0.5244140625, 0.5244140625, 0.5244140625, 0.521484375, 0.5205078125, 0.5205078125, 0.5205078125, 0.517578125, 0.51708984375, 0.51708984375, 0.51708984375, 0.51708984375, 0.51611328125, 0.51611328125, 0.51171875, 0.51171875, 0.51171875, 0.51171875, 0.51171875, 0.51123046875, 0.51123046875, 0.50927734375, 0.50537109375, 0.50537109375, 0.5048828125, 0.49853515625, 0.498291015625, 0.496826171875, 0.493896484375, 0.49365234375, 0.489990234375, 0.48974609375, 0.48486328125, 0.482421875, 0.4814453125, 0.478515625, 0.478515625, 0.475341796875, 0.474609375, 0.474609375, 0.474609375, 0.47314453125, 0.472412109375, 0.46875, 0.46533203125, 0.46533203125, 0.462890625, 0.461669921875, 0.46142578125, 0.461181640625, 0.461181640625, 0.459228515625, 0.458984375, 0.45751953125, 0.45751953125, 0.457275390625, 0.456787109375, 0.454833984375, 0.4541015625, 0.453125, 0.45263671875]}, "17": {"indices": [29, 24, 18, 8, 10, 22, 6, 7, 19, 20, 31, 25, 11, 12, 32, 21, 27, 23, 26, 28, 3, 9, 34, 35, 4, 1, 2, 5, 30, 0, 33, 13, 14, 15, 16, 17], "values": [0.50244140625, 0.492919921875, 0.469482421875, 0.45263671875, 0.45263671875, 0.429931640625, 0.4208984375, 0.4208984375, 0.4208984375, 0.4208984375, 0.416748046875, 0.39794921875, 0.3916015625, 0.3916015625, 0.374267578125, 0.366455078125, 0.325439453125, 0.319580078125, 0.3037109375, 0.298828125, 0.27734375, 0.274658203125, 0.27001953125, 0.27001953125, 0.267578125, 0.2254638671875, 0.2254638671875, 0.199951171875, 0.1666259765625, 0.137939453125, 0.05291748046875, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 24, 22, 26, 6, 7, 19, 20, 8, 10, 21, 31, 30, 28, 27, 23, 9, 29, 1, 2, 25, 5, 4, 34, 35, 11, 12, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.70654296875, 0.63330078125, 0.62255859375, 0.60693359375, 0.568359375, 0.568359375, 0.568359375, 0.568359375, 0.55859375, 0.55859375, 0.5341796875, 0.51611328125, 0.51416015625, 0.5107421875, 0.48486328125, 0.483642578125, 0.47509765625, 0.451171875, 0.42919921875, 0.42919921875, 0.42626953125, 0.415283203125, 0.40576171875, 0.384521484375, 0.384521484375, 0.37939453125, 0.37939453125, 0.32373046875, 0.29541015625, 0.256103515625, 0.121337890625, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 165, 163, 53, 54, 88, 89, 113, 59, 61, 107, 197, 208, 121, 212, 9, 57, 45, 203, 213, 5, 217, 8, 65, 66, 64, 71, 177, 72, 210, 211, 60, 182, 174, 128, 192, 144, 145, 146, 48, 4, 6, 7, 228, 83, 122, 196, 191, 56, 82, 200, 0, 76, 16, 18, 245, 183, 91, 115, 67, 205, 84, 215, 207, 246, 114, 116, 201, 112, 214, 24, 124, 199, 25, 17, 216, 193, 235, 249, 209, 73, 135, 198, 105, 181, 126, 143, 204, 185, 236, 247, 237, 1, 240, 239, 202, 130], "values": [0.81591796875, 0.7939453125, 0.7939453125, 0.55126953125, 0.5498046875, 0.498779296875, 0.491943359375, 0.491943359375, 0.48681640625, 0.48681640625, 0.464111328125, 0.45361328125, 0.418701171875, 0.391845703125, 0.362060546875, 0.360595703125, 0.358154296875, 0.357421875, 0.34814453125, 0.33935546875, 0.32080078125, 0.304443359375, 0.297119140625, 0.2939453125, 0.283447265625, 0.28271484375, 0.274658203125, 0.274658203125, 0.272216796875, 0.2386474609375, 0.236328125, 0.2357177734375, 0.230224609375, 0.230224609375, 0.2266845703125, 0.226318359375, 0.21875, 0.2127685546875, 0.203125, 0.2012939453125, 0.2012939453125, 0.2012939453125, 0.188720703125, 0.1885986328125, 0.1885986328125, 0.1885986328125, 0.1865234375, 0.1854248046875, 0.173828125, 0.171875, 0.1656494140625, 0.1607666015625, 0.1607666015625, 0.1607666015625, 0.16015625, 0.157470703125, 0.1536865234375, 0.1536865234375, 0.1536865234375, 0.1534423828125, 0.1497802734375, 0.1492919921875, 0.147705078125, 0.1435546875, 0.1424560546875, 0.1300048828125, 0.12939453125, 0.12481689453125, 0.12152099609375, 0.12152099609375, 0.1197509765625, 0.1192626953125, 0.117919921875, 0.11529541015625, 0.11505126953125, 0.11431884765625, 0.11187744140625, 0.1065673828125, 0.10302734375, 0.09918212890625, 0.0987548828125, 0.098388671875, 0.096923828125, 0.09686279296875, 0.09686279296875, 0.09619140625, 0.09613037109375, 0.09539794921875, 0.08935546875, 0.08935546875, 0.08685302734375, 0.08477783203125, 0.081787109375, 0.0804443359375, 0.08038330078125, 0.0794677734375, 0.07586669921875, 0.06842041015625, 0.068115234375, 0.06463623046875]}}


import pandas as pd
for i in range(10):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])
    results = []
    for j in range(len(e2['values'])):
        results.append([e1['indices'][j], e1['values'][j], e2['indices'][j], e2['values'][j]])
    pd.DataFrame(results).to_csv(f'dp_request_v2_{i}.csv')


v1 =  {"0": {"indices": [395, 686, 434, 783, 785, 379, 397, 199, 396, 182, 21, 89, 180, 181, 207, 209, 787, 788, 789, 758, 778, 779, 203, 204, 627, 618, 620, 621, 622, 432, 791, 535, 153, 811, 86, 87, 796, 408, 409, 71, 110, 111, 634, 534, 465, 469, 486, 487, 488, 793, 626, 668, 795, 410, 152, 174, 433, 545, 200, 16, 768, 769, 205, 441, 161, 105, 172, 429, 413, 166, 167, 612, 40, 88, 866, 631, 633, 674, 385, 435, 669, 767, 399, 404, 405, 406, 407, 431, 154, 217, 895, 897, 898, 558, 673, 542, 411, 163, 400, 613], "values": [0.61328125, 0.59033203125, 0.5703125, 0.5498046875, 0.5498046875, 0.54833984375, 0.54833984375, 0.54052734375, 0.5400390625, 0.53125, 0.5166015625, 0.5166015625, 0.5166015625, 0.5166015625, 0.515625, 0.515625, 0.5009765625, 0.5009765625, 0.5009765625, 0.4951171875, 0.4951171875, 0.4951171875, 0.49267578125, 0.49267578125, 0.487060546875, 0.4833984375, 0.4833984375, 0.4833984375, 0.4833984375, 0.482666015625, 0.46728515625, 0.434814453125, 0.42578125, 0.424072265625, 0.423828125, 0.423828125, 0.421142578125, 0.41015625, 0.41015625, 0.40380859375, 0.40380859375, 0.40380859375, 0.4033203125, 0.402099609375, 0.383544921875, 0.383544921875, 0.383544921875, 0.383544921875, 0.383544921875, 0.378662109375, 0.375244140625, 0.375244140625, 0.37255859375, 0.362060546875, 0.357666015625, 0.35400390625, 0.349853515625, 0.348388671875, 0.344482421875, 0.33056640625, 0.33056640625, 0.33056640625, 0.328125, 0.32177734375, 0.32080078125, 0.31689453125, 0.31689453125, 0.31591796875, 0.312744140625, 0.312255859375, 0.312255859375, 0.312255859375, 0.311767578125, 0.311279296875, 0.309326171875, 0.308349609375, 0.308349609375, 0.30712890625, 0.29833984375, 0.29833984375, 0.297119140625, 0.297119140625, 0.296630859375, 0.296630859375, 0.296630859375, 0.296630859375, 0.296630859375, 0.295166015625, 0.294921875, 0.294921875, 0.289306640625, 0.289306640625, 0.289306640625, 0.2880859375, 0.28759765625, 0.284912109375, 0.283447265625, 0.2822265625, 0.2822265625, 0.2822265625]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 88, 89, 90, 91, 92, 96, 97, 59, 66, 67, 10, 21, 9, 139, 85, 48, 45, 61, 60, 53, 94, 95, 47, 62, 63, 137, 0, 140, 40, 138, 77, 78, 74, 76, 98, 157, 158, 159, 160, 80, 81, 58, 15, 142, 32, 153, 154, 93, 135, 106, 108, 109, 110, 111, 115, 57, 17, 107, 112, 114, 141, 28, 131, 27, 155, 24, 25, 82, 127, 132, 133, 35, 36, 54, 134, 125, 79, 124, 46, 2, 73, 22, 23, 51, 52, 143, 146, 31, 33, 19, 34, 148, 149, 150], "values": [0.61376953125, 0.587890625, 0.587890625, 0.52197265625, 0.50634765625, 0.50634765625, 0.486328125, 0.486328125, 0.486328125, 0.486328125, 0.486328125, 0.486328125, 0.486328125, 0.45263671875, 0.43896484375, 0.43896484375, 0.423583984375, 0.419677734375, 0.397216796875, 0.39208984375, 0.389404296875, 0.374267578125, 0.3515625, 0.349365234375, 0.34326171875, 0.336669921875, 0.3310546875, 0.3310546875, 0.32958984375, 0.3232421875, 0.3232421875, 0.306396484375, 0.299072265625, 0.288818359375, 0.279052734375, 0.275146484375, 0.27392578125, 0.27392578125, 0.265625, 0.26513671875, 0.26318359375, 0.25341796875, 0.25341796875, 0.25341796875, 0.25341796875, 0.250732421875, 0.250732421875, 0.248046875, 0.2386474609375, 0.2376708984375, 0.224365234375, 0.22265625, 0.22265625, 0.218994140625, 0.2086181640625, 0.2064208984375, 0.2064208984375, 0.2064208984375, 0.2064208984375, 0.2064208984375, 0.2064208984375, 0.1968994140625, 0.1953125, 0.1953125, 0.1953125, 0.1953125, 0.1865234375, 0.1856689453125, 0.1737060546875, 0.1676025390625, 0.1651611328125, 0.1578369140625, 0.1578369140625, 0.15576171875, 0.154541015625, 0.154541015625, 0.154541015625, 0.145751953125, 0.145751953125, 0.145751953125, 0.14501953125, 0.142578125, 0.13720703125, 0.1336669921875, 0.1331787109375, 0.13037109375, 0.125244140625, 0.124755859375, 0.124755859375, 0.12078857421875, 0.12078857421875, 0.10498046875, 0.103515625, 0.0953369140625, 0.0953369140625, 0.0914306640625, 0.0850830078125, 0.08355712890625, 0.08355712890625, 0.08355712890625]}, "2": {"indices": [5, 32, 44, 43, 4, 3, 18, 55, 37, 38, 39, 31, 33, 8, 56, 6, 30, 7, 41, 42, 35, 36, 54, 34, 15, 9, 74, 156, 48, 22, 23, 49, 88, 89, 90, 91, 92, 96, 97, 10, 12, 13, 14, 26, 24, 25, 29, 16, 86, 87, 84, 75, 76, 17, 94, 95, 157, 158, 159, 160, 151, 85, 155, 141, 40, 98, 93, 46, 131, 53, 146, 21, 19, 51, 52, 2, 153, 154, 0, 20, 140, 71, 72, 138, 69, 70, 143, 47, 50, 137, 148, 149, 150, 127, 132, 133, 80, 81, 45, 147], "values": [0.759765625, 0.69970703125, 0.66162109375, 0.65087890625, 0.63232421875, 0.6240234375, 0.6240234375, 0.62158203125, 0.58935546875, 0.58935546875, 0.58935546875, 0.57861328125, 0.57861328125, 0.54638671875, 0.517578125, 0.51025390625, 0.462646484375, 0.443603515625, 0.443603515625, 0.443603515625, 0.4326171875, 0.4326171875, 0.4326171875, 0.407958984375, 0.37890625, 0.3642578125, 0.3193359375, 0.304443359375, 0.292724609375, 0.29248046875, 0.29248046875, 0.285400390625, 0.2822265625, 0.2822265625, 0.2822265625, 0.2822265625, 0.2822265625, 0.2822265625, 0.2822265625, 0.27587890625, 0.271484375, 0.271484375, 0.271484375, 0.27001953125, 0.259033203125, 0.259033203125, 0.255615234375, 0.251953125, 0.2481689453125, 0.2481689453125, 0.246826171875, 0.2430419921875, 0.2236328125, 0.22021484375, 0.2174072265625, 0.2174072265625, 0.2080078125, 0.2080078125, 0.2080078125, 0.2080078125, 0.2056884765625, 0.205322265625, 0.1998291015625, 0.1971435546875, 0.195556640625, 0.1810302734375, 0.177978515625, 0.1778564453125, 0.1673583984375, 0.1588134765625, 0.156494140625, 0.15185546875, 0.1455078125, 0.1441650390625, 0.1441650390625, 0.1412353515625, 0.1405029296875, 0.1405029296875, 0.1319580078125, 0.12939453125, 0.12078857421875, 0.11871337890625, 0.11871337890625, 0.11688232421875, 0.11322021484375, 0.11322021484375, 0.10992431640625, 0.108154296875, 0.10455322265625, 0.10308837890625, 0.1024169921875, 0.1024169921875, 0.1024169921875, 0.099853515625, 0.099853515625, 0.099853515625, 0.09613037109375, 0.09613037109375, 0.09088134765625, 0.07769775390625]}, "3": {"indices": [66, 447, 420, 449, 450, 64, 149, 444, 448, 200, 204, 268, 298, 325, 326, 442, 89, 311, 4083, 46, 50, 65, 67, 775, 1014, 3871, 4073, 84, 68, 76, 85, 86, 312, 313, 526, 709, 1142, 4067, 4068, 4383, 2134, 2160, 524, 8, 1015, 3100, 3268, 3269, 3270, 3271, 2887, 1499, 851, 852, 853, 2406, 2407, 3236, 4380, 451, 456, 3874, 391, 2702, 90, 525, 107, 397, 4326, 4352, 1506, 1507, 4777, 4778, 4779, 88, 100, 209, 4146, 4193, 4200, 4228, 708, 55, 56, 58, 682, 782, 804, 148, 57, 870, 4087, 4088, 4859, 4151, 91, 776, 428, 429], "values": [0.99755859375, 0.99755859375, 0.96728515625, 0.96728515625, 0.96728515625, 0.84375, 0.84375, 0.84375, 0.84375, 0.83251953125, 0.83251953125, 0.83251953125, 0.83251953125, 0.83251953125, 0.83251953125, 0.83251953125, 0.81298828125, 0.7431640625, 0.73291015625, 0.71533203125, 0.71533203125, 0.71533203125, 0.71533203125, 0.71533203125, 0.71533203125, 0.70654296875, 0.705078125, 0.703125, 0.68017578125, 0.68017578125, 0.68017578125, 0.68017578125, 0.68017578125, 0.68017578125, 0.65869140625, 0.65869140625, 0.65869140625, 0.65380859375, 0.65380859375, 0.65380859375, 0.64990234375, 0.64990234375, 0.6396484375, 0.63623046875, 0.62939453125, 0.623046875, 0.623046875, 0.623046875, 0.623046875, 0.623046875, 0.615234375, 0.60791015625, 0.6044921875, 0.6044921875, 0.6044921875, 0.60205078125, 0.60205078125, 0.59716796875, 0.5908203125, 0.587890625, 0.587890625, 0.5869140625, 0.58349609375, 0.58349609375, 0.5791015625, 0.5791015625, 0.57568359375, 0.57568359375, 0.5703125, 0.5703125, 0.5693359375, 0.5693359375, 0.56884765625, 0.56884765625, 0.56884765625, 0.56689453125, 0.56689453125, 0.56396484375, 0.5625, 0.5625, 0.5625, 0.5615234375, 0.5576171875, 0.5556640625, 0.5556640625, 0.5556640625, 0.55517578125, 0.55517578125, 0.55517578125, 0.5537109375, 0.55126953125, 0.55029296875, 0.544921875, 0.544921875, 0.5419921875, 0.5380859375, 0.53564453125, 0.53173828125, 0.53076171875, 0.53076171875]}, "4": {"indices": [225, 234, 212, 120, 121, 124, 126, 127, 56, 57, 91, 181, 232, 92, 163, 179, 209, 178, 135, 142, 129, 233, 158, 17, 44, 239, 240, 107, 116, 117, 118, 119, 122, 206, 109, 177, 110, 111, 132, 146, 147, 249, 250, 180, 61, 152, 153, 94, 104, 108, 113, 128, 130, 141, 148, 157, 60, 7, 34, 45, 176, 70, 72, 100, 11, 5, 36, 53, 213, 230, 236, 52, 238, 237, 1, 2, 159, 160, 14, 247, 221, 220, 93, 99, 101, 115, 246, 54, 69, 189, 190, 79, 187, 19, 16, 46, 67, 74, 78, 86], "values": [0.69091796875, 0.69091796875, 0.68115234375, 0.63232421875, 0.63232421875, 0.63232421875, 0.63232421875, 0.63232421875, 0.61669921875, 0.61669921875, 0.61669921875, 0.61669921875, 0.6044921875, 0.5556640625, 0.52783203125, 0.52783203125, 0.51513671875, 0.51416015625, 0.4951171875, 0.491943359375, 0.487060546875, 0.485107421875, 0.47412109375, 0.468505859375, 0.461181640625, 0.45556640625, 0.45556640625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.449462890625, 0.447998046875, 0.440185546875, 0.43896484375, 0.431640625, 0.428955078125, 0.428955078125, 0.428955078125, 0.428955078125, 0.428955078125, 0.4287109375, 0.4287109375, 0.421875, 0.42138671875, 0.41259765625, 0.41259765625, 0.410888671875, 0.410888671875, 0.410888671875, 0.410888671875, 0.410888671875, 0.410888671875, 0.410888671875, 0.410888671875, 0.40771484375, 0.40576171875, 0.405029296875, 0.405029296875, 0.405029296875, 0.404541015625, 0.403564453125, 0.403564453125, 0.401611328125, 0.397705078125, 0.395751953125, 0.395751953125, 0.3955078125, 0.3916015625, 0.38525390625, 0.384765625, 0.38134765625, 0.38134765625, 0.37060546875, 0.35986328125, 0.35986328125, 0.350341796875, 0.350341796875, 0.3447265625, 0.336669921875, 0.332275390625, 0.331787109375, 0.329833984375, 0.32958984375, 0.32958984375, 0.32958984375, 0.32470703125, 0.3232421875, 0.3203125, 0.3125, 0.3125, 0.311767578125, 0.3115234375, 0.310546875, 0.30224609375, 0.30224609375, 0.30224609375, 0.30224609375, 0.30224609375, 0.30224609375]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4022, 4271, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 4021, 4023, 4024, 2336, 4852, 1074, 1111, 1153, 1171, 4504, 4335, 4222, 4624, 4306, 4616, 2368, 441, 2359, 4341, 4342, 4343, 1370, 3417, 3687, 3695, 3699, 3700, 4177, 2766, 2797, 2834, 2843, 2868, 2928, 2936, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 4577, 4757, 4775, 4977, 4340, 4500, 3223, 3751, 933, 3570, 3573, 3591, 4716, 4735, 4758, 4759, 4773, 2920, 3585, 4790, 4850, 3692, 3696, 4076, 4079, 4250], "values": [0.998046875, 0.998046875, 0.998046875, 0.998046875, 0.95947265625, 0.79345703125, 0.79345703125, 0.77685546875, 0.69482421875, 0.677734375, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.66455078125, 0.63916015625, 0.63916015625, 0.63916015625, 0.62158203125, 0.61474609375, 0.6142578125, 0.6142578125, 0.6142578125, 0.6142578125, 0.61083984375, 0.609375, 0.60595703125, 0.60595703125, 0.6015625, 0.59814453125, 0.5947265625, 0.591796875, 0.59130859375, 0.5869140625, 0.5869140625, 0.5869140625, 0.58447265625, 0.58447265625, 0.58447265625, 0.58447265625, 0.58447265625, 0.58447265625, 0.58447265625, 0.58154296875, 0.58154296875, 0.58154296875, 0.58154296875, 0.58154296875, 0.58154296875, 0.58154296875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.56982421875, 0.568359375, 0.568359375, 0.568359375, 0.56689453125, 0.5576171875, 0.55029296875, 0.55029296875, 0.54150390625, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53564453125, 0.53466796875, 0.5322265625, 0.529296875, 0.529296875, 0.525390625, 0.525390625, 0.521484375, 0.521484375, 0.51708984375]}, "6": {"indices": [391, 347, 250, 251, 253, 254, 255, 934, 392, 1158, 252, 3343, 3167, 1121, 1608, 1609, 222, 434, 435, 436, 3581, 3582, 3586, 3587, 223, 256, 58, 69, 70, 76, 11, 3604, 1103, 1147, 1167, 3965, 194, 2898, 3176, 3177, 3178, 3180, 3188, 3189, 3240, 3241, 3338, 350, 933, 941, 942, 1817, 82, 83, 396, 2874, 29, 1193, 935, 2821, 638, 4201, 4202, 4204, 4205, 2452, 2867, 1034, 1874, 2604, 54, 634, 885, 888, 182, 4420, 709, 722, 3328, 3329, 224, 2596, 2599, 2600, 2601, 1816, 1187, 3599, 14, 90, 4763, 4849, 4964, 4598, 588, 210, 3584, 4649, 2244, 3365], "values": [0.71728515625, 0.69140625, 0.654296875, 0.654296875, 0.654296875, 0.654296875, 0.654296875, 0.63916015625, 0.6181640625, 0.533203125, 0.5166015625, 0.50244140625, 0.4912109375, 0.476318359375, 0.46875, 0.46875, 0.4560546875, 0.4365234375, 0.4365234375, 0.4365234375, 0.435791015625, 0.435791015625, 0.435791015625, 0.435791015625, 0.435302734375, 0.431396484375, 0.4306640625, 0.4306640625, 0.4306640625, 0.4306640625, 0.423828125, 0.419189453125, 0.41552734375, 0.41552734375, 0.41552734375, 0.41357421875, 0.412841796875, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.409912109375, 0.404296875, 0.400146484375, 0.39990234375, 0.39990234375, 0.39990234375, 0.3994140625, 0.3994140625, 0.3984375, 0.3955078125, 0.392333984375, 0.392333984375, 0.38720703125, 0.38525390625, 0.384521484375, 0.383544921875, 0.383544921875, 0.383544921875, 0.383544921875, 0.3828125, 0.382080078125, 0.38037109375, 0.38037109375, 0.38037109375, 0.377197265625, 0.377197265625, 0.377197265625, 0.377197265625, 0.376953125, 0.37646484375, 0.3740234375, 0.3740234375, 0.371826171875, 0.371826171875, 0.368408203125, 0.368408203125, 0.368408203125, 0.368408203125, 0.368408203125, 0.3662109375, 0.363037109375, 0.358154296875, 0.355712890625, 0.35498046875, 0.3544921875, 0.3544921875, 0.3544921875, 0.353515625, 0.353271484375, 0.352783203125, 0.352783203125, 0.3505859375, 0.349853515625, 0.349609375]}, "7": {"indices": [54, 27, 31, 34, 32, 143, 33, 53, 66, 56, 61, 62, 35, 19, 63, 55, 124, 49, 50, 227, 47, 48, 26, 28, 118, 29, 51, 88, 57, 119, 132, 133, 10, 60, 46, 74, 75, 76, 77, 109, 157, 158, 159, 160, 204, 83, 192, 45, 9, 52, 97, 175, 1, 59, 111, 72, 178, 179, 180, 181, 182, 183, 213, 30, 73, 219, 220, 221, 44, 230, 18, 170, 172, 173, 123, 125, 113, 117, 128, 121, 114, 120, 58, 217, 214, 194, 64, 116, 130, 138, 200, 0, 185, 78, 2, 186, 233, 232, 225, 68], "values": [0.8447265625, 0.67724609375, 0.62158203125, 0.62158203125, 0.5986328125, 0.57470703125, 0.54931640625, 0.54248046875, 0.5380859375, 0.513671875, 0.513671875, 0.513671875, 0.488525390625, 0.483154296875, 0.476806640625, 0.467529296875, 0.447265625, 0.442626953125, 0.43310546875, 0.432373046875, 0.417236328125, 0.417236328125, 0.4130859375, 0.412353515625, 0.40087890625, 0.400390625, 0.3857421875, 0.378173828125, 0.37353515625, 0.37255859375, 0.363525390625, 0.363525390625, 0.36083984375, 0.357177734375, 0.35205078125, 0.34912109375, 0.34912109375, 0.34912109375, 0.34912109375, 0.345703125, 0.34228515625, 0.34228515625, 0.34228515625, 0.34228515625, 0.338623046875, 0.3369140625, 0.328125, 0.32568359375, 0.320556640625, 0.320068359375, 0.318359375, 0.31201171875, 0.31005859375, 0.3095703125, 0.30078125, 0.300537109375, 0.297119140625, 0.297119140625, 0.297119140625, 0.297119140625, 0.297119140625, 0.297119140625, 0.294921875, 0.288330078125, 0.28466796875, 0.276611328125, 0.276611328125, 0.276611328125, 0.267822265625, 0.26708984375, 0.266357421875, 0.261962890625, 0.261962890625, 0.261962890625, 0.259033203125, 0.259033203125, 0.257568359375, 0.253173828125, 0.2462158203125, 0.2396240234375, 0.2386474609375, 0.2376708984375, 0.233154296875, 0.231201171875, 0.2293701171875, 0.2237548828125, 0.216552734375, 0.21533203125, 0.2110595703125, 0.2110595703125, 0.20166015625, 0.199951171875, 0.1986083984375, 0.1968994140625, 0.1953125, 0.1953125, 0.19384765625, 0.193115234375, 0.1895751953125, 0.188232421875]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 640, 1377, 1600, 1601, 1602, 656, 441, 442, 443, 650, 161, 162, 554, 555, 159, 1973, 274, 277, 454, 1972, 1810, 451, 1959, 1195, 174, 175, 473, 474, 497, 498, 499, 156, 1491, 1492, 2222, 501, 157, 183, 923, 177, 178, 1874, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 1408, 2004, 680, 372, 1687, 2712, 1324, 1325, 1326, 1327, 418, 506, 507, 508, 565, 566, 567, 603, 649, 1078, 1079, 1203, 1204, 1081, 1138, 1232, 1240, 1328, 1498, 2013, 2022, 2041, 2068, 495, 496], "values": [0.9140625, 0.9140625, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87109375, 0.81005859375, 0.7802734375, 0.7578125, 0.75, 0.67626953125, 0.67626953125, 0.67626953125, 0.66455078125, 0.64208984375, 0.64208984375, 0.64208984375, 0.63623046875, 0.63134765625, 0.63134765625, 0.6142578125, 0.6142578125, 0.59765625, 0.5849609375, 0.5810546875, 0.5810546875, 0.5478515625, 0.53759765625, 0.5283203125, 0.52685546875, 0.5205078125, 0.517578125, 0.5087890625, 0.5087890625, 0.50830078125, 0.50830078125, 0.50830078125, 0.50830078125, 0.50830078125, 0.50244140625, 0.50244140625, 0.50244140625, 0.50244140625, 0.496337890625, 0.49267578125, 0.49267578125, 0.490966796875, 0.48779296875, 0.48779296875, 0.485595703125, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.478515625, 0.46044921875, 0.46044921875, 0.4541015625, 0.452392578125, 0.45166015625, 0.451416015625, 0.451171875, 0.451171875, 0.451171875, 0.451171875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.442138671875, 0.436279296875, 0.436279296875, 0.436279296875, 0.436279296875, 0.433837890625, 0.43359375, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.432373046875, 0.432373046875]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2839, 2919, 2840, 3631, 2475, 2941, 2944, 752, 283, 2473, 2474, 212, 213, 220, 221, 687, 751, 4204, 1027, 174, 278, 422, 286, 873, 827, 835, 4165, 828, 834, 1075, 1076, 1077, 1339, 2957, 4383, 4488, 630, 2448, 1790, 2916, 2018, 4533, 755, 756, 4349, 4350, 4376, 4434, 4435, 4507, 866, 867, 4348, 4351, 796, 772, 2481, 2431, 277, 1337, 262, 2476, 2803, 2961, 624, 753, 754, 905, 749, 904, 1031, 2195, 2308, 4676, 189, 1979, 1944, 3389, 3630, 260, 263, 264, 872, 1902, 36, 2772, 2225, 4753, 1457, 4180, 4374, 684, 686, 173, 2777], "values": [0.99755859375, 0.89697265625, 0.85986328125, 0.8056640625, 0.8056640625, 0.6708984375, 0.669921875, 0.66650390625, 0.65478515625, 0.64404296875, 0.64404296875, 0.64404296875, 0.62890625, 0.6279296875, 0.6279296875, 0.6279296875, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.62060546875, 0.607421875, 0.58740234375, 0.57470703125, 0.5693359375, 0.5693359375, 0.568359375, 0.56787109375, 0.56396484375, 0.5556640625, 0.5556640625, 0.5498046875, 0.53857421875, 0.53857421875, 0.53857421875, 0.53857421875, 0.53857421875, 0.53857421875, 0.53076171875, 0.52978515625, 0.52978515625, 0.52734375, 0.51611328125, 0.51318359375, 0.51318359375, 0.50830078125, 0.5078125, 0.50537109375, 0.50537109375, 0.5029296875, 0.5029296875, 0.5029296875, 0.50048828125, 0.50048828125, 0.493408203125, 0.492431640625, 0.492431640625, 0.486083984375, 0.486083984375, 0.48291015625, 0.477294921875, 0.4765625, 0.46826171875, 0.468017578125, 0.466796875, 0.465576171875, 0.46435546875, 0.46435546875, 0.46435546875, 0.45458984375, 0.45458984375, 0.45458984375, 0.45458984375, 0.44921875, 0.44921875, 0.44482421875, 0.44482421875, 0.44482421875, 0.44482421875, 0.4443359375, 0.44384765625, 0.4345703125, 0.419921875, 0.41943359375, 0.416748046875, 0.416748046875, 0.416748046875, 0.4130859375, 0.4130859375, 0.4091796875, 0.408935546875, 0.40771484375, 0.407470703125, 0.406982421875, 0.403564453125, 0.401123046875, 0.400390625, 0.400390625, 0.400146484375, 0.400146484375]}, "10": {"indices": [262, 267, 279, 462, 1346, 1665, 1039, 691, 1286, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 1668, 1653, 1654, 1666, 273, 277, 278, 1521, 1553, 1565, 648, 754, 794, 1656, 1192, 1333, 1340, 1697, 1699, 1702, 501, 730, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 679, 685, 792, 1671, 1673, 570, 795, 884, 1445, 502, 1472, 1524, 1548, 1335, 1691, 1669, 1751, 1672, 1424, 506, 520, 1621, 524, 340, 1677, 482, 1344, 334, 123, 1724, 210, 212, 1221, 1252, 1279, 1283, 209, 218, 672], "values": [0.998046875, 0.998046875, 0.998046875, 0.8994140625, 0.8994140625, 0.8994140625, 0.89208984375, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.86865234375, 0.86865234375, 0.86865234375, 0.8681640625, 0.8681640625, 0.8681640625, 0.86328125, 0.86328125, 0.86328125, 0.86279296875, 0.86279296875, 0.86279296875, 0.85888671875, 0.8515625, 0.849609375, 0.849609375, 0.849609375, 0.849609375, 0.849609375, 0.84765625, 0.84765625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.8447265625, 0.84375, 0.84375, 0.84375, 0.83984375, 0.83984375, 0.83740234375, 0.83642578125, 0.83642578125, 0.8359375, 0.83544921875, 0.83203125, 0.82861328125, 0.82861328125, 0.82666015625, 0.82666015625, 0.82421875, 0.82421875, 0.81982421875, 0.818359375, 0.81640625, 0.81640625, 0.81591796875, 0.8154296875, 0.8125, 0.80810546875, 0.80419921875, 0.80419921875, 0.80126953125, 0.80078125, 0.798828125, 0.79736328125, 0.79736328125, 0.7958984375, 0.7958984375, 0.7958984375, 0.7958984375, 0.79541015625, 0.79541015625, 0.794921875]}, "11": {"indices": [457, 1613, 553, 557, 558, 577, 587, 761, 1060, 478, 1651, 1685, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 545, 945, 946, 953, 1533, 1012, 1338, 1189, 514, 1040, 1571, 1745, 1779, 480, 1580, 1615, 484, 493, 494, 495, 499, 500, 507, 521, 550, 552, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1602, 829, 490, 519, 965, 1378, 1205, 511, 1734, 571, 1271, 1272, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 32, 1718, 383, 35, 43, 141, 562, 101, 102, 124, 125, 126, 139, 140, 147, 526, 1579, 1713, 1720, 1730, 1731, 1752], "values": [0.99755859375, 0.97119140625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9541015625, 0.9482421875, 0.9482421875, 0.943359375, 0.92822265625, 0.92822265625, 0.92822265625, 0.9267578125, 0.9267578125, 0.9267578125, 0.9267578125, 0.92333984375, 0.90771484375, 0.90771484375, 0.904296875, 0.90234375, 0.90234375, 0.896484375, 0.89306640625, 0.88916015625, 0.88916015625, 0.8876953125, 0.873046875, 0.873046875, 0.873046875, 0.873046875, 0.873046875, 0.861328125, 0.861328125, 0.861328125, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.8583984375, 0.84814453125, 0.84765625, 0.84765625, 0.84765625, 0.84033203125, 0.8349609375, 0.8291015625, 0.82861328125, 0.82666015625, 0.826171875, 0.826171875, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.818359375, 0.818359375, 0.8173828125, 0.81689453125, 0.81689453125, 0.81591796875, 0.8154296875, 0.814453125, 0.814453125, 0.814453125, 0.814453125, 0.814453125, 0.810546875, 0.810546875, 0.810546875, 0.8076171875, 0.8076171875, 0.8076171875, 0.8076171875, 0.8076171875, 0.8076171875, 0.8076171875]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 189, 173, 304, 187, 214, 334, 365, 366, 74, 77, 80, 284, 227, 20, 132, 237, 203, 180, 37, 364, 243, 276, 277, 278, 281, 110, 47, 130, 200, 150, 362, 118, 72, 151, 213, 215, 391, 392, 393, 394, 395, 396, 380, 196, 282, 116, 117, 381, 382, 354, 355, 66, 181, 190, 6, 34, 35, 36, 68, 326, 60, 359, 63, 157, 158, 43, 251, 48, 58, 121, 123, 124, 144, 179, 254, 156, 100, 160, 161, 178, 32, 106, 112, 114, 115, 147, 153, 191, 61, 233], "values": [0.82763671875, 0.82763671875, 0.82763671875, 0.6650390625, 0.6650390625, 0.6650390625, 0.66357421875, 0.66357421875, 0.50732421875, 0.49169921875, 0.48193359375, 0.45263671875, 0.4462890625, 0.446044921875, 0.432861328125, 0.431640625, 0.431640625, 0.431640625, 0.426513671875, 0.426513671875, 0.415283203125, 0.4052734375, 0.3876953125, 0.386962890625, 0.38134765625, 0.38134765625, 0.375732421875, 0.361083984375, 0.3583984375, 0.353759765625, 0.349853515625, 0.344482421875, 0.344482421875, 0.344482421875, 0.344482421875, 0.338134765625, 0.336669921875, 0.335693359375, 0.335693359375, 0.3349609375, 0.333984375, 0.333740234375, 0.3251953125, 0.323486328125, 0.323486328125, 0.3232421875, 0.32177734375, 0.32177734375, 0.32177734375, 0.32177734375, 0.32177734375, 0.32177734375, 0.3203125, 0.320068359375, 0.31982421875, 0.316162109375, 0.316162109375, 0.31591796875, 0.31591796875, 0.3134765625, 0.3134765625, 0.313232421875, 0.30908203125, 0.306396484375, 0.305908203125, 0.3046875, 0.3046875, 0.3046875, 0.29931640625, 0.29931640625, 0.299072265625, 0.297119140625, 0.287109375, 0.285888671875, 0.285888671875, 0.283203125, 0.283203125, 0.281005859375, 0.27783203125, 0.27587890625, 0.27587890625, 0.27587890625, 0.275390625, 0.2744140625, 0.2744140625, 0.2724609375, 0.27197265625, 0.27099609375, 0.27099609375, 0.27099609375, 0.26904296875, 0.26806640625, 0.26806640625, 0.26806640625, 0.26806640625, 0.26806640625, 0.26806640625, 0.26806640625, 0.265869140625, 0.262451171875]}, "13": {"indices": [275, 274, 272, 273, 276, 537, 277, 513, 578, 529, 530, 573, 574, 2836, 2478, 575, 576, 498, 1864, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 2832, 2833, 229, 230, 607, 2875, 293, 1381, 531, 2373, 602, 442, 533, 296, 298, 526, 1067, 1070, 2525, 279, 291, 2458, 1990, 1991, 1992, 348, 349, 350, 363, 365, 370, 371, 716, 717, 1164, 2882, 2884, 2885, 2838, 270, 254, 2259, 233, 1775, 1776, 528, 3040, 2843, 1026, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 1641, 1642, 1643, 2611, 2568, 2569, 2128], "values": [0.64453125, 0.5888671875, 0.55908203125, 0.55517578125, 0.5517578125, 0.55029296875, 0.546875, 0.54443359375, 0.5263671875, 0.505859375, 0.505859375, 0.50439453125, 0.50439453125, 0.47802734375, 0.477294921875, 0.47314453125, 0.47314453125, 0.469970703125, 0.465576171875, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.464599609375, 0.463134765625, 0.463134765625, 0.447998046875, 0.447998046875, 0.447509765625, 0.445556640625, 0.444580078125, 0.435791015625, 0.43505859375, 0.4296875, 0.42431640625, 0.42333984375, 0.419677734375, 0.41796875, 0.416748046875, 0.413330078125, 0.413330078125, 0.413330078125, 0.407470703125, 0.406982421875, 0.40185546875, 0.400146484375, 0.3974609375, 0.3974609375, 0.3974609375, 0.397216796875, 0.397216796875, 0.397216796875, 0.397216796875, 0.397216796875, 0.397216796875, 0.397216796875, 0.39697265625, 0.39697265625, 0.396240234375, 0.393310546875, 0.393310546875, 0.393310546875, 0.39111328125, 0.390869140625, 0.390625, 0.388671875, 0.3857421875, 0.3857421875, 0.3857421875, 0.384033203125, 0.382080078125, 0.3818359375, 0.380859375, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.380126953125, 0.376708984375, 0.376708984375, 0.3759765625]}, "14": {"indices": [291, 2328, 484, 485, 293, 2404, 2397, 273, 271, 272, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 2912, 495, 2886, 292, 2472, 557, 2568, 2569, 1598, 2339, 2521, 506, 1063, 1064, 1065, 298, 304, 748, 284, 285, 286, 287, 288, 303, 305, 575, 576, 458, 750, 2523, 296, 2525, 901, 300, 2402, 2403, 2163, 302, 489, 294, 295, 269, 743, 744, 745, 746, 957, 477, 478, 420, 539, 747, 749, 280, 281, 2111, 2112, 314, 297, 2008, 2009, 2010, 789, 504, 2366, 290, 1117, 275, 229, 230, 317, 2687, 2688, 474, 2122, 703, 279, 770, 2838, 2372, 2416], "values": [0.6357421875, 0.619140625, 0.615234375, 0.615234375, 0.6015625, 0.59765625, 0.59326171875, 0.58935546875, 0.5712890625, 0.5712890625, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.5439453125, 0.541015625, 0.541015625, 0.541015625, 0.5234375, 0.5048828125, 0.5009765625, 0.499267578125, 0.49755859375, 0.4970703125, 0.49365234375, 0.49365234375, 0.492431640625, 0.486572265625, 0.486572265625, 0.483642578125, 0.482666015625, 0.482666015625, 0.482666015625, 0.4814453125, 0.47705078125, 0.470458984375, 0.466064453125, 0.466064453125, 0.466064453125, 0.466064453125, 0.466064453125, 0.459716796875, 0.455322265625, 0.455078125, 0.455078125, 0.45361328125, 0.451416015625, 0.450927734375, 0.449951171875, 0.448486328125, 0.44775390625, 0.4423828125, 0.437744140625, 0.437744140625, 0.43505859375, 0.42724609375, 0.4267578125, 0.4228515625, 0.4228515625, 0.419189453125, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.41650390625, 0.415283203125, 0.415283203125, 0.414306640625, 0.41259765625, 0.41259765625, 0.41259765625, 0.411865234375, 0.411865234375, 0.410400390625, 0.410400390625, 0.405029296875, 0.40234375, 0.402099609375, 0.402099609375, 0.402099609375, 0.400146484375, 0.39990234375, 0.39453125, 0.39111328125, 0.390625, 0.39013671875, 0.388427734375, 0.388427734375, 0.388427734375, 0.38720703125, 0.38720703125, 0.38525390625, 0.385009765625, 0.383544921875, 0.381103515625, 0.379638671875, 0.376953125, 0.37646484375, 0.37060546875]}, "15": {"indices": [570, 483, 2770, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 2406, 2407, 1378, 1380, 1599, 1600, 255, 260, 261, 1041, 1052, 475, 486, 773, 1117, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 742, 789, 1294, 1295, 1333, 191, 192, 923, 1208, 2199, 2215, 2216, 2217, 259, 461, 524, 525, 2712, 1813, 366, 2369, 1124, 2157, 927, 928, 361, 422, 2065, 1181, 1182, 1183, 1029, 566, 572, 2625, 2564, 1265, 1266, 1281, 926, 1661, 1838, 1857, 2866, 2520, 2332, 431, 1704, 1763, 1764, 1765, 1766, 1767, 2777, 1071, 1072, 1545, 1403, 1907, 442, 385, 1602, 2504, 2558, 2559, 900], "values": [0.6826171875, 0.61181640625, 0.60888671875, 0.52880859375, 0.5029296875, 0.5029296875, 0.5029296875, 0.5029296875, 0.5029296875, 0.5029296875, 0.5029296875, 0.493408203125, 0.481689453125, 0.481689453125, 0.474365234375, 0.474365234375, 0.451416015625, 0.451416015625, 0.45068359375, 0.45068359375, 0.45068359375, 0.43994140625, 0.43994140625, 0.431396484375, 0.431396484375, 0.43017578125, 0.421630859375, 0.417724609375, 0.417724609375, 0.417724609375, 0.417724609375, 0.417724609375, 0.417724609375, 0.417724609375, 0.416259765625, 0.416015625, 0.41552734375, 0.41552734375, 0.41552734375, 0.41259765625, 0.41259765625, 0.41015625, 0.40869140625, 0.408203125, 0.408203125, 0.408203125, 0.408203125, 0.40771484375, 0.40673828125, 0.40673828125, 0.40673828125, 0.405517578125, 0.4052734375, 0.404052734375, 0.40234375, 0.400390625, 0.39990234375, 0.398681640625, 0.398681640625, 0.3984375, 0.3984375, 0.397216796875, 0.39404296875, 0.39404296875, 0.39404296875, 0.393798828125, 0.392578125, 0.392578125, 0.3916015625, 0.389892578125, 0.3896484375, 0.3896484375, 0.3896484375, 0.388671875, 0.387451171875, 0.385009765625, 0.385009765625, 0.384765625, 0.38330078125, 0.38232421875, 0.38134765625, 0.376708984375, 0.376708984375, 0.376708984375, 0.376708984375, 0.376708984375, 0.376708984375, 0.376708984375, 0.376220703125, 0.376220703125, 0.370849609375, 0.369384765625, 0.369384765625, 0.368408203125, 0.365966796875, 0.36376953125, 0.361083984375, 0.361083984375, 0.361083984375, 0.359130859375]}, "16": {"indices": [380, 202, 3432, 3343, 3347, 2884, 2885, 2890, 2892, 2658, 1879, 1880, 4445, 4887, 1883, 1885, 1888, 1889, 3337, 3909, 793, 991, 3464, 1895, 852, 891, 3970, 3972, 3973, 4669, 3975, 3977, 3978, 1882, 1890, 1891, 3155, 33, 43, 2020, 111, 112, 113, 114, 364, 4662, 1827, 1832, 2480, 2484, 3899, 1881, 3979, 3338, 3340, 4169, 3900, 990, 4609, 2886, 3235, 4865, 359, 4551, 4595, 4660, 4663, 3334, 3145, 3966, 1903, 1016, 1972, 1860, 2016, 1141, 1142, 945, 785, 786, 795, 796, 800, 1062, 3976, 3778, 881, 3149, 4171, 4172, 2593, 994, 2389, 1317, 4854, 4580, 2536, 512, 513, 514], "values": [0.892578125, 0.65380859375, 0.65185546875, 0.61328125, 0.61328125, 0.59619140625, 0.59619140625, 0.59619140625, 0.59619140625, 0.59130859375, 0.5830078125, 0.5830078125, 0.57958984375, 0.568359375, 0.55517578125, 0.55517578125, 0.55517578125, 0.55517578125, 0.55419921875, 0.546875, 0.54541015625, 0.54541015625, 0.54541015625, 0.54443359375, 0.54248046875, 0.53857421875, 0.5380859375, 0.52880859375, 0.52880859375, 0.5283203125, 0.52783203125, 0.52783203125, 0.52783203125, 0.52685546875, 0.52685546875, 0.52685546875, 0.52685546875, 0.52587890625, 0.52587890625, 0.52587890625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5244140625, 0.5234375, 0.5205078125, 0.52001953125, 0.51806640625, 0.51806640625, 0.515625, 0.51513671875, 0.51318359375, 0.5126953125, 0.5126953125, 0.51171875, 0.51025390625, 0.50634765625, 0.5048828125, 0.50439453125, 0.50390625, 0.49951171875, 0.498779296875, 0.498291015625, 0.498291015625, 0.498291015625, 0.498291015625, 0.496826171875, 0.49609375, 0.49072265625, 0.489501953125, 0.488525390625, 0.48828125, 0.48486328125, 0.48095703125, 0.479248046875, 0.479248046875, 0.478271484375, 0.477294921875, 0.477294921875, 0.4765625, 0.4765625, 0.4765625, 0.474609375, 0.47265625, 0.47021484375, 0.46826171875, 0.466796875, 0.466552734375, 0.466552734375, 0.462646484375, 0.46240234375, 0.46142578125, 0.458740234375, 0.458740234375, 0.4580078125, 0.455810546875, 0.45556640625, 0.45556640625, 0.45556640625]}, "17": {"indices": [29, 24, 18, 8, 10, 22, 6, 7, 19, 20, 31, 11, 12, 25, 32, 21, 27, 23, 26, 28, 9, 3, 4, 34, 35, 1, 2, 5, 30, 0, 33, 13, 14, 15, 16, 17], "values": [0.50439453125, 0.5029296875, 0.477294921875, 0.4599609375, 0.4599609375, 0.435546875, 0.431640625, 0.431640625, 0.431640625, 0.431640625, 0.41748046875, 0.40087890625, 0.40087890625, 0.39306640625, 0.382080078125, 0.375244140625, 0.33544921875, 0.328369140625, 0.31640625, 0.30126953125, 0.282958984375, 0.279052734375, 0.27197265625, 0.27197265625, 0.27197265625, 0.2318115234375, 0.2318115234375, 0.2109375, 0.17626953125, 0.1390380859375, 0.05804443359375, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 24, 22, 26, 6, 7, 19, 20, 8, 10, 21, 31, 30, 28, 27, 23, 9, 29, 1, 2, 5, 25, 4, 34, 35, 11, 12, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.70556640625, 0.63916015625, 0.63134765625, 0.61669921875, 0.5771484375, 0.5771484375, 0.5771484375, 0.5771484375, 0.5615234375, 0.5615234375, 0.5361328125, 0.525390625, 0.52001953125, 0.51904296875, 0.498779296875, 0.491943359375, 0.48291015625, 0.46142578125, 0.43115234375, 0.43115234375, 0.4267578125, 0.4189453125, 0.4072265625, 0.394287109375, 0.394287109375, 0.393798828125, 0.393798828125, 0.3291015625, 0.30908203125, 0.26708984375, 0.12445068359375, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 165, 163, 53, 54, 88, 89, 113, 59, 61, 107, 208, 212, 121, 197, 9, 57, 45, 203, 213, 5, 8, 64, 217, 65, 66, 177, 72, 71, 60, 210, 211, 182, 174, 192, 128, 144, 145, 146, 228, 4, 6, 7, 48, 83, 122, 196, 91, 0, 200, 191, 56, 82, 245, 183, 16, 18, 76, 115, 67, 84, 205, 215, 201, 114, 116, 207, 246, 199, 214, 112, 25, 124, 24, 209, 216, 17, 198, 235, 249, 193, 135, 181, 105, 126, 143, 73, 247, 185, 237, 204, 236, 240, 1, 239, 12, 130], "values": [0.81689453125, 0.796875, 0.796875, 0.55029296875, 0.5498046875, 0.49853515625, 0.492431640625, 0.492431640625, 0.48681640625, 0.48681640625, 0.46337890625, 0.45556640625, 0.425537109375, 0.401123046875, 0.363037109375, 0.36181640625, 0.360107421875, 0.354736328125, 0.352783203125, 0.34619140625, 0.330810546875, 0.310546875, 0.31005859375, 0.299072265625, 0.28564453125, 0.2822265625, 0.2783203125, 0.27587890625, 0.27587890625, 0.24462890625, 0.23583984375, 0.2352294921875, 0.2322998046875, 0.2318115234375, 0.2318115234375, 0.2269287109375, 0.2216796875, 0.218017578125, 0.2076416015625, 0.2059326171875, 0.2059326171875, 0.2059326171875, 0.197021484375, 0.1944580078125, 0.1944580078125, 0.1944580078125, 0.191162109375, 0.1871337890625, 0.1806640625, 0.1783447265625, 0.1712646484375, 0.1688232421875, 0.1683349609375, 0.16748046875, 0.1654052734375, 0.1654052734375, 0.1644287109375, 0.16015625, 0.1558837890625, 0.1558837890625, 0.1558837890625, 0.1549072265625, 0.1527099609375, 0.1513671875, 0.144775390625, 0.14453125, 0.133056640625, 0.1328125, 0.1328125, 0.1318359375, 0.12890625, 0.1282958984375, 0.1279296875, 0.1256103515625, 0.12298583984375, 0.11767578125, 0.11236572265625, 0.11083984375, 0.1104736328125, 0.109130859375, 0.10906982421875, 0.10858154296875, 0.1082763671875, 0.1070556640625, 0.10321044921875, 0.101806640625, 0.1014404296875, 0.1004638671875, 0.1004638671875, 0.09796142578125, 0.093505859375, 0.09228515625, 0.08929443359375, 0.089111328125, 0.0887451171875, 0.08636474609375, 0.082275390625, 0.0789794921875, 0.0726318359375, 0.072265625]}}
v2 =  {"0": {"indices": [395, 686, 434, 783, 785, 379, 397, 199, 396, 182, 21, 89, 180, 181, 207, 209, 787, 788, 789, 758, 778, 779, 203, 204, 618, 620, 621, 622, 627, 791, 432, 811, 86, 87, 535, 408, 409, 153, 796, 634, 71, 110, 111, 534, 793, 465, 469, 486, 487, 488, 626, 668, 795, 152, 410, 174, 433, 200, 545, 16, 768, 769, 441, 205, 166, 167, 105, 172, 161, 631, 633, 429, 88, 612, 866, 40, 674, 399, 404, 405, 406, 407, 669, 154, 385, 435, 413, 767, 217, 558, 895, 897, 898, 431, 673, 411, 835, 542, 858, 34], "values": [0.61328125, 0.58544921875, 0.57275390625, 0.55615234375, 0.55615234375, 0.55126953125, 0.55126953125, 0.54541015625, 0.53759765625, 0.537109375, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.52099609375, 0.52099609375, 0.50341796875, 0.50341796875, 0.50341796875, 0.50244140625, 0.50244140625, 0.50244140625, 0.50146484375, 0.50146484375, 0.49853515625, 0.49853515625, 0.49853515625, 0.49853515625, 0.495361328125, 0.469482421875, 0.465576171875, 0.428955078125, 0.424560546875, 0.424560546875, 0.4208984375, 0.419189453125, 0.419189453125, 0.4189453125, 0.41748046875, 0.4150390625, 0.4130859375, 0.4130859375, 0.4130859375, 0.401611328125, 0.395751953125, 0.382568359375, 0.382568359375, 0.382568359375, 0.382568359375, 0.382568359375, 0.373046875, 0.373046875, 0.372314453125, 0.369140625, 0.3623046875, 0.359375, 0.353759765625, 0.349365234375, 0.341552734375, 0.334716796875, 0.334716796875, 0.334716796875, 0.331298828125, 0.326416015625, 0.32421875, 0.32421875, 0.323974609375, 0.323974609375, 0.32373046875, 0.322021484375, 0.322021484375, 0.32080078125, 0.3193359375, 0.318603515625, 0.318115234375, 0.31201171875, 0.307861328125, 0.307373046875, 0.307373046875, 0.307373046875, 0.307373046875, 0.307373046875, 0.307373046875, 0.306396484375, 0.305908203125, 0.305908203125, 0.303466796875, 0.303466796875, 0.30224609375, 0.29833984375, 0.29345703125, 0.29345703125, 0.29345703125, 0.290283203125, 0.289306640625, 0.28857421875, 0.2880859375, 0.287353515625, 0.284912109375, 0.283203125]}, "1": {"indices": [75, 71, 72, 84, 86, 87, 88, 89, 90, 91, 92, 96, 97, 59, 66, 67, 21, 10, 9, 85, 139, 48, 61, 45, 60, 53, 62, 63, 47, 94, 95, 137, 0, 140, 40, 77, 78, 138, 98, 76, 74, 157, 158, 159, 160, 80, 81, 58, 142, 15, 106, 108, 109, 110, 111, 115, 153, 154, 135, 93, 32, 107, 112, 114, 57, 141, 17, 28, 131, 27, 82, 155, 24, 25, 127, 132, 133, 134, 125, 35, 36, 54, 124, 46, 2, 79, 51, 52, 73, 22, 23, 143, 19, 146, 113, 49, 34, 148, 149, 150], "values": [0.61328125, 0.5888671875, 0.5888671875, 0.5166015625, 0.49951171875, 0.49951171875, 0.4765625, 0.4765625, 0.4765625, 0.4765625, 0.4765625, 0.4765625, 0.4765625, 0.4560546875, 0.447998046875, 0.447998046875, 0.423828125, 0.418701171875, 0.392578125, 0.38134765625, 0.38037109375, 0.375, 0.348876953125, 0.34228515625, 0.341552734375, 0.329833984375, 0.324951171875, 0.324951171875, 0.3212890625, 0.318603515625, 0.318603515625, 0.2900390625, 0.28759765625, 0.27490234375, 0.26953125, 0.26611328125, 0.26611328125, 0.260986328125, 0.259765625, 0.25732421875, 0.2474365234375, 0.2435302734375, 0.2435302734375, 0.2435302734375, 0.2435302734375, 0.2392578125, 0.2392578125, 0.2374267578125, 0.2318115234375, 0.2314453125, 0.2108154296875, 0.2108154296875, 0.2108154296875, 0.2108154296875, 0.2108154296875, 0.2108154296875, 0.210205078125, 0.210205078125, 0.2100830078125, 0.2095947265625, 0.202880859375, 0.1944580078125, 0.1944580078125, 0.1944580078125, 0.1937255859375, 0.187255859375, 0.18701171875, 0.170654296875, 0.16015625, 0.158447265625, 0.1572265625, 0.1553955078125, 0.1534423828125, 0.1534423828125, 0.1466064453125, 0.1466064453125, 0.1466064453125, 0.142578125, 0.1422119140625, 0.1370849609375, 0.1370849609375, 0.1370849609375, 0.133056640625, 0.1260986328125, 0.12274169921875, 0.1221923828125, 0.11151123046875, 0.11151123046875, 0.10504150390625, 0.1048583984375, 0.1048583984375, 0.09814453125, 0.093017578125, 0.0836181640625, 0.0833740234375, 0.079833984375, 0.0784912109375, 0.0777587890625, 0.0777587890625, 0.0777587890625]}, "2": {"indices": [5, 32, 44, 43, 4, 3, 18, 55, 37, 38, 39, 31, 33, 8, 56, 6, 30, 7, 41, 42, 35, 36, 54, 34, 15, 9, 74, 22, 23, 48, 156, 49, 88, 89, 90, 91, 92, 96, 97, 12, 13, 14, 10, 26, 16, 24, 25, 29, 86, 87, 75, 84, 17, 94, 95, 157, 158, 159, 160, 76, 151, 85, 40, 141, 155, 98, 93, 46, 21, 131, 53, 19, 51, 52, 146, 20, 153, 154, 0, 2, 69, 70, 71, 72, 50, 143, 148, 149, 150, 140, 138, 47, 137, 66, 67, 127, 132, 133, 45, 81], "values": [0.75244140625, 0.689453125, 0.64697265625, 0.640625, 0.62255859375, 0.6181640625, 0.6181640625, 0.60791015625, 0.57666015625, 0.57666015625, 0.57666015625, 0.5673828125, 0.5673828125, 0.53662109375, 0.5048828125, 0.50048828125, 0.455322265625, 0.435791015625, 0.435791015625, 0.435791015625, 0.423828125, 0.423828125, 0.423828125, 0.402587890625, 0.3642578125, 0.355224609375, 0.29931640625, 0.288330078125, 0.288330078125, 0.28466796875, 0.281005859375, 0.27197265625, 0.26708984375, 0.26708984375, 0.26708984375, 0.26708984375, 0.26708984375, 0.26708984375, 0.26708984375, 0.2666015625, 0.2666015625, 0.2666015625, 0.26025390625, 0.255126953125, 0.25244140625, 0.24853515625, 0.24853515625, 0.2440185546875, 0.23681640625, 0.23681640625, 0.2210693359375, 0.213623046875, 0.21240234375, 0.2059326171875, 0.2059326171875, 0.2025146484375, 0.2025146484375, 0.2025146484375, 0.2025146484375, 0.2003173828125, 0.197998046875, 0.197265625, 0.1964111328125, 0.1846923828125, 0.18359375, 0.1707763671875, 0.168701171875, 0.1622314453125, 0.151123046875, 0.151123046875, 0.1483154296875, 0.131103515625, 0.1298828125, 0.1298828125, 0.126708984375, 0.1259765625, 0.12054443359375, 0.12054443359375, 0.11883544921875, 0.11834716796875, 0.10955810546875, 0.10955810546875, 0.10577392578125, 0.10577392578125, 0.09716796875, 0.0965576171875, 0.09234619140625, 0.09234619140625, 0.09234619140625, 0.0916748046875, 0.09033203125, 0.086181640625, 0.07818603515625, 0.076416015625, 0.076416015625, 0.0755615234375, 0.0755615234375, 0.0755615234375, 0.07208251953125, 0.07061767578125]}, "3": {"indices": [66, 447, 420, 449, 450, 64, 149, 444, 448, 298, 325, 326, 200, 204, 268, 442, 89, 311, 4083, 46, 50, 65, 67, 775, 1014, 84, 4073, 3871, 68, 76, 85, 86, 312, 313, 526, 709, 1142, 4067, 4068, 4383, 2134, 2160, 524, 8, 1015, 3100, 3268, 3269, 3270, 3271, 2887, 1499, 2406, 2407, 851, 852, 853, 4380, 2702, 451, 456, 3236, 3874, 391, 397, 90, 525, 107, 88, 100, 4228, 4777, 4778, 4779, 682, 782, 804, 209, 870, 1506, 1507, 4087, 4088, 4326, 4352, 148, 4146, 4200, 4193, 55, 56, 58, 57, 708, 2948, 91, 4151, 1546, 1554, 1555], "values": [0.998046875, 0.998046875, 0.96826171875, 0.96826171875, 0.96826171875, 0.8505859375, 0.8505859375, 0.8505859375, 0.8505859375, 0.84130859375, 0.84130859375, 0.84130859375, 0.8408203125, 0.8408203125, 0.8408203125, 0.8408203125, 0.8193359375, 0.74951171875, 0.73828125, 0.7255859375, 0.7255859375, 0.7255859375, 0.7255859375, 0.7255859375, 0.7255859375, 0.71484375, 0.7080078125, 0.70458984375, 0.6875, 0.6875, 0.6875, 0.6875, 0.6875, 0.6875, 0.66455078125, 0.66455078125, 0.66455078125, 0.65576171875, 0.65576171875, 0.65576171875, 0.65478515625, 0.65478515625, 0.642578125, 0.64208984375, 0.63671875, 0.626953125, 0.626953125, 0.626953125, 0.626953125, 0.626953125, 0.6142578125, 0.6123046875, 0.60791015625, 0.60791015625, 0.60546875, 0.60546875, 0.60546875, 0.59716796875, 0.59423828125, 0.59375, 0.59375, 0.59326171875, 0.5869140625, 0.58642578125, 0.5849609375, 0.583984375, 0.583984375, 0.57470703125, 0.57373046875, 0.57373046875, 0.57275390625, 0.5712890625, 0.5712890625, 0.5712890625, 0.57080078125, 0.57080078125, 0.57080078125, 0.56982421875, 0.56982421875, 0.56884765625, 0.56884765625, 0.56787109375, 0.56787109375, 0.56787109375, 0.56787109375, 0.56494140625, 0.5634765625, 0.5634765625, 0.5615234375, 0.5595703125, 0.5595703125, 0.5595703125, 0.55859375, 0.54931640625, 0.54736328125, 0.54541015625, 0.54150390625, 0.541015625, 0.53955078125, 0.53955078125]}, "4": {"indices": [225, 234, 212, 120, 121, 124, 126, 127, 56, 57, 91, 181, 232, 92, 163, 179, 209, 178, 129, 142, 135, 233, 158, 44, 17, 239, 240, 107, 116, 117, 118, 119, 206, 122, 109, 180, 110, 111, 132, 146, 147, 177, 249, 250, 61, 94, 104, 108, 113, 128, 130, 141, 148, 152, 153, 7, 34, 11, 213, 70, 72, 60, 157, 100, 53, 36, 5, 176, 45, 230, 238, 236, 52, 237, 14, 159, 160, 1, 2, 93, 247, 220, 99, 101, 115, 221, 54, 189, 190, 69, 79, 19, 246, 226, 227, 51, 16, 67, 74, 78], "values": [0.68115234375, 0.68115234375, 0.67041015625, 0.6337890625, 0.6337890625, 0.6337890625, 0.6337890625, 0.6337890625, 0.61328125, 0.61328125, 0.61328125, 0.61328125, 0.60400390625, 0.5556640625, 0.51953125, 0.51953125, 0.50927734375, 0.50439453125, 0.50390625, 0.495361328125, 0.490966796875, 0.484619140625, 0.4619140625, 0.459716796875, 0.45556640625, 0.4521484375, 0.4521484375, 0.447265625, 0.447265625, 0.447265625, 0.447265625, 0.447265625, 0.443115234375, 0.439453125, 0.433349609375, 0.428955078125, 0.4267578125, 0.4267578125, 0.4267578125, 0.4267578125, 0.4267578125, 0.4228515625, 0.420654296875, 0.420654296875, 0.41552734375, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.4091796875, 0.407470703125, 0.407470703125, 0.406494140625, 0.406494140625, 0.3994140625, 0.3974609375, 0.39697265625, 0.39697265625, 0.3955078125, 0.39306640625, 0.392822265625, 0.39208984375, 0.3916015625, 0.388671875, 0.38671875, 0.38330078125, 0.382080078125, 0.380126953125, 0.373291015625, 0.3701171875, 0.365234375, 0.3515625, 0.34814453125, 0.34814453125, 0.346435546875, 0.346435546875, 0.3359375, 0.334716796875, 0.329345703125, 0.328369140625, 0.328369140625, 0.328369140625, 0.327392578125, 0.318115234375, 0.3154296875, 0.3154296875, 0.312255859375, 0.311767578125, 0.31103515625, 0.308837890625, 0.305419921875, 0.305419921875, 0.29638671875, 0.296142578125, 0.296142578125, 0.296142578125, 0.296142578125]}, "5": {"indices": [1004, 1005, 1011, 1071, 1381, 1984, 2026, 2242, 4022, 4271, 2327, 2366, 2367, 2471, 2584, 2632, 2728, 2846, 2848, 2849, 2881, 2882, 2883, 2884, 3006, 3010, 3014, 4021, 4023, 4024, 2336, 4852, 4504, 1074, 1111, 1153, 1171, 4341, 4342, 4343, 4616, 4222, 4624, 4335, 4306, 2359, 2368, 2766, 2797, 2834, 2843, 2740, 2780, 2791, 2844, 2847, 2852, 2855, 2864, 2867, 2905, 2918, 2937, 441, 1370, 3417, 3687, 3695, 3699, 3700, 4177, 2868, 2928, 2936, 4577, 4340, 4757, 4775, 4977, 3223, 3751, 4500, 3585, 2920, 3570, 3573, 3591, 4716, 4735, 4758, 4759, 4773, 933, 4076, 4079, 4250, 3692, 3696, 4790, 4850], "values": [0.99755859375, 0.99755859375, 0.99755859375, 0.99755859375, 0.95849609375, 0.78662109375, 0.78662109375, 0.7685546875, 0.68505859375, 0.673828125, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.6572265625, 0.63037109375, 0.63037109375, 0.63037109375, 0.6171875, 0.611328125, 0.60205078125, 0.6015625, 0.6015625, 0.6015625, 0.6015625, 0.60107421875, 0.60107421875, 0.60107421875, 0.60107421875, 0.6005859375, 0.6005859375, 0.59765625, 0.5908203125, 0.5859375, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.58251953125, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.5810546875, 0.57763671875, 0.5771484375, 0.5771484375, 0.5771484375, 0.5771484375, 0.5771484375, 0.5771484375, 0.5771484375, 0.57568359375, 0.57568359375, 0.57568359375, 0.56396484375, 0.56298828125, 0.55615234375, 0.55615234375, 0.54931640625, 0.54736328125, 0.54736328125, 0.54150390625, 0.53466796875, 0.5341796875, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.5234375, 0.52197265625, 0.51806640625, 0.51806640625, 0.51708984375, 0.51611328125, 0.51611328125, 0.51416015625, 0.513671875]}, "6": {"indices": [394, 350, 253, 254, 256, 257, 258, 937, 395, 1161, 255, 3170, 3346, 1124, 1611, 1612, 225, 437, 438, 439, 226, 61, 72, 73, 79, 3584, 3585, 3589, 3590, 14, 259, 3607, 1106, 1150, 1170, 3968, 197, 2901, 3179, 3180, 3181, 3183, 3191, 3192, 3243, 3244, 3341, 399, 936, 353, 1196, 1820, 32, 938, 944, 945, 2824, 85, 86, 2870, 57, 637, 888, 891, 2877, 712, 725, 641, 1037, 1877, 2607, 185, 2455, 4204, 4205, 4207, 4208, 4423, 3331, 3332, 1190, 2599, 2602, 2603, 2604, 3602, 227, 1819, 93, 4601, 3587, 591, 2247, 17, 970, 971, 1118, 3245, 3368, 4852], "values": [0.71533203125, 0.685546875, 0.6494140625, 0.6494140625, 0.6494140625, 0.6494140625, 0.6494140625, 0.64306640625, 0.6142578125, 0.54150390625, 0.5107421875, 0.495849609375, 0.49560546875, 0.484619140625, 0.468994140625, 0.468994140625, 0.457763671875, 0.448486328125, 0.448486328125, 0.448486328125, 0.44140625, 0.43359375, 0.43359375, 0.43359375, 0.43359375, 0.43310546875, 0.43310546875, 0.43310546875, 0.43310546875, 0.42822265625, 0.425537109375, 0.425537109375, 0.423828125, 0.423828125, 0.423828125, 0.416259765625, 0.416015625, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.414794921875, 0.411865234375, 0.410888671875, 0.408203125, 0.400146484375, 0.3984375, 0.397216796875, 0.396484375, 0.395751953125, 0.395751953125, 0.394775390625, 0.392822265625, 0.392822265625, 0.39111328125, 0.3896484375, 0.3896484375, 0.3896484375, 0.3896484375, 0.387451171875, 0.38720703125, 0.38720703125, 0.38525390625, 0.38427734375, 0.38427734375, 0.38427734375, 0.383056640625, 0.3828125, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.377685546875, 0.37548828125, 0.37548828125, 0.370361328125, 0.369140625, 0.369140625, 0.369140625, 0.369140625, 0.367919921875, 0.3671875, 0.364013671875, 0.360595703125, 0.35693359375, 0.354736328125, 0.354248046875, 0.354248046875, 0.353515625, 0.352783203125, 0.352783203125, 0.3515625, 0.35107421875, 0.35107421875, 0.348876953125]}, "7": {"indices": [54, 27, 31, 34, 32, 143, 33, 53, 66, 56, 61, 62, 35, 19, 63, 55, 49, 50, 124, 227, 47, 48, 26, 29, 28, 118, 51, 88, 57, 10, 119, 60, 46, 132, 133, 83, 204, 157, 158, 159, 160, 192, 74, 75, 76, 77, 109, 45, 97, 9, 52, 175, 111, 213, 59, 1, 72, 30, 178, 179, 180, 181, 182, 183, 18, 219, 220, 221, 44, 230, 73, 128, 170, 172, 173, 113, 123, 125, 114, 117, 214, 217, 58, 121, 194, 120, 116, 64, 200, 185, 130, 138, 186, 78, 233, 25, 232, 0, 15, 16], "values": [0.849609375, 0.6796875, 0.6181640625, 0.6181640625, 0.59423828125, 0.56884765625, 0.5458984375, 0.53857421875, 0.53466796875, 0.50537109375, 0.50537109375, 0.50537109375, 0.486572265625, 0.479736328125, 0.469970703125, 0.458984375, 0.44140625, 0.427734375, 0.427734375, 0.42578125, 0.412109375, 0.412109375, 0.408203125, 0.404541015625, 0.402099609375, 0.398193359375, 0.378173828125, 0.3671875, 0.36279296875, 0.358642578125, 0.357177734375, 0.352783203125, 0.3505859375, 0.348388671875, 0.348388671875, 0.345458984375, 0.34326171875, 0.3427734375, 0.3427734375, 0.3427734375, 0.3427734375, 0.33203125, 0.33154296875, 0.33154296875, 0.33154296875, 0.33154296875, 0.328369140625, 0.32470703125, 0.317138671875, 0.315185546875, 0.31396484375, 0.310302734375, 0.304443359375, 0.303466796875, 0.302001953125, 0.299560546875, 0.295654296875, 0.288818359375, 0.28564453125, 0.28564453125, 0.28564453125, 0.28564453125, 0.28564453125, 0.28564453125, 0.2744140625, 0.2724609375, 0.2724609375, 0.2724609375, 0.272216796875, 0.267333984375, 0.263671875, 0.262451171875, 0.255615234375, 0.255615234375, 0.255615234375, 0.251220703125, 0.244140625, 0.244140625, 0.242919921875, 0.240966796875, 0.2379150390625, 0.2322998046875, 0.22802734375, 0.2178955078125, 0.216796875, 0.216064453125, 0.2093505859375, 0.2080078125, 0.207763671875, 0.2076416015625, 0.2071533203125, 0.2071533203125, 0.19921875, 0.1962890625, 0.1962890625, 0.1951904296875, 0.19482421875, 0.192626953125, 0.1898193359375, 0.1898193359375]}, "8": {"indices": [475, 502, 604, 626, 633, 634, 635, 636, 643, 490, 1462, 1729, 640, 1377, 1600, 1601, 1602, 656, 441, 442, 443, 650, 161, 162, 554, 555, 159, 1973, 274, 277, 454, 1972, 451, 1810, 1959, 1195, 174, 175, 156, 1491, 1492, 2222, 501, 473, 474, 497, 498, 499, 157, 183, 923, 177, 178, 2031, 2032, 2043, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 1874, 1324, 1325, 1326, 1327, 680, 1408, 2004, 2712, 1328, 1687, 2957, 418, 506, 507, 508, 565, 566, 567, 603, 649, 1078, 1079, 1203, 1204, 954, 2013, 2022, 2041, 2068, 1081, 1138, 1232, 1240, 372, 1498], "values": [0.91552734375, 0.91552734375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87646484375, 0.87158203125, 0.81201171875, 0.7802734375, 0.7626953125, 0.7490234375, 0.67529296875, 0.67529296875, 0.67529296875, 0.66748046875, 0.64111328125, 0.64111328125, 0.64111328125, 0.6328125, 0.619140625, 0.619140625, 0.619140625, 0.619140625, 0.59228515625, 0.568359375, 0.56689453125, 0.56689453125, 0.54345703125, 0.5419921875, 0.52392578125, 0.51953125, 0.51953125, 0.51708984375, 0.51171875, 0.51171875, 0.501953125, 0.501953125, 0.501953125, 0.50146484375, 0.493408203125, 0.4931640625, 0.4931640625, 0.4931640625, 0.4931640625, 0.4931640625, 0.48828125, 0.48828125, 0.488037109375, 0.487060546875, 0.487060546875, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.484130859375, 0.4765625, 0.45947265625, 0.45947265625, 0.45947265625, 0.45947265625, 0.4501953125, 0.447265625, 0.447265625, 0.447265625, 0.44580078125, 0.439453125, 0.4375, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436767578125, 0.436279296875, 0.432861328125, 0.432861328125, 0.432861328125, 0.432861328125, 0.4326171875, 0.4326171875, 0.4326171875, 0.4326171875, 0.432373046875, 0.431640625]}, "9": {"indices": [1819, 4878, 4809, 4280, 4464, 2839, 2919, 2840, 3631, 2475, 2941, 2944, 752, 283, 2473, 2474, 212, 213, 220, 221, 687, 751, 4204, 1027, 422, 174, 278, 286, 873, 827, 835, 4165, 4383, 828, 834, 1075, 1076, 1077, 1339, 2957, 630, 4488, 2448, 4434, 4435, 2018, 755, 756, 1790, 2916, 4349, 4350, 4376, 4533, 4507, 866, 867, 4348, 4351, 772, 796, 2481, 2431, 2476, 2803, 2961, 1337, 277, 262, 624, 753, 754, 905, 749, 904, 1031, 2195, 2308, 4676, 1979, 189, 1944, 684, 686, 3389, 260, 263, 264, 2225, 872, 1902, 2772, 36, 3630, 1457, 4180, 4495, 4374, 258, 4753], "values": [0.998046875, 0.89404296875, 0.85546875, 0.79833984375, 0.79833984375, 0.669921875, 0.66162109375, 0.66064453125, 0.6435546875, 0.63525390625, 0.63525390625, 0.63525390625, 0.61865234375, 0.6181640625, 0.6181640625, 0.6181640625, 0.61474609375, 0.61474609375, 0.61474609375, 0.61474609375, 0.61474609375, 0.59619140625, 0.576171875, 0.5703125, 0.56201171875, 0.55712890625, 0.55712890625, 0.55322265625, 0.55078125, 0.54248046875, 0.54248046875, 0.53759765625, 0.53173828125, 0.525390625, 0.525390625, 0.525390625, 0.525390625, 0.525390625, 0.525390625, 0.521484375, 0.51416015625, 0.51416015625, 0.50537109375, 0.497802734375, 0.497802734375, 0.4970703125, 0.49609375, 0.49609375, 0.495361328125, 0.495361328125, 0.491455078125, 0.491455078125, 0.491455078125, 0.48828125, 0.484619140625, 0.477783203125, 0.477783203125, 0.472412109375, 0.472412109375, 0.466064453125, 0.46142578125, 0.46142578125, 0.458740234375, 0.454833984375, 0.454833984375, 0.454833984375, 0.452880859375, 0.45166015625, 0.44775390625, 0.44775390625, 0.44775390625, 0.44775390625, 0.44775390625, 0.439208984375, 0.439208984375, 0.428466796875, 0.428466796875, 0.428466796875, 0.428466796875, 0.425048828125, 0.422607421875, 0.4208984375, 0.409423828125, 0.409423828125, 0.40771484375, 0.403564453125, 0.403564453125, 0.403564453125, 0.4033203125, 0.399169921875, 0.399169921875, 0.3984375, 0.398193359375, 0.397216796875, 0.39404296875, 0.39404296875, 0.393798828125, 0.39306640625, 0.3916015625, 0.390625]}, "10": {"indices": [262, 267, 279, 462, 1346, 1665, 1039, 691, 1286, 1303, 1339, 1345, 1374, 1385, 1401, 1501, 1525, 1547, 1668, 1653, 1654, 1666, 273, 277, 278, 1521, 1553, 1565, 648, 754, 794, 1656, 1697, 1699, 1702, 1192, 1333, 1340, 501, 730, 679, 685, 792, 308, 446, 465, 468, 523, 581, 582, 585, 628, 641, 642, 647, 654, 674, 686, 689, 711, 714, 715, 786, 791, 1671, 1673, 1445, 570, 502, 795, 884, 1472, 1524, 1548, 1691, 1669, 1751, 1335, 1672, 506, 520, 340, 1424, 1621, 482, 524, 1677, 1344, 123, 334, 1221, 1252, 1279, 1283, 503, 637, 672, 751, 759, 763], "values": [0.998046875, 0.998046875, 0.998046875, 0.90185546875, 0.90185546875, 0.90185546875, 0.892578125, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.884765625, 0.86962890625, 0.86962890625, 0.86962890625, 0.869140625, 0.869140625, 0.869140625, 0.865234375, 0.865234375, 0.865234375, 0.8642578125, 0.8642578125, 0.8642578125, 0.86083984375, 0.8515625, 0.8515625, 0.8515625, 0.85107421875, 0.85009765625, 0.85009765625, 0.8486328125, 0.8486328125, 0.8447265625, 0.8447265625, 0.8447265625, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.84375, 0.83935546875, 0.83935546875, 0.83740234375, 0.8369140625, 0.8359375, 0.8349609375, 0.8349609375, 0.83349609375, 0.830078125, 0.830078125, 0.82763671875, 0.8251953125, 0.8251953125, 0.82421875, 0.82080078125, 0.8173828125, 0.8173828125, 0.81689453125, 0.81689453125, 0.81494140625, 0.80615234375, 0.8056640625, 0.8056640625, 0.8017578125, 0.80126953125, 0.7978515625, 0.796875, 0.796875, 0.796875, 0.796875, 0.7958984375, 0.7958984375, 0.7958984375, 0.7958984375, 0.7958984375, 0.7958984375]}, "11": {"indices": [457, 1613, 553, 557, 558, 577, 587, 761, 1060, 478, 1651, 1685, 447, 452, 458, 1400, 1486, 1487, 1489, 1754, 1447, 1463, 545, 945, 946, 953, 1533, 1012, 1338, 1189, 514, 1040, 1571, 1745, 1779, 480, 1580, 1615, 490, 519, 965, 484, 493, 494, 495, 499, 500, 507, 521, 550, 552, 1342, 1386, 1402, 1474, 1502, 1526, 1538, 1539, 1540, 1602, 829, 1378, 1205, 511, 571, 1535, 1550, 1568, 1624, 1627, 1630, 1631, 1648, 1659, 1734, 383, 562, 1718, 141, 1271, 1272, 101, 102, 124, 125, 126, 35, 43, 32, 139, 140, 147, 162, 526, 1579, 1720, 1730, 1731, 1752], "values": [0.998046875, 0.96826171875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.95263671875, 0.94970703125, 0.94970703125, 0.9404296875, 0.9287109375, 0.9287109375, 0.9287109375, 0.92529296875, 0.92529296875, 0.92529296875, 0.92529296875, 0.91845703125, 0.90673828125, 0.90673828125, 0.89794921875, 0.8955078125, 0.8955078125, 0.8955078125, 0.89404296875, 0.8876953125, 0.88720703125, 0.8857421875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.8701171875, 0.86083984375, 0.86083984375, 0.86083984375, 0.84912109375, 0.84912109375, 0.84912109375, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84765625, 0.84619140625, 0.83984375, 0.8291015625, 0.82373046875, 0.82373046875, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82177734375, 0.82080078125, 0.81591796875, 0.8154296875, 0.814453125, 0.81396484375, 0.8134765625, 0.8134765625, 0.8125, 0.8125, 0.8125, 0.8125, 0.8125, 0.8115234375, 0.8115234375, 0.80908203125, 0.80419921875, 0.80419921875, 0.80419921875, 0.8037109375, 0.802734375, 0.802734375, 0.802734375, 0.802734375, 0.802734375, 0.802734375]}, "12": {"indices": [228, 229, 230, 222, 223, 224, 225, 231, 174, 202, 189, 173, 187, 214, 304, 334, 365, 366, 74, 77, 80, 284, 237, 227, 20, 132, 203, 37, 180, 150, 364, 243, 110, 276, 277, 278, 281, 362, 47, 391, 392, 393, 394, 395, 396, 130, 200, 196, 151, 213, 118, 72, 354, 355, 215, 66, 380, 181, 282, 6, 34, 35, 36, 68, 381, 382, 116, 117, 190, 157, 158, 359, 60, 179, 326, 43, 251, 63, 160, 161, 178, 254, 156, 48, 144, 121, 123, 124, 100, 58, 32, 106, 112, 147, 153, 191, 233, 129, 114, 115], "values": [0.8251953125, 0.8251953125, 0.8251953125, 0.66015625, 0.66015625, 0.66015625, 0.654296875, 0.654296875, 0.50537109375, 0.487060546875, 0.467529296875, 0.44970703125, 0.43798828125, 0.434326171875, 0.433349609375, 0.424072265625, 0.424072265625, 0.424072265625, 0.415771484375, 0.415771484375, 0.408935546875, 0.3994140625, 0.38427734375, 0.381591796875, 0.376953125, 0.369140625, 0.36181640625, 0.3525390625, 0.350830078125, 0.346435546875, 0.337646484375, 0.33642578125, 0.33544921875, 0.3349609375, 0.3349609375, 0.3349609375, 0.3349609375, 0.33154296875, 0.32421875, 0.32275390625, 0.32275390625, 0.32275390625, 0.32275390625, 0.32275390625, 0.32275390625, 0.321533203125, 0.321533203125, 0.32080078125, 0.31396484375, 0.31396484375, 0.313232421875, 0.31201171875, 0.311767578125, 0.311767578125, 0.309814453125, 0.3056640625, 0.3056640625, 0.30517578125, 0.302001953125, 0.301025390625, 0.2978515625, 0.2978515625, 0.2978515625, 0.297607421875, 0.29736328125, 0.29736328125, 0.297119140625, 0.297119140625, 0.28857421875, 0.28662109375, 0.28662109375, 0.28564453125, 0.28369140625, 0.2802734375, 0.279052734375, 0.27587890625, 0.271240234375, 0.2685546875, 0.268310546875, 0.268310546875, 0.268310546875, 0.268310546875, 0.267578125, 0.265869140625, 0.263671875, 0.26318359375, 0.26318359375, 0.26318359375, 0.25927734375, 0.25830078125, 0.2578125, 0.2568359375, 0.2568359375, 0.2568359375, 0.2568359375, 0.2568359375, 0.252685546875, 0.2509765625, 0.25048828125, 0.25048828125]}, "13": {"indices": [275, 274, 272, 276, 273, 537, 513, 277, 578, 529, 530, 573, 574, 575, 576, 2836, 1864, 2478, 2832, 2833, 498, 1003, 1031, 1033, 1034, 1035, 1036, 1038, 1043, 1044, 1048, 1049, 1050, 1051, 1053, 1054, 1055, 229, 230, 607, 2875, 293, 533, 531, 2373, 298, 602, 296, 2525, 526, 1067, 1070, 1381, 2458, 442, 279, 291, 2838, 1990, 1991, 1992, 2882, 2884, 2885, 2843, 1164, 348, 349, 350, 363, 365, 370, 371, 716, 717, 254, 2839, 2611, 528, 3040, 270, 1026, 1775, 1776, 2259, 1030, 1032, 1037, 1039, 1040, 1042, 1045, 1046, 1047, 1641, 1642, 1643, 2568, 2569, 2128], "values": [0.64111328125, 0.5791015625, 0.56494140625, 0.55712890625, 0.55322265625, 0.54833984375, 0.537109375, 0.533203125, 0.5302734375, 0.50341796875, 0.50341796875, 0.50341796875, 0.50341796875, 0.482177734375, 0.482177734375, 0.48046875, 0.47900390625, 0.473876953125, 0.469970703125, 0.469970703125, 0.468017578125, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.4619140625, 0.457275390625, 0.457275390625, 0.454345703125, 0.44287109375, 0.438232421875, 0.432373046875, 0.43115234375, 0.4287109375, 0.424072265625, 0.4228515625, 0.421630859375, 0.4208984375, 0.418701171875, 0.418701171875, 0.418701171875, 0.415283203125, 0.415283203125, 0.408935546875, 0.408447265625, 0.40185546875, 0.39990234375, 0.39892578125, 0.39892578125, 0.39892578125, 0.39599609375, 0.39599609375, 0.39599609375, 0.393310546875, 0.39306640625, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.39111328125, 0.389892578125, 0.38671875, 0.38623046875, 0.38525390625, 0.385009765625, 0.384765625, 0.384765625, 0.38427734375, 0.38427734375, 0.3837890625, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3818359375, 0.3798828125, 0.3798828125, 0.3798828125, 0.378662109375, 0.378662109375, 0.37841796875]}, "14": {"indices": [291, 2328, 484, 485, 2404, 293, 273, 2397, 272, 271, 2006, 2007, 2011, 2012, 2016, 2017, 526, 1067, 1070, 2912, 292, 1598, 495, 557, 2568, 2569, 2472, 2521, 2886, 2339, 506, 298, 304, 1063, 1064, 1065, 748, 575, 576, 284, 285, 286, 287, 288, 303, 458, 296, 305, 750, 2523, 2525, 300, 901, 2402, 2403, 489, 2163, 302, 269, 957, 743, 744, 745, 746, 420, 477, 478, 539, 504, 294, 295, 747, 749, 2111, 2112, 2008, 2009, 2010, 297, 280, 281, 314, 789, 229, 230, 2366, 770, 290, 474, 2687, 2688, 1117, 275, 703, 2122, 2838, 317, 2372, 279, 448], "values": [0.63232421875, 0.62451171875, 0.6083984375, 0.6083984375, 0.59716796875, 0.59130859375, 0.58837890625, 0.58349609375, 0.57177734375, 0.57080078125, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.54638671875, 0.54296875, 0.54296875, 0.54296875, 0.52294921875, 0.5068359375, 0.50341796875, 0.5029296875, 0.49951171875, 0.49755859375, 0.49755859375, 0.49365234375, 0.490478515625, 0.489013671875, 0.48876953125, 0.48779296875, 0.487060546875, 0.47900390625, 0.47705078125, 0.47705078125, 0.47705078125, 0.47021484375, 0.468994140625, 0.468994140625, 0.4677734375, 0.4677734375, 0.4677734375, 0.4677734375, 0.4677734375, 0.464599609375, 0.45947265625, 0.45703125, 0.4541015625, 0.453125, 0.453125, 0.453125, 0.4482421875, 0.44677734375, 0.446044921875, 0.446044921875, 0.43896484375, 0.437255859375, 0.4326171875, 0.42138671875, 0.419677734375, 0.417236328125, 0.417236328125, 0.417236328125, 0.417236328125, 0.4169921875, 0.4169921875, 0.4169921875, 0.4140625, 0.413818359375, 0.41357421875, 0.41357421875, 0.4130859375, 0.4130859375, 0.41015625, 0.41015625, 0.4072265625, 0.4072265625, 0.4072265625, 0.406494140625, 0.405517578125, 0.405517578125, 0.4013671875, 0.4013671875, 0.39599609375, 0.39599609375, 0.392822265625, 0.39208984375, 0.391357421875, 0.389892578125, 0.389404296875, 0.389404296875, 0.386962890625, 0.385986328125, 0.383544921875, 0.38037109375, 0.380126953125, 0.37841796875, 0.378173828125, 0.377197265625, 0.373291015625]}, "15": {"indices": [570, 2770, 483, 2106, 245, 246, 249, 250, 251, 252, 253, 2457, 2406, 2407, 1378, 1380, 1599, 1600, 773, 255, 260, 261, 1041, 1052, 475, 486, 1117, 191, 192, 789, 742, 2369, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 461, 524, 525, 361, 422, 1208, 1813, 923, 1294, 1295, 1333, 2199, 2215, 2216, 2217, 2712, 2157, 259, 566, 572, 1124, 366, 2065, 2625, 1265, 1266, 1281, 927, 928, 1029, 1181, 1182, 1183, 2564, 1838, 1857, 1661, 2777, 1071, 1072, 1704, 1763, 1764, 1765, 1766, 1767, 2520, 2332, 926, 431, 2866, 2609, 1545, 442, 1403, 999, 385, 1123, 2504, 2558, 2559], "values": [0.67919921875, 0.60986328125, 0.60400390625, 0.5341796875, 0.498291015625, 0.498291015625, 0.498291015625, 0.498291015625, 0.498291015625, 0.498291015625, 0.498291015625, 0.490234375, 0.482666015625, 0.482666015625, 0.4697265625, 0.4697265625, 0.4482421875, 0.4482421875, 0.44384765625, 0.443115234375, 0.443115234375, 0.443115234375, 0.43408203125, 0.43408203125, 0.431396484375, 0.431396484375, 0.423095703125, 0.421630859375, 0.421630859375, 0.41943359375, 0.4169921875, 0.4130859375, 0.412109375, 0.412109375, 0.412109375, 0.412109375, 0.412109375, 0.412109375, 0.412109375, 0.411376953125, 0.411376953125, 0.411376953125, 0.410888671875, 0.410888671875, 0.410888671875, 0.407958984375, 0.406494140625, 0.406494140625, 0.406494140625, 0.406494140625, 0.40625, 0.40625, 0.40625, 0.40625, 0.405517578125, 0.404541015625, 0.400634765625, 0.39990234375, 0.39990234375, 0.399658203125, 0.397216796875, 0.396484375, 0.39599609375, 0.395751953125, 0.395751953125, 0.395751953125, 0.39453125, 0.39453125, 0.394287109375, 0.39013671875, 0.39013671875, 0.39013671875, 0.389892578125, 0.38720703125, 0.38720703125, 0.384521484375, 0.384521484375, 0.38427734375, 0.38427734375, 0.384033203125, 0.384033203125, 0.384033203125, 0.384033203125, 0.384033203125, 0.384033203125, 0.382080078125, 0.381591796875, 0.37939453125, 0.379150390625, 0.375244140625, 0.373046875, 0.371337890625, 0.370849609375, 0.3701171875, 0.367431640625, 0.365478515625, 0.365234375, 0.36376953125, 0.36376953125, 0.36376953125]}, "16": {"indices": [380, 202, 3432, 3343, 3347, 4445, 2658, 2884, 2885, 2890, 2892, 1879, 1880, 3464, 852, 1883, 1885, 1888, 1889, 4887, 3337, 3900, 891, 3899, 4662, 793, 991, 3972, 3973, 4669, 3155, 3909, 111, 112, 113, 114, 364, 1895, 3970, 1882, 1890, 1891, 1832, 3975, 3977, 3978, 1881, 1827, 33, 43, 2020, 2480, 2484, 3235, 3979, 4169, 3338, 3340, 4609, 990, 2886, 359, 4865, 1903, 3966, 1062, 4551, 4595, 4660, 4663, 1972, 3334, 1016, 1860, 2016, 1141, 1142, 3145, 3976, 4854, 945, 795, 796, 800, 881, 4425, 785, 786, 4580, 2593, 3778, 4171, 4172, 116, 196, 2340, 2353, 2354, 2355, 4890], "values": [0.89208984375, 0.65234375, 0.646484375, 0.611328125, 0.611328125, 0.60546875, 0.5986328125, 0.59814453125, 0.59814453125, 0.59814453125, 0.59814453125, 0.58740234375, 0.58740234375, 0.5595703125, 0.5556640625, 0.5556640625, 0.5556640625, 0.5556640625, 0.5556640625, 0.5537109375, 0.55126953125, 0.54296875, 0.5419921875, 0.5419921875, 0.5419921875, 0.5400390625, 0.5400390625, 0.5400390625, 0.5400390625, 0.5400390625, 0.53515625, 0.5341796875, 0.533203125, 0.533203125, 0.533203125, 0.533203125, 0.533203125, 0.5302734375, 0.529296875, 0.52880859375, 0.52880859375, 0.52880859375, 0.52392578125, 0.52392578125, 0.52392578125, 0.52392578125, 0.521484375, 0.52099609375, 0.51904296875, 0.51904296875, 0.51904296875, 0.517578125, 0.517578125, 0.51708984375, 0.51025390625, 0.50830078125, 0.50732421875, 0.50732421875, 0.50634765625, 0.50439453125, 0.50439453125, 0.499267578125, 0.490966796875, 0.489501953125, 0.488525390625, 0.488037109375, 0.4873046875, 0.4873046875, 0.4873046875, 0.4873046875, 0.487060546875, 0.48583984375, 0.485107421875, 0.4833984375, 0.483154296875, 0.48193359375, 0.48193359375, 0.480712890625, 0.478759765625, 0.476318359375, 0.475341796875, 0.47509765625, 0.47509765625, 0.47509765625, 0.473876953125, 0.473876953125, 0.47314453125, 0.47314453125, 0.470703125, 0.468017578125, 0.46533203125, 0.464599609375, 0.464599609375, 0.4638671875, 0.4638671875, 0.4638671875, 0.4638671875, 0.4638671875, 0.4638671875, 0.46337890625]}, "17": {"indices": [29, 24, 18, 8, 10, 6, 7, 19, 20, 22, 31, 11, 12, 25, 32, 21, 27, 23, 26, 28, 3, 9, 34, 35, 4, 1, 2, 5, 30, 0, 33, 13, 14, 15, 16, 17], "values": [0.50439453125, 0.499755859375, 0.4697265625, 0.450439453125, 0.450439453125, 0.4384765625, 0.4384765625, 0.4384765625, 0.4384765625, 0.4365234375, 0.416015625, 0.4072265625, 0.4072265625, 0.387939453125, 0.387939453125, 0.3759765625, 0.338134765625, 0.325927734375, 0.31494140625, 0.306640625, 0.281982421875, 0.281494140625, 0.271484375, 0.271484375, 0.26513671875, 0.2255859375, 0.2255859375, 0.209228515625, 0.18505859375, 0.13818359375, 0.06573486328125, 0.0, 0.0, 0.0, 0.0, 0.0]}, "18": {"indices": [18, 24, 22, 26, 6, 7, 19, 20, 8, 10, 21, 30, 28, 31, 27, 23, 9, 29, 1, 2, 25, 5, 4, 11, 12, 34, 35, 3, 32, 0, 33, 13, 14, 15, 16, 17], "values": [0.70361328125, 0.63671875, 0.62890625, 0.60888671875, 0.576171875, 0.576171875, 0.576171875, 0.576171875, 0.55419921875, 0.55419921875, 0.5380859375, 0.5185546875, 0.517578125, 0.51611328125, 0.4931640625, 0.48876953125, 0.48388671875, 0.45703125, 0.431640625, 0.431640625, 0.423828125, 0.418212890625, 0.408203125, 0.39306640625, 0.39306640625, 0.385498046875, 0.385498046875, 0.331787109375, 0.312744140625, 0.26513671875, 0.1339111328125, 0.0, 0.0, 0.0, 0.0, 0.0]}, "19": {"indices": [20, 62, 63, 81, 165, 53, 54, 163, 88, 89, 113, 59, 61, 107, 121, 208, 9, 197, 212, 57, 45, 203, 5, 213, 64, 217, 8, 65, 66, 72, 60, 71, 177, 182, 210, 211, 144, 145, 146, 192, 174, 4, 6, 7, 128, 228, 48, 83, 196, 245, 122, 200, 76, 191, 0, 183, 115, 67, 84, 114, 116, 56, 82, 16, 18, 205, 91, 215, 201, 199, 25, 246, 112, 207, 124, 126, 143, 198, 214, 235, 209, 181, 24, 204, 17, 73, 135, 193, 249, 216, 236, 185, 239, 237, 105, 247, 130, 187, 186, 1], "values": [0.81640625, 0.79150390625, 0.79150390625, 0.55224609375, 0.5478515625, 0.499267578125, 0.499267578125, 0.498291015625, 0.49365234375, 0.49365234375, 0.46484375, 0.459228515625, 0.4189453125, 0.397705078125, 0.36083984375, 0.35888671875, 0.351318359375, 0.35107421875, 0.3486328125, 0.342529296875, 0.337646484375, 0.302001953125, 0.30078125, 0.298583984375, 0.290283203125, 0.28369140625, 0.282958984375, 0.2685546875, 0.2685546875, 0.240234375, 0.2366943359375, 0.231689453125, 0.2310791015625, 0.2254638671875, 0.22265625, 0.22265625, 0.21875, 0.21875, 0.21875, 0.2115478515625, 0.2080078125, 0.204833984375, 0.204833984375, 0.204833984375, 0.2015380859375, 0.1951904296875, 0.1907958984375, 0.18017578125, 0.1795654296875, 0.172607421875, 0.1693115234375, 0.1683349609375, 0.16455078125, 0.162841796875, 0.162109375, 0.159423828125, 0.1583251953125, 0.1568603515625, 0.151123046875, 0.146728515625, 0.146728515625, 0.1451416015625, 0.1451416015625, 0.1441650390625, 0.1441650390625, 0.1424560546875, 0.1422119140625, 0.13671875, 0.1318359375, 0.1300048828125, 0.1298828125, 0.1229248046875, 0.12152099609375, 0.12127685546875, 0.12005615234375, 0.11767578125, 0.11767578125, 0.11358642578125, 0.113037109375, 0.11224365234375, 0.1082763671875, 0.10791015625, 0.10687255859375, 0.1004638671875, 0.1002197265625, 0.09991455078125, 0.09637451171875, 0.0928955078125, 0.09234619140625, 0.092041015625, 0.0906982421875, 0.0885009765625, 0.083984375, 0.07891845703125, 0.07672119140625, 0.07452392578125, 0.07440185546875, 0.0723876953125, 0.07000732421875, 0.0689697265625]}}

for i in range(20):
    e1 = v1[str(i)]
    e2 = v2[str(i)]
    print(i, 'indices:')
    print(e1['indices'])
    print(e2['indices'])
    print(i, 'values:')
    print(e1['values'])
    print(e2['values'])


for i in """function process_fun()\n              local channel = 0\n              channel = (p2l_label & 3)\n\n              local time_int = math.floor(tonumber(_REQ_TIME_) / 1000.0) - 60\n              return ((p2l_photo_play_time<<24) | p2l_photo_duration), math.floor((time_int - p2l_timestamp) / 86400), ((p2l_photo_play_time<<24) | p2l_live_play_time), channel\n            end",
          """.splitlines():
    print(f'{i}')

import tensorflow as tf
import numpy as np

def target_attention_absorb(query, key, value, num_head, head_dim, name):
    # 针对 query 的长度为 1 的特殊情况优化
    """
    Multi-head target attention absorb
    query: [batch_size, query_dim]
    key: [batch_size, seq_len, key_dim]
    value: [batch_size, seq_len, value_dim]
    """
    seq_len = int(key.shape[1])

    # 投影矩阵
    Q = tf.get_variable(name + '_q_trans_matrix', (int(query.shape[-1]), num_head*head_dim))
    K = tf.get_variable(name + '_k_trans_matrix', (int(key.shape[-1]), num_head*head_dim))
    V = tf.get_variable(name + '_v_trans_matrix', (int(value.shape[-1]), num_head*head_dim))
    
    # 投影key和value
    k_proj = tf.reshape(K, [-1, num_head, head_dim]) # [k, n, d]
    v_proj = tf.reshape(V, [-1, num_head, head_dim]) # [k, n, d]
    k_proj = tf.transpose(k_proj, perm=[1, 2, 0])  # [n, d, k]
    v_proj = tf.transpose(v_proj, perm=[1, 0, 2])  # [n, k, d]

    # 计算矩阵吸收公式 softmax(q * Q * K(T) * k(T)) * v * V(T)
    # q * Q
    query_trans = tf.matmul(query, Q) * (head_dim ** (-0.5)) # [B, n * d]
    query_trans = tf.reshape(query_trans, [-1, num_head, head_dim]) # [B, n, d]
    # (q * Q) * K(T)
    query_trans = tf.transpose(query_trans, perm=[1, 0, 2])  # [n, B, d]
    qk_absorb = tf.matmul(query_trans, k_proj) # [n, B, k]
    qk_absorb = tf.transpose(qk_absorb, perm=[1, 0, 2])  # [B, n, k]
    # (q * Q) * K(T) * k(T)
    
    key = tf.transpose(key, perm=[0, 2, 1])  # [B, k, s]
    qk_dot = tf.matmul(qk_absorb, key)  # [B, n, s]
    # softmax
    qk_dot = tf.nn.softmax(qk_dot, axis=-1) #[B, n, s]
    #  softmax(q * Q * K(T) * k(T)) * v
    mha_output = tf.matmul(qk_dot, value)  # [B, n, k]
    mha_output = tf.transpose(mha_output, perm=[1, 0, 2])  # [n, B, k]
    # mha_output * V(T)
    mha_output = tf.matmul(mha_output, v_proj)  # [n, B, d]
    mha_output = tf.reshape(tf.transpose(mha_output, perm=[1, 0, 2]), (-1, num_head * head_dim))
    return mha_output  # [B, n * d]

def target_attention_absorb_v1(query, key, value, num_head, head_dim, name):
    """
    Multi-head target attention absorb
    query: [batch_size, query_dim]
    key: [batch_size, seq_len, key_dim]
    value: [batch_size, seq_len, value_dim]
    """
    seq_len = int(key.shape[1])

    Q = tf.get_variable(name + '_q_trans_matrix', (int(query.shape[-1]), num_head*head_dim))  # [emb, att_emb * hn]
    K = tf.get_variable(name + '_k_trans_matrix', (int(key.shape[-1]), num_head*head_dim))
    V = tf.get_variable(name + '_v_trans_matrix', (int(value.shape[-1]), num_head*head_dim))
    K_proj = tf.reshape(K, [-1, num_head, head_dim])
    V_proj = tf.reshape(V, [-1, num_head, head_dim])

    query_trans = tf.tensordot(query, Q, axes=(-1, 0))
    query_trans = query_trans * (head_dim ** (-0.5))
    query_trans = tf.reshape(query_trans, [-1, num_head, head_dim]) #[batch_size, num_head, head_dim]
    q_k_absorb = tf.einsum('bnd, knd -> bnk', query_trans, K_proj) ##[batch_size, num_head, k_dim]
    
    qk_dot = tf.einsum('bnk, bsk -> bns', q_k_absorb, key)
    qk_dot = tf.nn.softmax(qk_dot, axis=-1)
    
    mha_output = tf.einsum('bns, bsk -> bnk', qk_dot, value)
    result = tf.einsum('bnk, knd -> bnd', mha_output, V_proj)
    result = tf.reshape(result, [-1, num_head*head_dim])
    return result


if __name__ == '__main__':
    B, S, Q_DIM, K_DIM, V_DIM, NH, HD = 3, 7, 32, 64, 64, 8, 8
    query_ph = tf.placeholder(tf.float32, [B, Q_DIM])
    key_ph   = tf.placeholder(tf.float32, [B, S, K_DIM])
    value_ph = tf.placeholder(tf.float32, [B, S, V_DIM])

    with tf.variable_scope("absorb", reuse=tf.AUTO_REUSE):
        out0 = target_attention_absorb(query_ph, key_ph, value_ph, NH, HD, "absorb")
        out1 = target_attention_absorb_v1(query_ph, key_ph, value_ph, NH, HD, "absorb")
    
    # 计算差值
    diff = tf.reduce_max(tf.abs(out0 - out1))
    np.random.seed(123)
    q_np = np.random.randn(B, Q_DIM).astype(np.float32)
    k_np = np.random.randn(B, S, K_DIM).astype(np.float32)
    v_np = np.random.randn(B, S, V_DIM).astype(np.float32)

    # 运行
    with tf.Session() as sess:
        sess.run(tf.global_variables_initializer())
        d = sess.run(diff, feed_dict={query_ph: q_np,
                                    key_ph:   k_np,
                                    value_ph: v_np})
        print("max(|out0 - out1|) =", d)
        if d < 1e-5:
            print("✅ 两个函数输出等价")
        else:
            print("❌ 两个函数输出不等价")

import numpy as np

def main(n):
    n = np.int64(n)
    sign = np.uint64(n)
    print(n,sign)

for i in [-4035225266123964416, -4035225266123964415, -4035225266123964414, -4035225266123964413, -4035225266123964412]:
    main(i)

t= [i/10 for i in range(24*10)]

def cal_route_weight(t):
    last_idx = int(t//2 * 2) % 24
    next_idx = int(last_idx + 1) % 24
    ratio = t%2 / 2
    weight = [0] * 24
    weight[last_idx] = 1 - ratio
    weight[next_idx] = ratio
    return weight

for i in t:
    print(i, cal_route_weight(i))

text = """column_name=ExtractCombineSparseInnerOrderCvr, compression_type = None,column_type = List<Long>,feature_extractor_flag = true
column_name=ExtractUserSparseInnerOrderRankTopkPhotoId,compression_type = None,column_type = List<Long>,feature_extractor_flag = true
column_name=ExtractUserSparseInnerOrderRankTopkCvr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true
column_name=ExtractUserSparseInnerOrderRankTopkCpr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true
column_name=ExtractUserSparseInnerOrderRankTopkLvtr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true
column_name=ExtractUserSparseInnerOrderRankTopkWtr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true""".splitlines()

for t in text:
    print(f"class={t.split(',')[0].split('=')[-1]}, category=combine, field=611, size=0, topic_id=0")


text= """column_name=good_click_softsearch_real_seller_id_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_cate1_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_cate2_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_cate3_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_click_type_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_from_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_page_view_time_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_label_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_lag_hour_list_top100, compression_type=None, column_type=List<Long>
column_name=good_click_softsearch_lag_min_list_top100, compression_type=None, column_type=List<Long>
column_name = good_show_soft_category1, compression_type = None, column_type = List<Long>
column_name = good_show_soft_category2, compression_type = None, column_type = List<Long>
column_name = good_show_soft_category3, compression_type = None, column_type = List<Long>
column_name = good_show_soft_category4, compression_type = None, column_type = List<Long>
column_name = good_show_soft_hour_of_day, compression_type = None, column_type = List<Long>
column_name = good_show_soft_seller_id, compression_type = None, column_type = List<Long>
column_name = good_show_soft_leaf_category, compression_type = None, column_type = List<Long>
column_name = good_show_soft_time_gap_hour, compression_type = None, column_type = List<Long>""".splitlines()

for t in text:
    print(f"{t.split(',')[0].split('=')[-1].strip()},")
len(text)

s = """sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,
            good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,
            colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,
            eshop_ad,
            good_click_cate2cate_cate1_list_extend, good_click_cate2cate_lag_hour_list_extend, good_click_cate2cate_click_type_list_extend, good_click_cate2cate_index_list_extend, 
            cart_photo_exposure_pid_list_expand, cart_photo_exposure_aid_list_expand, cart_photo_exposure_duration_list_expand, cart_photo_exposure_play_time_list_expand, cart_photo_exposure_channel_list_expand, cart_photo_exposure_spu_id_list_expand, cart_photo_exposure_category_list_expand,
            request_time_hour""".split(',')
len(s)

origin = [23317615620954, 24475918632703, 23317615620954, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24236034362185, 24374148590371, 24209602837038, 23444521027181, 21211897705494, 21211897705494, 24538482737856, 24053945919483, 21824885568053, 22207542224046, 21578668853102, 21965652178971, 23795042093290, 20685424113641, 22191424512515, 21751660769847, 23959999877582, 21765947400603, 22710951294491, 21170151035108, 21170151035108]
a = [21, 0, 2, 23, 10, 16, 17, 14, 24, 12, 11, 7, 4, 3, 5, 6, 13, 19, 15, 18, 9, 20, 1, 8, 27, 26, 22, 64, 28, 30, 31, 29, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 96, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 112, 97, 98, 99]
b = [origin[i] for i in a if i < 28]
cnt = ([i for i in a if i < 28])
ref = [22191424512515, 23317615620954, 23317615620954, 23959999877582, 23444521027181, 22207542224046, 21578668853102, 24053945919483, 21765947400603, 21211897705494, 21211897705494, 24236034362185, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24538482737856, 23795042093290, 21824885568053, 21965652178971, 24209602837038, 20685424113641, 24475918632703, 24374148590371, 21170151035108, 21170151035108, 21751660769847]
print(b)
print(ref)
list(range(28))==sorted([21, 0, 2, 23, 10, 16, 17, 14, 24, 12, 11, 3, 4, 7, 5, 6, 13, 18, 19, 9, 15, 1, 20, 27, 26, 8, 22, 25])

a = """new_good_click_item_id_list_extend, new_good_click_seller_id_list_extend, new_good_click_real_seller_id_list_extend, new_good_click_lag_list_extend,
                new_good_click_cate1_list_extend, new_good_click_cate2_list_extend,
                new_good_click_cate3_list_extend, new_good_click_category_list_extend,new_good_click_carry_type_list_extend, new_good_click_click_type_list_extend,
                new_good_click_from_list_extend, new_good_click_price_list_extend,
                new_good_click_page_view_time_list_extend, new_good_click_label_list_extend,
                new_good_click_uniform_spu_id_list_extend, new_good_click_item_count_list,
                new_good_click_lag_hour_list_extend, new_good_click_lag_min_list_extend,
                new_good_click_seller_id_lag_list_extend, new_good_click_cate1_lag_list_extend, new_good_click_cate2_lag_list_extend, new_good_click_cate3_lag_list_extend,
                new_good_click_seller_id_price_list_extend, new_good_click_cate1_price_list_extend, new_good_click_cate2_price_list_extend, new_good_click_cate3_price_list_extend,
                new_good_click_seller_id_view_list_extend, new_good_click_cate1_view_list_extend, new_good_click_cate2_view_list_extend, new_good_click_cate3_view_list_extend
            """.split(",")
res = []
for t in a:
    res.append(t.strip().replace('new_','').replace('\n','').replace('_list_extend','_list_top100'))
res

print("""""function cal_price(ori_price)\n            local res = 0\n            res = ori_price // 100\n            if res >= 300 then res = 300\n            end\n            if res <0 then res = 0\n            end\n            return res\n        end\n\n        function cal_diff_price(ori_price, price)\n            if ori_price == 0 then return 0\n            end\n            local slice = 100\n            local res = ori_price - price\n            res = res * slice // ori_price\n            if res <0 then res = 0\n            end\n            if res >slice then res = slice\n            end\n            return res\n        end\n\n        function cal_view_time(view_time)\n            local res = view_time\n            if res <0 then res = 0\n            end\n            if res > 60 then res = 60\n            end\n            return res\n        end\n\n        function cal_cross(item1, item2)\n            return tostring(item1)..\"_\"..tostring(item2)\n        end\n\n        function calculate()\n            local good_click_cate2cate_cate1_list = good_click_cate2cate_cate1_list or {}\n            local good_click_cate2cate_cate2_list = good_click_cate2cate_cate2_list or {}\n            local good_click_cate2cate_cate3_list = good_click_cate2cate_cate3_list or {}\n            local good_click_cate2cate_category_list = good_click_cate2cate_category_list or {}\n            local good_click_cate2cate_click_from_list = good_click_cate2cate_click_from_list or {}\n            local good_click_cate2cate_click_index_list = good_click_cate2cate_click_index_list or {}\n            local good_click_cate2cate_item_id_list = good_click_cate2cate_item_id_list or {}\n            local good_click_cate2cate_lag_list = good_click_cate2cate_lag_list or {}\n            local good_click_cate2cate_real_price_list = good_click_cate2cate_real_price_list or {}\n            local good_click_cate2cate_real_seller_id_list = good_click_cate2cate_real_seller_id_list or {}\n            local good_click_cate2cate_seller_id_list = good_click_cate2cate_seller_id_list or {}\n            local good_click_cate2cate_timestamp_list = good_click_cate2cate_timestamp_list or {}\n\n            local new_good_click_cate2cate_item_id_list_extend = {}\n            local new_good_click_cate2cate_seller_id_list_extend = {}\n            local new_good_click_cate2cate_real_seller_id_list_extend = {}\n            local new_good_click_cate2cate_lag_list_extend = {}\n            local new_good_click_cate2cate_cate1_list_extend = {}\n            local new_good_click_cate2cate_cate2_list_extend = {}\n            local new_good_click_cate2cate_cate3_list_extend = {}\n            local new_good_click_cate2cate_category_list_extend = {}\n            local new_good_click_cate2cate_carry_type_list_extend = {}\n            local new_good_click_cate2cate_click_type_list_extend = {}\n            local new_good_click_cate2cate_click_from_list_extend = {}\n            local new_good_click_cate2cate_real_price_list_extend = {}\n            local new_good_click_cate2cate_index_list_extend = {}\n            local new_good_click_cate2cate_lag_hour_list_extend = {}\n            local new_good_click_cate2cate_lag_min_list_extend = {}\n            local new_good_click_cate2cate_seller_id_lag_list_extend = {}\n            local new_good_click_cate2cate_cate1_lag_list_extend = {}\n            local new_good_click_cate2cate_cate2_lag_list_extend = {}\n            local new_good_click_cate2cate_cate3_lag_list_extend = {}\n            local new_good_click_cate2cate_seller_id_price_list_extend = {}\n            local new_good_click_cate2cate_cate1_price_list_extend = {}\n            local new_good_click_cate2cate_cate2_price_list_extend = {}\n            local new_good_click_cate2cate_cate3_price_list_extend = {}\n\n            local count = 0\n            local len = #good_click_cate2cate_item_id_list\n            for index=len,1,-1 do\n                table.insert(new_good_click_cate2cate_item_id_list_extend, good_click_cate2cate_item_id_list[index])\n                table.insert(new_good_click_cate2cate_seller_id_list_extend, good_click_cate2cate_seller_id_list[index])\n                table.insert(new_good_click_cate2cate_real_seller_id_list_extend, good_click_cate2cate_real_seller_id_list[index])\n                table.insert(new_good_click_cate2cate_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24))\n                table.insert(new_good_click_cate2cate_cate1_list_extend, good_click_cate2cate_cate1_list[index])\n                table.insert(new_good_click_cate2cate_cate2_list_extend, good_click_cate2cate_cate2_list[index])\n                table.insert(new_good_click_cate2cate_cate3_list_extend, good_click_cate2cate_cate3_list[index])\n                table.insert(new_good_click_cate2cate_category_list_extend, good_click_cate2cate_category_list[index])\n                table.insert(new_good_click_cate2cate_carry_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>16) & 0xff)\n                table.insert(new_good_click_cate2cate_click_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>24) & 0xff)\n                table.insert(new_good_click_cate2cate_click_from_list_extend, good_click_cate2cate_click_from_list[index])\n                table.insert(new_good_click_cate2cate_real_price_list_extend, cal_price(good_click_cate2cate_real_price_list[index]))\n                table.insert(new_good_click_cate2cate_index_list_extend, count)\n                table.insert(new_good_click_cate2cate_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600))\n                table.insert(new_good_click_cate2cate_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(60))\n                table.insert(new_good_click_cate2cate_seller_id_lag_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n                table.insert(new_good_click_cate2cate_cate1_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff,\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n                table.insert(new_good_click_cate2cate_cate2_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff,\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n                table.insert(new_good_click_cate2cate_cate3_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff,\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n                table.insert(new_good_click_cate2cate_seller_id_price_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], cal_price(good_click_cate2cate_real_price_list[index])))\n                table.insert(new_good_click_cate2cate_cate1_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n                table.insert(new_good_click_cate2cate_cate2_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n                table.insert(new_good_click_cate2cate_cate3_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n\n                count = count + 1\n                if count >= 100 then\n                    break\n                end\n            end\n\n            return new_good_click_cate2cate_item_id_list_extend, new_good_click_cate2cate_seller_id_list_extend, new_good_click_cate2cate_real_seller_id_list_extend,\n                new_good_click_cate2cate_lag_list_extend, new_good_click_cate2cate_cate1_list_extend, new_good_click_cate2cate_cate2_list_extend,\n                new_good_click_cate2cate_cate3_list_extend, new_good_click_cate2cate_category_list_extend, new_good_click_cate2cate_carry_type_list_extend,\n                new_good_click_cate2cate_click_type_list_extend, new_good_click_cate2cate_click_from_list_extend, new_good_click_cate2cate_real_price_list_extend,\n                new_good_click_cate2cate_index_list_extend, new_good_click_cate2cate_lag_hour_list_extend, new_good_click_cate2cate_lag_min_list_extend,\n                new_good_click_cate2cate_seller_id_lag_list_extend, new_good_click_cate2cate_cate1_lag_list_extend, new_good_click_cate2cate_cate2_lag_list_extend,\n                new_good_click_cate2cate_cate3_lag_list_extend, new_good_click_cate2cate_seller_id_price_list_extend, new_good_click_cate2cate_cate1_price_list_extend,\n                new_good_click_cate2cate_cate2_price_list_extend, new_good_click_cate2cate_cate3_price_list_extend\n        end",
           """)

a = [            {
              "from_common": "good_show_1011",
              "to_common": "colossus_rs_category_1_list"
            },
            {
              "from_common": "good_show_1012",
              "to_common": "colossus_rs_category_2_list"
            },
            {
              "from_common": "good_show_1013",
              "to_common": "colossus_rs_category_3_list"
            },
            {
              "from_common": "good_show_1014",
              "to_common": "colossus_rs_count_index_list"
            },
            {
              "from_common": "good_show_1015",
              "to_common": "colossus_rs_is_buy_list"
            },
            {
              "from_common": "good_show_1016",
              "to_common": "colossus_rs_is_click_list"
            },
            {
              "from_common": "good_show_1017",
              "to_common": "colossus_rs_item_id_list"
            },
            {
              "from_common": "good_show_1018",
              "to_common": "colossus_rs_lagV1_list"
            },
            {
              "from_common": "good_show_1019",
              "to_common": "colossus_rs_lagV2_list"
            },
            {
              "from_common": "good_show_1020",
              "to_common": "colossus_rs_pagecode_id_list"
            },
            {
              "from_common": "good_show_1021",
              "to_common": "colossus_rs_seller_id_list"
            },
            {
              "from_common": "good_show_1022",
              "to_common": "colossus_rs_uniform_spu_id_list"
            }
          ]
for d in a:

    print(d['from_common'], d['to_common'])

a = [
            {
              "from_item": "good_click_top100_840",
              "to_item": "good_click_item_id_list_softsearch"
            },
            {
              "from_item": "good_click_top100_841",
              "to_item": "good_click_seller_id_list_softsearch"
            },
            {
              "from_item": "good_click_top100_842",
              "to_item": "good_click_real_seller_id_list_softsearch"
            },
            {
              "from_item": "good_click_top100_843",
              "to_item": "good_click_lag_list_softsearch"
            },
            {
              "from_item": "good_click_top100_844",
              "to_item": "good_click_carry_type_list_softsearch"
            },
            {
              "from_item": "good_click_top100_845",
              "to_item": "good_click_click_type_list_softsearch"
            },
            {
              "from_item": "good_click_top100_846",
              "to_item": "good_click_category_list_softsearch"
            },
            {
              "from_item": "good_click_top100_848",
              "to_item": "good_click_from_list_softsearch"
            },
            {
              "from_item": "good_click_top100_848",
              "to_item": "good_click_price_list_softsearch"
            },
            {
              "from_item": "good_click_top100_849",
              "to_item": "good_detail_page_view_time_list_softsearch"
            },
            {
              "from_item": "good_click_top100_850",
              "to_item": "good_click_origin_price_list_softsearch"
            },
            {
              "from_item": "good_click_top100_851",
              "to_item": "good_click_label_list_softsearch"
            },
            {
              "from_item": "topk_indices",
              "to_item": "good_click_topk_indices_softsearch"
            },
            {
              "from_item": "topk_values",
              "to_item": "good_click_topk_values_softsearch"
            }
          ]
for i in a:
    print(f"column_name={i['to_item']}, compression_type=None, column_type=List<Long>")

t = ["good_click_cate2cate_cate1_list_extend",
            "good_click_cate2cate_cate2_list_extend",
            "good_click_cate2cate_cate3_list_extend",]
res = []
for i in t:
    res.append({'from_item': i, 'to_item':i.replace('_extend', '_extend_copy')})


res = []
for i in range(len(t)):
    res.append({'from_item': f"good_click_cate2cate_{895+i}", 'to_item':t[i].replace('_extend', '_extend_copy')})

res

t = ["good_click_item_id_list",
"good_click_seller_id_list",
"good_click_real_seller_id_list",
"good_click_timestamp_list",
"good_click_category_list",
"good_click_flow_type_list",
"good_click_from_list",
"good_click_price_list",
"good_detail_page_view_time_list",
"good_click_origin_price_list",
"good_click_label_list",]
res = []
for i in t:
    res.append({'from_common': i, 'to_common':i.replace('_list', '_list_reverse')})

res

t = [
    ("good_click_item_id_list_top100", 840, "good_click_item_id_list_top100"), 
    ("good_click_seller_id_list_top100", 841, "good_click_seller_id_list_top100"),
    ("good_click_real_seller_id_list_top100", 842, "good_click_real_seller_id_list_top100"),
    ("good_click_lag_list_top100", 843, "good_click_lag_list_top100"),
    ("good_click_carry_type_list_top100", 844, "good_click_carry_type_list_top100"),
    ("good_click_click_type_list_top100", 845, "good_click_click_type_list_top100"),
    ("good_click_category_list_top100", 846, "good_click_category_list_top100"),
    ("good_click_from_list_top100", 848, "good_click_from_list_top100"),
    ("good_click_price_list_top100", 848, "good_click_price_list_top100"),
    ("good_detail_page_view_time_list_top100", 849, "good_detail_page_view_time_list_top100"),
    ("good_click_origin_price_list_top100", 850, "good_click_origin_price_list_top100"),
    ("good_click_label_list_top100", 851, "good_click_label_list_top100"),
]
res = []
for a,b,c in t:
    res.append({'from_item': f"good_click_top100_{b}", 'to_item':c.replace('_top100', '_softsearch')})
res

def transformer_layer_splithead(self,layer_input, extra_query_inputs, user_complex_actions, invalid_mask_kv, trans_output=16, nh=8, topk=100, name=""):
        # layer_input: [batch_size, x]
        # user_complex_actions: [batch_size, join_limit, sum(dim_i)]
        with tf.variable_scope(name + "_layers", reuse=tf.AUTO_REUSE):
            if layer_input is None:
                layer_input = extra_query_inputs
            elif extra_query_inputs is not None:
                layer_input = tf.concat([tf.stop_gradient(layer_input), extra_query_inputs], axis=-1)
            else:
                layer_input = tf.stop_gradient(layer_input)
            rown = tf.shape(layer_input)[0]
            coln = layer_input.get_shape()[1]

            query_input = tf.reshape(layer_input, (rown, 1, coln))

            colm_kv = user_complex_actions.get_shape()[-1]
            sq_query = query_input.shape.as_list()[1]
            sq_value = user_complex_actions.shape.as_list()[1]

            Q = tf.get_variable('q_trans_matrix', (coln, trans_output * nh))
            K = tf.get_variable('k_trans_matrix', (colm_kv, trans_output * nh))
            V = tf.get_variable('v_trans_matrix', (colm_kv, trans_output * nh))
            attn_mask = invalid_mask_kv * (-1000.0)  # [batch_size, sq_len]
            
            # [batch_size, 1, dim * heads]
            querys = tf.tensordot(query_input, Q, axes=(-1, 0))
            querys_trans = tf.transpose(tf.reshape(querys, [-1, sq_query, nh, trans_output]), [0, 2, 1, 3])
            # [batch_size, 50, dim * heads]
            keys = tf.tensordot(user_complex_actions, K, axes=(-1, 0))
            keys_trans =  tf.transpose(tf.reshape(keys, [-1, sq_value, nh, trans_output]), [0, 2, 1, 3])
            # [batch_size, 50, dim * heads]
            values = tf.tensordot(user_complex_actions, V, axes=(-1, 0))
            values_trans = tf.transpose(tf.reshape(values, [-1, sq_value, nh, trans_output]), [0, 2, 1, 3])
            
            Q_mat_K_score = tf.matmul(querys_trans,keys_trans,transpose_b=True)  # (batch_size, nh, 1, sq_len)

            ####### mask
            attn_mask = tf.tile(tf.expand_dims(tf.expand_dims(attn_mask, 1),2), [1, 8, 1, 1] )

            Q_mat_K_score = Q_mat_K_score + attn_mask  # (h, b, l1, l)
            Q_mat_K_score = tf.clip_by_value(Q_mat_K_score, -10000.0, 10000.0)

            score = tf.reshape(Q_mat_K_score, [-1, nh, sq_value]) / 8.0

            _, h_idx = tf.math.top_k(score, topk)  # (bs, nh, topk)
            h_select_values = tf.gather(values_trans, h_idx, batch_dims=2)  # (bs, nh, topk,att_emb_size)

            inner_product = tf.gather(score, h_idx, batch_dims=2)  # (batch_size, nh, topk)
            normalized_att_scores = tf.nn.softmax(inner_product)  # (batch_size,nh,topk)
            normalized_att_scores = tf.reshape(normalized_att_scores, [rown, nh, 1, topk])

            result = tf.matmul(normalized_att_scores, h_select_values)  # (batch_size,nh,1, att_emb_size)
            mha_result = tf.reshape(result, (-1, nh * trans_output))

text = """class=ExtractItemGoodsBasicAttr, category=photo, size=10000001, field=354, slot=150, topic_id=0
class=ExtractItemGoodsGender, category=photo, size=10001, field=355, slot=151, topic_id=0
class=ExtractItemGoodsPeople, category=photo, size=5000001, field=356, slot=152, topic_id=0
class=ExtractItemGoodsSeason, category=photo, size=1001, field=357, slot=153, topic_id=0
class=ExtractItemGoodsAge, category=photo, size=50001, field=358, slot=154, topic_id=0
class=ExtractItemGoodsPlace, category=photo, size=100001, field=359, slot=155, topic_id=0
class=ExtractItemGoodsEnter, category=photo, size=11, field=360, slot=156, topic_id=0
class=ExtractItemGoodsPrice, category=photo, size=101, field=361, slot=157, topic_id=0
class=ExtractItemGoodsScene, category=photo, size=100001, field=362, slot=158, topic_id=0
class=ExtractItemGoodsBrand, category=photo, size=1000001, field=363, slot=159, topic_id=0
class=ExtractItemGoodsEffect, category=photo, size=1000001, field=364, slot=160, topic_id=0
class=ExtractItemGoodsStyle, category=photo, size=10000001, field=365, slot=161, topic_id=0
class=ExtractItemGoodsCloth, category=photo, size=1000001, field=366, slot=162, topic_id=0
class=ExtractItemGoodsMedical, category=photo, size=1000001, field=367, slot=163, topic_id=0
class=ExtractItemGoodsFood, category=photo, size=1000001, field=368, slot=164, topic_id=0
class=ExtractItemGoodsMakeup, category=photo, size=1000001, field=369, slot=165, topic_id=0
class=ExtractItemGoodsBuild, category=photo, size=1000001, field=370, slot=166, topic_id=0
class=ExtractItemGoodsConsume, category=photo, size=1000001, field=371, slot=167, topic_id=0""".splitlines()

a = [t.split(',')[0].split('=')[1].lower() for t in text]
# ', '.join(a)
len(a)

b = [729, 731, 632, 733, 735, 734, 126, 127, 35, 564]
[a[i] for i in b]