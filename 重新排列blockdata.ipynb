lines = """
good_show_hardsearch_count_index_list = block_data[102].output
good_show_hardsearch_exposure_ratio_list = block_data[103].output
good_show_hardsearch_item_id_list = block_data[104].output
good_show_hardsearch_lagV1_list = block_data[105].output
good_show_hardsearch_lagV2_list = block_data[106].output
good_show_hardsearch_pagecode_id_list = block_data[107].output
good_show_hardsearch_uniform_spu_id_list = block_data[108].output
""".strip().splitlines()

index = 92
for line in lines:
    # 替换idx为index
    import re
    # 查找block_data[数字]模式并替换为block_data[index]
    new_line = re.sub(r'block_data\[\d+\]', f'block_data[{index}]', line)
    print(new_line)
    index += 1


lines = """sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_<PERSON>_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,
good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,
colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,
eshop_ad, ue_score_sparse, has_uescore, ExtractUserDenseGrpoFea, grpo_sparse,
good_click_softsearch_item_id_list_top50, good_click_softsearch_seller_id_list_top50,
good_click_softsearch_lag_list_top50, good_click_softsearch_category_list_top50,
good_click_softsearch_carry_type_list_top50, good_click_softsearch_price_list_top50,
good_click_softsearch_item_count_list_top50, good_click_softsearch_topk_values, good_click_softsearch_topk_indices,
good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list,
good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list,
good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list""".replace('\n','').split(',')
index = 0
for line in lines:
    
    # 查找block_data[数字]模式并替换为block_data[index]
    new_line = f"{line.strip()} = block_data[{index}].output"
    print(new_line)
    index += 1


lines = """sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,
good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,
colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,
eshop_ad, ExtractUserDenseGrpoFea, grpo_sparse,
good_click_softsearch_item_id_list_top50, good_click_softsearch_seller_id_list_top50,
good_click_softsearch_lag_list_top50, good_click_softsearch_category_list_top50,
good_click_softsearch_carry_type_list_top50, good_click_softsearch_price_list_top50,
good_click_softsearch_item_count_list_top50, good_click_softsearch_topk_values, good_click_softsearch_topk_indices,
good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list,
good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list,
good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list""".replace('\n','').split(',')
index = 0
for line in lines:
    
    # 查找block_data[数字]模式并替换为block_data[index]
    new_line = f"{line.strip()} = block_data[{index}].output"
    print(new_line)
    index += 1